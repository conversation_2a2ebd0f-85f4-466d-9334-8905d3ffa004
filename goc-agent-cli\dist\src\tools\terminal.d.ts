/**
 * Terminal Manager Tool
 *
 * Implements read-terminal for terminal access and interaction
 */
import { ChildProcess } from 'child_process';
import { ToolResult } from './index';
export interface TerminalSession {
    id: string;
    shell: string;
    cwd: string;
    process: ChildProcess;
    output: string;
    isActive: boolean;
    createdAt: Date;
    lastActivity: Date;
}
export interface TerminalOptions {
    shell?: string;
    cwd?: string;
    env?: Record<string, string>;
    rows?: number;
    cols?: number;
}
export declare class TerminalManager {
    private terminals;
    private terminalCounter;
    /**
     * Create a new terminal session
     */
    createTerminal(options?: TerminalOptions): Promise<ToolResult>;
    /**
     * Read terminal output
     */
    readTerminal(terminalId?: string, options?: {
        onlySelected?: boolean;
        clear?: boolean;
    }): Promise<ToolResult>;
    /**
     * Write to terminal
     */
    writeTerminal(terminalId: string, command: string): Promise<ToolResult>;
    /**
     * Execute command in terminal and wait for output
     */
    executeCommand(terminalId: string, command: string, timeoutMs?: number): Promise<ToolResult>;
    /**
     * Close terminal
     */
    closeTerminal(terminalId: string): Promise<ToolResult>;
    /**
     * List all terminals
     */
    listTerminals(): ToolResult;
    /**
     * Get terminal info
     */
    getTerminalInfo(terminalId: string): ToolResult;
    /**
     * Get default shell based on platform
     */
    private getDefaultShell;
    /**
     * Cleanup inactive terminals
     */
    cleanup(): void;
}
//# sourceMappingURL=terminal.d.ts.map