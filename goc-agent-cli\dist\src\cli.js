#!/usr/bin/env node
"use strict";
/**
 * GOC Agent CLI
 *
 * Command-line interface powered by GOC Core
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const commander_1 = require("commander");
const chalk_1 = __importDefault(require("chalk"));
const boxen_1 = __importDefault(require("boxen"));
const ora_1 = __importDefault(require("ora"));
const core_1 = require("@goc-agent/core");
const chat_1 = require("./commands/chat");
const agent_1 = require("./commands/agent");
const config_1 = require("./commands/config");
const analyze_1 = require("./commands/analyze");
const train_1 = require("./commands/train");
const research_1 = require("./commands/research");
const auth_1 = require("./commands/auth");
const session_1 = require("./commands/session");
const tools_1 = require("./commands/tools");
const context_1 = require("./commands/context");
const web_1 = require("./commands/web");
const auto_1 = require("./commands/auto");
const generate_1 = require("./commands/generate");
const monitor_1 = require("./commands/monitor");
const docs_1 = require("./commands/docs");
const deploy_1 = require("./commands/deploy");
const models_1 = require("./commands/models");
const BackendAPIClient_1 = require("../services/BackendAPIClient");
class GocCLI {
    constructor() {
        this.core = null;
        this.configManager = null;
        this.program = new commander_1.Command();
        this.apiClient = new BackendAPIClient_1.BackendAPIClient();
        this.setupProgram();
    }
    setupProgram() {
        this.program
            .name('goc')
            .description('GOC Agent - AI-powered coding assistant')
            .version('1.0.0')
            .option('-v, --verbose', 'Enable verbose output')
            .option('-q, --quiet', 'Suppress non-essential output')
            .hook('preAction', async (thisCommand) => {
            await this.initialize(thisCommand.opts());
        });
        // Register commands
        this.registerCommands();
    }
    registerCommands() {
        // Register authentication command
        const authCommand = new auth_1.AuthCommand(this.apiClient);
        authCommand.registerCommands(this.program);
        // Register session management command
        const sessionCommand = new session_1.SessionCommand(this.apiClient);
        sessionCommand.register(this.program);
        // Register tools command
        const toolsCommand = new tools_1.ToolsCommand();
        toolsCommand.register(this.program);
        // Register context command
        const contextCommand = new context_1.ContextCommand(this.core);
        contextCommand.register(this.program);
        // Register web command
        const webCommand = new web_1.WebCommand(this.core);
        webCommand.register(this.program);
        // Register auto mode command
        const autoCommand = new auto_1.AutoCommand(this.core);
        autoCommand.register(this.program);
        // Register code generation command
        const generateCommand = new generate_1.GenerateCommand(this.core);
        generateCommand.register(this.program);
        // Register monitoring command
        const monitorCommand = new monitor_1.MonitorCommand(this.core);
        monitorCommand.register(this.program);
        // Register documentation command
        const docsCommand = new docs_1.DocsCommand(this.core);
        docsCommand.register(this.program);
        // Register deployment command
        const deployCommand = new deploy_1.DeployCommand(this.core);
        deployCommand.register(this.program);
        // Register other commands
        const chatCommand = new chat_1.ChatCommand(this.core, this.configManager, this.apiClient);
        chatCommand.register(this.program);
        const agentCommand = new agent_1.AgentCommand(this.core, this.configManager);
        agentCommand.register(this.program);
        const configCommand = new config_1.ConfigCommand();
        configCommand.register(this.program);
        const analyzeCommand = new analyze_1.AnalyzeCommand(this.core);
        analyzeCommand.register(this.program);
        const trainCommand = new train_1.TrainCommand(this.core);
        trainCommand.register(this.program);
        const researchCommand = new research_1.ResearchCommand(this.core);
        researchCommand.register(this.program);
        // Register models command
        const modelsCommand = new models_1.ModelsCommand();
        modelsCommand.register(this.program);
        // Status command
        this.program
            .command('status')
            .description('Show GOC Agent status')
            .action(async () => {
            await this.showStatus();
        });
    }
    async initialize(options) {
        try {
            // Configure logger
            core_1.logger.updateConfig({
                level: options.verbose ? 'debug' : options.quiet ? 'error' : 'info',
                enableConsole: true
            });
            // Show initialization message
            if (!options.quiet) {
                const spinner = (0, ora_1.default)('Initializing GOC Agent...').start();
                try {
                    // Initialize configuration
                    this.configManager = new core_1.ConfigManager();
                    await this.configManager.initialize();
                    // Initialize core
                    this.core = await (0, core_1.createGocCore)({
                        ai: {
                            defaultProvider: this.configManager.getConfig().ai.defaultProvider,
                            defaultModel: this.configManager.getConfig().ai.defaultModel
                        },
                        global: {
                            logLevel: options.verbose ? 'debug' : 'info'
                        }
                    });
                    spinner.succeed('GOC Agent initialized successfully');
                }
                catch (error) {
                    spinner.fail('Failed to initialize GOC Agent');
                    throw error;
                }
            }
            else {
                // Silent initialization
                this.configManager = new core_1.ConfigManager();
                await this.configManager.initialize();
                this.core = await (0, core_1.createGocCore)({
                    ai: {
                        defaultProvider: this.configManager.getConfig().ai.defaultProvider,
                        defaultModel: this.configManager.getConfig().ai.defaultModel
                    }
                });
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('Failed to initialize GOC Agent:'), error);
            process.exit(1);
        }
    }
    async showStatus() {
        if (!this.core || !this.configManager) {
            console.error(chalk_1.default.red('GOC Agent not initialized'));
            return;
        }
        const config = this.configManager.getConfig();
        const isInitialized = this.core.isInitialized();
        // Check backend connectivity
        const backendHealthy = await this.apiClient.healthCheck();
        const isAuthenticated = this.apiClient.isAuthenticated();
        const statusInfo = [
            ['Status', isInitialized ? chalk_1.default.green('Ready') : chalk_1.default.red('Not Ready')],
            ['Version', '1.0.0'],
            ['Provider', config.ai.defaultProvider],
            ['Model', config.ai.defaultModel],
            ['Backend', backendHealthy ? chalk_1.default.green('Connected') : chalk_1.default.yellow('Offline')],
            ['Authentication', isAuthenticated ? chalk_1.default.green('Authenticated') : chalk_1.default.red('Not Authenticated')],
            ['Config Path', '~/.goc-agent/config.yaml']
        ];
        console.log((0, boxen_1.default)(statusInfo.map(([key, value]) => `${chalk_1.default.bold(key)}: ${value}`).join('\n'), {
            title: 'GOC Agent Status',
            padding: 1,
            borderStyle: 'round',
            borderColor: 'blue'
        }));
        if (!backendHealthy) {
            console.log(chalk_1.default.yellow('\n⚠️  Backend is offline. Some features may not be available.'));
            console.log(chalk_1.default.dim('   Make sure the Laravel backend is running on http://localhost:8000'));
        }
        if (!isAuthenticated && backendHealthy) {
            console.log(chalk_1.default.blue('\n💡 Tip: Use "goc auth login" to authenticate and access cloud features.'));
        }
    }
    async run() {
        try {
            await this.program.parseAsync(process.argv);
        }
        catch (error) {
            core_1.logger.error('CLI execution failed', error);
            console.error(chalk_1.default.red('Error:'), error);
            process.exit(1);
        }
        finally {
            // Cleanup
            if (this.core) {
                await this.core.dispose();
            }
        }
    }
}
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    core_1.logger.error('Uncaught exception', error);
    console.error(chalk_1.default.red('Uncaught exception:'), error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    core_1.logger.error('Unhandled rejection', reason);
    console.error(chalk_1.default.red('Unhandled rejection:'), reason);
    process.exit(1);
});
// Run CLI
if (require.main === module) {
    const cli = new GocCLI();
    cli.run();
}
//# sourceMappingURL=cli.js.map