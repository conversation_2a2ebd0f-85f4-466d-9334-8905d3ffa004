/**
 * Status Bar Component - Bottom status information
 */

export class StatusBar {
  private cursorPosition = { line: 1, column: 1 };
  private currentFile = '';
  private gocStatus = 'Ready';

  public async initialize(): Promise<void> {
    console.log('Initializing Status Bar...');
    this.updateDisplay();
    console.log('Status Bar initialized');
  }

  public updateCursorPosition(line: number, column: number): void {
    this.cursorPosition = { line, column };
    this.updateCursorDisplay();
  }

  public updateFileStatus(filePath: string, isDirty: boolean): void {
    this.currentFile = filePath;
    this.updateFileDisplay(isDirty);
  }

  public updateGocStatus(status: string): void {
    this.gocStatus = status;
    this.updateGocDisplay();
  }

  private updateDisplay(): void {
    this.updateCursorDisplay();
    this.updateFileDisplay(false);
    this.updateGocDisplay();
  }

  private updateCursorDisplay(): void {
    const element = document.getElementById('cursor-position');
    if (element) {
      element.textContent = `Ln ${this.cursorPosition.line}, Col ${this.cursorPosition.column}`;
    }
  }

  private updateFileDisplay(isDirty: boolean): void {
    const encodingElement = document.getElementById('file-encoding');
    const languageElement = document.getElementById('language-mode');
    
    if (encodingElement) {
      encodingElement.textContent = 'UTF-8';
    }
    
    if (languageElement) {
      const language = this.getLanguageFromFile(this.currentFile);
      languageElement.textContent = language;
    }
  }

  private updateGocDisplay(): void {
    const element = document.getElementById('goc-status');
    if (element) {
      element.textContent = `GOC Agent: ${this.gocStatus}`;
    }
  }

  private getLanguageFromFile(filePath: string): string {
    if (!filePath) return 'Plain Text';
    
    const extension = filePath.split('.').pop()?.toLowerCase();
    const languageMap: { [key: string]: string } = {
      'js': 'JavaScript',
      'ts': 'TypeScript',
      'html': 'HTML',
      'css': 'CSS',
      'json': 'JSON',
      'md': 'Markdown',
      'py': 'Python',
      'php': 'PHP'
    };
    
    return languageMap[extension || ''] || 'Plain Text';
  }
}
