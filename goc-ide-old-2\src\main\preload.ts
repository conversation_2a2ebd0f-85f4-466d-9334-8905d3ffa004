/**
 * Preload Script - Secure bridge between main and renderer processes
 */

import { contextBridge, ipcRenderer } from 'electron';

// Define the API interface
interface ElectronAPI {
  // Window controls
  window: {
    minimize: () => void;
    maximize: () => void;
    close: () => void;
    isMaximized: () => Promise<boolean>;
  };

  // File system operations
  fs: {
    readFile: (filePath: string) => Promise<string>;
    writeFile: (filePath: string, content: string) => Promise<void>;
    readDirectory: (dirPath: string) => Promise<string[]>;
    createFile: (filePath: string) => Promise<void>;
    createDirectory: (dirPath: string) => Promise<void>;
    deleteFile: (filePath: string) => Promise<void>;
    deleteDirectory: (dirPath: string) => Promise<void>;
    watchDirectory: (dirPath: string) => Promise<void>;
    unwatchDirectory: (dirPath: string) => Promise<void>;
  };

  // GOC Agent operations
  goc: {
    chat: (message: string, context?: any) => Promise<any>;
    explainCode: (code: string, language: string) => Promise<string>;
    generateCode: (prompt: string, language: string) => Promise<string>;
    refactorCode: (code: string, instructions: string) => Promise<string>;
    fixCode: (code: string, error: string) => Promise<string>;
    getStatus: () => Promise<any>;
    setProvider: (provider: string, model: string) => Promise<void>;
  };

  // Terminal operations
  terminal: {
    create: (cwd?: string) => Promise<string>;
    write: (terminalId: string, data: string) => Promise<void>;
    resize: (terminalId: string, cols: number, rows: number) => Promise<void>;
    kill: (terminalId: string) => Promise<void>;
  };

  // Dialog operations
  dialog: {
    showOpenDialog: (options: any) => Promise<any>;
    showSaveDialog: (options: any) => Promise<any>;
    showMessageBox: (options: any) => Promise<any>;
  };

  // Event listeners
  on: (channel: string, callback: (...args: any[]) => void) => void;
  off: (channel: string, callback: (...args: any[]) => void) => void;
  once: (channel: string, callback: (...args: any[]) => void) => void;
}

// Expose the API to the renderer process
const electronAPI: ElectronAPI = {
  // Window controls
  window: {
    minimize: () => ipcRenderer.invoke('window-minimize'),
    maximize: () => ipcRenderer.invoke('window-maximize'),
    close: () => ipcRenderer.invoke('window-close'),
    isMaximized: () => ipcRenderer.invoke('window-is-maximized')
  },

  // File system operations
  fs: {
    readFile: (filePath: string) => ipcRenderer.invoke('fs-read-file', filePath),
    writeFile: (filePath: string, content: string) => 
      ipcRenderer.invoke('fs-write-file', filePath, content),
    readDirectory: (dirPath: string) => ipcRenderer.invoke('fs-read-directory', dirPath),
    createFile: (filePath: string) => ipcRenderer.invoke('fs-create-file', filePath),
    createDirectory: (dirPath: string) => ipcRenderer.invoke('fs-create-directory', dirPath),
    deleteFile: (filePath: string) => ipcRenderer.invoke('fs-delete-file', filePath),
    deleteDirectory: (dirPath: string) => ipcRenderer.invoke('fs-delete-directory', dirPath),
    watchDirectory: (dirPath: string) => ipcRenderer.invoke('fs-watch-directory', dirPath),
    unwatchDirectory: (dirPath: string) => ipcRenderer.invoke('fs-unwatch-directory', dirPath)
  },

  // GOC Agent operations
  goc: {
    chat: (message: string, context?: any) => 
      ipcRenderer.invoke('goc-chat', message, context),
    explainCode: (code: string, language: string) => 
      ipcRenderer.invoke('goc-explain-code', code, language),
    generateCode: (prompt: string, language: string) => 
      ipcRenderer.invoke('goc-generate-code', prompt, language),
    refactorCode: (code: string, instructions: string) => 
      ipcRenderer.invoke('goc-refactor-code', code, instructions),
    fixCode: (code: string, error: string) => 
      ipcRenderer.invoke('goc-fix-code', code, error),
    getStatus: () => ipcRenderer.invoke('goc-get-status'),
    setProvider: (provider: string, model: string) => 
      ipcRenderer.invoke('goc-set-provider', provider, model)
  },

  // Terminal operations
  terminal: {
    create: (cwd?: string) => ipcRenderer.invoke('terminal-create', cwd),
    write: (terminalId: string, data: string) => 
      ipcRenderer.invoke('terminal-write', terminalId, data),
    resize: (terminalId: string, cols: number, rows: number) => 
      ipcRenderer.invoke('terminal-resize', terminalId, cols, rows),
    kill: (terminalId: string) => ipcRenderer.invoke('terminal-kill', terminalId)
  },

  // Dialog operations
  dialog: {
    showOpenDialog: (options: any) => ipcRenderer.invoke('dialog-show-open', options),
    showSaveDialog: (options: any) => ipcRenderer.invoke('dialog-show-save', options),
    showMessageBox: (options: any) => ipcRenderer.invoke('dialog-show-message', options)
  },

  // Event listeners
  on: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.on(channel, (event, ...args) => callback(...args));
  },
  
  off: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.removeListener(channel, callback);
  },
  
  once: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.once(channel, (event, ...args) => callback(...args));
  }
};

// Expose the API
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// Type declaration for TypeScript
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
