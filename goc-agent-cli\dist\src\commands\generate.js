"use strict";
/**
 * Code Generation Command
 *
 * Intelligent code generation with pattern recognition and best practices
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenerateCommand = void 0;
const chalk_1 = __importDefault(require("chalk"));
const inquirer_1 = __importDefault(require("inquirer"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
class GenerateCommand {
    constructor(core) {
        this.core = core;
    }
    register(program) {
        const generateCmd = program
            .command('generate')
            .alias('gen')
            .description('Intelligent code generation');
        generateCmd
            .command('code <description>')
            .description('Generate code from description')
            .option('-l, --language <lang>', 'Programming language', 'javascript')
            .option('-t, --type <type>', 'Code type (function|class|component|module|test)', 'function')
            .option('-o, --output <file>', 'Output file path')
            .option('--style <style>', 'Code style (modern|traditional|functional)', 'modern')
            .option('--tests', 'Include test generation')
            .option('--docs', 'Include documentation')
            .option('--patterns', 'Follow design patterns')
            .action(async (description, options) => {
            await this.generateCode(description, options);
        });
        generateCmd
            .command('refactor <file>')
            .description('Refactor existing code')
            .option('-l, --language <lang>', 'Programming language (auto-detect if not specified)')
            .option('--modernize', 'Apply modern patterns')
            .option('--preserve-logic', 'Preserve existing logic', true)
            .option('-o, --output <file>', 'Output file path (overwrites original if not specified)')
            .action(async (file, options) => {
            await this.refactorCode(file, options);
        });
        generateCmd
            .command('from-description <description>')
            .description('Generate code from natural language description')
            .option('-l, --language <lang>', 'Programming language', 'javascript')
            .option('-o, --output <file>', 'Output file path')
            .action(async (description, options) => {
            await this.generateFromDescription(description, options);
        });
        generateCmd
            .command('templates')
            .description('List available code templates')
            .option('-l, --language <lang>', 'Filter by programming language')
            .action(async (options) => {
            await this.listTemplates(options);
        });
        generateCmd
            .command('interactive')
            .alias('i')
            .description('Interactive code generation')
            .action(async () => {
            await this.interactiveGeneration();
        });
    }
    async generateCode(description, options) {
        try {
            console.log(chalk_1.default.blue(`🔧 Generating ${options.type} in ${options.language}`));
            console.log(chalk_1.default.dim(`Description: "${description}"`));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            const codeGenerator = this.core.getCodeGenerator();
            const result = await codeGenerator.generateCode({
                type: options.type,
                language: options.language,
                description,
                context: {
                    projectType: this.detectProjectType(),
                    filePath: options.output
                },
                options: {
                    style: options.style,
                    includeTests: options.tests,
                    includeDocumentation: options.docs,
                    followPatterns: options.patterns
                }
            });
            console.log(chalk_1.default.blue('📝 Generated Code:'));
            console.log(chalk_1.default.dim('─'.repeat(40)));
            console.log(result.code);
            console.log(chalk_1.default.dim('─'.repeat(40)));
            console.log();
            console.log(chalk_1.default.blue('📖 Explanation:'));
            console.log(result.explanation);
            console.log();
            if (result.suggestions.length > 0) {
                console.log(chalk_1.default.blue('💡 Suggestions:'));
                result.suggestions.forEach((suggestion, index) => {
                    console.log(`${index + 1}. ${suggestion}`);
                });
                console.log();
            }
            if (result.patterns.length > 0) {
                console.log(chalk_1.default.blue('🎨 Patterns Used:'));
                console.log(result.patterns.join(', '));
                console.log();
            }
            console.log(chalk_1.default.blue('📊 Quality Analysis:'));
            console.log(`Score: ${result.quality.score}/100`);
            if (result.quality.issues.length > 0) {
                console.log(chalk_1.default.yellow('Issues:'));
                result.quality.issues.forEach(issue => console.log(`  • ${issue}`));
            }
            if (result.quality.improvements.length > 0) {
                console.log(chalk_1.default.blue('Improvements:'));
                result.quality.improvements.forEach(improvement => console.log(`  • ${improvement}`));
            }
            console.log();
            if (result.tests && options.tests) {
                console.log(chalk_1.default.blue('🧪 Generated Tests:'));
                console.log(chalk_1.default.dim('─'.repeat(40)));
                console.log(result.tests);
                console.log(chalk_1.default.dim('─'.repeat(40)));
                console.log();
            }
            if (result.documentation && options.docs) {
                console.log(chalk_1.default.blue('📚 Generated Documentation:'));
                console.log(chalk_1.default.dim('─'.repeat(40)));
                console.log(result.documentation);
                console.log(chalk_1.default.dim('─'.repeat(40)));
                console.log();
            }
            // Save to file if specified
            if (options.output) {
                await this.saveGeneratedCode(options.output, result, options);
                console.log(chalk_1.default.green(`✅ Code saved to: ${options.output}`));
            }
            else {
                const { save } = await inquirer_1.default.prompt([{
                        type: 'confirm',
                        name: 'save',
                        message: 'Save the generated code to a file?',
                        default: true
                    }]);
                if (save) {
                    const { filename } = await inquirer_1.default.prompt([{
                            type: 'input',
                            name: 'filename',
                            message: 'Enter filename:',
                            default: `generated.${this.getFileExtension(options.language)}`
                        }]);
                    await this.saveGeneratedCode(filename, result, options);
                    console.log(chalk_1.default.green(`✅ Code saved to: ${filename}`));
                }
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Code generation failed:'), error);
        }
    }
    async refactorCode(filePath, options) {
        try {
            console.log(chalk_1.default.blue(`🔄 Refactoring: ${filePath}`));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            // Read existing code
            if (!fs_1.default.existsSync(filePath)) {
                console.error(chalk_1.default.red(`❌ File not found: ${filePath}`));
                return;
            }
            const code = fs_1.default.readFileSync(filePath, 'utf-8');
            const language = options.language || this.detectLanguage(filePath);
            console.log(chalk_1.default.blue('📊 Original Code Analysis:'));
            console.log(`Language: ${language}`);
            console.log(`Size: ${code.length} characters`);
            console.log(`Lines: ${code.split('\n').length}`);
            console.log();
            const codeGenerator = this.core.getCodeGenerator();
            const result = await codeGenerator.refactorCode(code, {
                language,
                preserveLogic: options.preserveLogic,
                modernize: options.modernize
            });
            console.log(chalk_1.default.blue('🔧 Refactored Code:'));
            console.log(chalk_1.default.dim('─'.repeat(40)));
            console.log(result.code);
            console.log(chalk_1.default.dim('─'.repeat(40)));
            console.log();
            console.log(chalk_1.default.blue('📖 Refactoring Summary:'));
            console.log(result.explanation);
            console.log();
            if (result.suggestions.length > 0) {
                console.log(chalk_1.default.blue('✨ Improvements Made:'));
                result.suggestions.forEach((suggestion, index) => {
                    console.log(`${index + 1}. ${suggestion}`);
                });
                console.log();
            }
            console.log(chalk_1.default.blue('📊 Quality Comparison:'));
            console.log(`New Quality Score: ${result.quality.score}/100`);
            console.log();
            // Save refactored code
            const outputPath = options.output || filePath;
            const { confirm } = await inquirer_1.default.prompt([{
                    type: 'confirm',
                    name: 'confirm',
                    message: `Save refactored code to ${outputPath}?`,
                    default: true
                }]);
            if (confirm) {
                // Backup original if overwriting
                if (outputPath === filePath) {
                    const backupPath = `${filePath}.backup.${Date.now()}`;
                    fs_1.default.copyFileSync(filePath, backupPath);
                    console.log(chalk_1.default.dim(`Original backed up to: ${backupPath}`));
                }
                fs_1.default.writeFileSync(outputPath, result.code, 'utf-8');
                console.log(chalk_1.default.green(`✅ Refactored code saved to: ${outputPath}`));
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Code refactoring failed:'), error);
        }
    }
    async generateFromDescription(description, options) {
        try {
            console.log(chalk_1.default.blue(`🤖 Generating code from natural language`));
            console.log(chalk_1.default.dim(`Description: "${description}"`));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            const codeGenerator = this.core.getCodeGenerator();
            const result = await codeGenerator.generateFromDescription(description, options.language, {
                projectPath: process.cwd(),
                projectType: this.detectProjectType()
            });
            console.log(chalk_1.default.blue('🎯 Generated Code:'));
            console.log(chalk_1.default.dim('─'.repeat(40)));
            console.log(result.code);
            console.log(chalk_1.default.dim('─'.repeat(40)));
            console.log();
            console.log(chalk_1.default.blue('📖 Explanation:'));
            console.log(result.explanation);
            console.log();
            if (result.suggestions.length > 0) {
                console.log(chalk_1.default.blue('💡 Suggestions:'));
                result.suggestions.forEach((suggestion, index) => {
                    console.log(`${index + 1}. ${suggestion}`);
                });
                console.log();
            }
            // Save to file if specified
            if (options.output) {
                fs_1.default.writeFileSync(options.output, result.code, 'utf-8');
                console.log(chalk_1.default.green(`✅ Code saved to: ${options.output}`));
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Code generation from description failed:'), error);
        }
    }
    async listTemplates(options) {
        try {
            console.log(chalk_1.default.blue('📋 Available Code Templates'));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            const codeGenerator = this.core.getCodeGenerator();
            const templates = codeGenerator.getTemplates(options.language);
            if (templates.length === 0) {
                console.log(chalk_1.default.yellow('No templates available'));
                return;
            }
            // Group by language
            const groupedTemplates = templates.reduce((groups, template) => {
                const lang = template.language;
                if (!groups[lang])
                    groups[lang] = [];
                groups[lang].push(template);
                return groups;
            }, {});
            Object.entries(groupedTemplates).forEach(([language, langTemplates]) => {
                console.log(chalk_1.default.bold(`\n${language.toUpperCase()}:`));
                langTemplates.forEach((template, index) => {
                    console.log(`${index + 1}. ${chalk_1.default.bold(template.name)} (${template.category})`);
                    console.log(chalk_1.default.dim(`   ${template.description}`));
                    if (template.variables.length > 0) {
                        const vars = template.variables.map(v => v.name).join(', ');
                        console.log(chalk_1.default.dim(`   Variables: ${vars}`));
                    }
                });
            });
            console.log(chalk_1.default.dim('\nUse templates with the --type option in generate commands'));
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Failed to list templates:'), error);
        }
    }
    async interactiveGeneration() {
        try {
            console.log(chalk_1.default.blue('🎯 Interactive Code Generation'));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            const answers = await inquirer_1.default.prompt([
                {
                    type: 'input',
                    name: 'description',
                    message: 'Describe what you want to generate:',
                    validate: (input) => input.trim().length > 0 || 'Description cannot be empty'
                },
                {
                    type: 'list',
                    name: 'language',
                    message: 'Select programming language:',
                    choices: ['javascript', 'typescript', 'python', 'php', 'java', 'go', 'rust']
                },
                {
                    type: 'list',
                    name: 'type',
                    message: 'Select code type:',
                    choices: ['function', 'class', 'component', 'module', 'test']
                },
                {
                    type: 'list',
                    name: 'style',
                    message: 'Select code style:',
                    choices: ['modern', 'traditional', 'functional']
                },
                {
                    type: 'checkbox',
                    name: 'features',
                    message: 'Select additional features:',
                    choices: [
                        { name: 'Include tests', value: 'tests' },
                        { name: 'Include documentation', value: 'docs' },
                        { name: 'Follow design patterns', value: 'patterns' }
                    ]
                },
                {
                    type: 'input',
                    name: 'output',
                    message: 'Output file (optional):',
                    default: ''
                }
            ]);
            const options = {
                language: answers.language,
                type: answers.type,
                style: answers.style,
                tests: answers.features.includes('tests'),
                docs: answers.features.includes('docs'),
                patterns: answers.features.includes('patterns'),
                output: answers.output || undefined
            };
            await this.generateCode(answers.description, options);
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Interactive generation failed:'), error);
        }
    }
    // Helper methods
    async saveGeneratedCode(filePath, result, options) {
        // Ensure directory exists
        const dir = path_1.default.dirname(filePath);
        if (!fs_1.default.existsSync(dir)) {
            fs_1.default.mkdirSync(dir, { recursive: true });
        }
        let content = result.code;
        // Add documentation if generated
        if (result.documentation && options.docs) {
            content = result.documentation + '\n\n' + content;
        }
        fs_1.default.writeFileSync(filePath, content, 'utf-8');
        // Save tests separately if generated
        if (result.tests && options.tests) {
            const testPath = this.getTestFilePath(filePath, options.language);
            fs_1.default.writeFileSync(testPath, result.tests, 'utf-8');
            console.log(chalk_1.default.green(`✅ Tests saved to: ${testPath}`));
        }
    }
    getFileExtension(language) {
        const extensions = {
            javascript: 'js',
            typescript: 'ts',
            python: 'py',
            php: 'php',
            java: 'java',
            go: 'go',
            rust: 'rs',
            cpp: 'cpp',
            c: 'c'
        };
        return extensions[language] || 'txt';
    }
    getTestFilePath(originalPath, language) {
        const ext = path_1.default.extname(originalPath);
        const base = path_1.default.basename(originalPath, ext);
        const dir = path_1.default.dirname(originalPath);
        const testSuffix = language === 'python' ? '_test' : '.test';
        return path_1.default.join(dir, `${base}${testSuffix}${ext}`);
    }
    detectLanguage(filePath) {
        const ext = path_1.default.extname(filePath).toLowerCase();
        const languageMap = {
            '.js': 'javascript',
            '.ts': 'typescript',
            '.py': 'python',
            '.php': 'php',
            '.java': 'java',
            '.go': 'go',
            '.rs': 'rust',
            '.cpp': 'cpp',
            '.c': 'c'
        };
        return languageMap[ext] || 'javascript';
    }
    detectProjectType() {
        // Simple project type detection based on files in current directory
        const files = fs_1.default.readdirSync(process.cwd());
        if (files.includes('package.json'))
            return 'nodejs';
        if (files.includes('composer.json'))
            return 'php';
        if (files.includes('requirements.txt') || files.includes('setup.py'))
            return 'python';
        if (files.includes('pom.xml') || files.includes('build.gradle'))
            return 'java';
        if (files.includes('Cargo.toml'))
            return 'rust';
        if (files.includes('go.mod'))
            return 'go';
        return 'general';
    }
}
exports.GenerateCommand = GenerateCommand;
//# sourceMappingURL=generate.js.map