"use strict";
/**
 * Advanced File Operations Tool
 *
 * Implements str-replace-editor, view, save-file, remove-files with validation and backup
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileOperations = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const os = __importStar(require("os"));
class FileOperations {
    constructor() {
        this.changeHistory = [];
        this.backupDir = path.join(os.homedir(), '.goc-agent', 'backups');
        this.ensureBackupDir();
    }
    ensureBackupDir() {
        if (!fs.existsSync(this.backupDir)) {
            fs.mkdirSync(this.backupDir, { recursive: true });
        }
    }
    createBackup(filePath) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const fileName = path.basename(filePath);
        const backupPath = path.join(this.backupDir, `${fileName}.${timestamp}.backup`);
        if (fs.existsSync(filePath)) {
            fs.copyFileSync(filePath, backupPath);
        }
        return backupPath;
    }
    /**
     * View file content with optional range and search
     */
    async view(filePath, options = {}) {
        try {
            if (!fs.existsSync(filePath)) {
                return {
                    success: false,
                    error: `File not found: ${filePath}`
                };
            }
            const content = fs.readFileSync(filePath, 'utf8');
            const lines = content.split('\n');
            let result = lines;
            // Apply range filter
            if (options.range) {
                const start = Math.max(0, options.range.start - 1);
                const end = options.range.end === -1 ? lines.length : Math.min(lines.length, options.range.end);
                result = lines.slice(start, end);
            }
            // Apply regex search
            if (options.searchRegex) {
                const regex = new RegExp(options.searchRegex, options.caseSensitive ? 'g' : 'gi');
                const contextLines = options.contextLines || 5;
                const matchedLines = [];
                const matchedIndices = [];
                result.forEach((line, index) => {
                    if (regex.test(line)) {
                        matchedIndices.push(index);
                    }
                });
                // Build result with context
                const includedIndices = new Set();
                matchedIndices.forEach(index => {
                    for (let i = Math.max(0, index - contextLines); i <= Math.min(result.length - 1, index + contextLines); i++) {
                        includedIndices.add(i);
                    }
                });
                const sortedIndices = Array.from(includedIndices).sort((a, b) => a - b);
                result = sortedIndices.map(i => `${i + 1}: ${result[i]}`);
            }
            return {
                success: true,
                data: {
                    content: result.join('\n'),
                    lines: result.length,
                    totalLines: lines.length,
                    path: filePath
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * String replace editor with backup and validation
     */
    async strReplace(filePath, operations) {
        try {
            if (!fs.existsSync(filePath)) {
                return {
                    success: false,
                    error: `File not found: ${filePath}`
                };
            }
            // Create backup
            const backupPath = this.createBackup(filePath);
            const content = fs.readFileSync(filePath, 'utf8');
            const lines = content.split('\n');
            let modifiedLines = [...lines];
            let totalLinesAdded = 0;
            let totalLinesDeleted = 0;
            // Apply operations in reverse order to maintain line numbers
            const sortedOps = operations.sort((a, b) => (b.startLine || 0) - (a.startLine || 0));
            for (const op of sortedOps) {
                if (op.startLine && op.endLine) {
                    // Line-based replacement
                    const startIdx = op.startLine - 1;
                    const endIdx = op.endLine - 1;
                    if (startIdx < 0 || endIdx >= modifiedLines.length || startIdx > endIdx) {
                        return {
                            success: false,
                            error: `Invalid line range: ${op.startLine}-${op.endLine}`
                        };
                    }
                    const oldSection = modifiedLines.slice(startIdx, endIdx + 1).join('\n');
                    if (oldSection !== op.oldStr) {
                        return {
                            success: false,
                            error: `Content mismatch at lines ${op.startLine}-${op.endLine}`
                        };
                    }
                    const newLines = op.newStr.split('\n');
                    const deletedCount = endIdx - startIdx + 1;
                    const addedCount = newLines.length;
                    modifiedLines.splice(startIdx, deletedCount, ...newLines);
                    totalLinesDeleted += deletedCount;
                    totalLinesAdded += addedCount;
                }
                else {
                    // Content-based replacement
                    const fullContent = modifiedLines.join('\n');
                    const newContent = fullContent.replace(op.oldStr, op.newStr);
                    if (fullContent === newContent) {
                        return {
                            success: false,
                            error: `String not found: ${op.oldStr.substring(0, 50)}...`
                        };
                    }
                    modifiedLines = newContent.split('\n');
                    totalLinesAdded += newContent.split('\n').length - lines.length;
                }
            }
            // Write modified content
            fs.writeFileSync(filePath, modifiedLines.join('\n'), 'utf8');
            // Record change
            const change = {
                type: 'modify',
                path: filePath,
                linesAdded: totalLinesAdded,
                linesDeleted: totalLinesDeleted,
                backup: backupPath
            };
            this.changeHistory.push(change);
            return {
                success: true,
                data: {
                    linesAdded: totalLinesAdded,
                    linesDeleted: totalLinesDeleted,
                    backup: backupPath,
                    operations: operations.length
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Save new file with validation
     */
    async saveFile(filePath, content, options = {}) {
        try {
            if (fs.existsSync(filePath) && !options.overwrite) {
                return {
                    success: false,
                    error: `File already exists: ${filePath}. Use overwrite option to replace.`
                };
            }
            // Ensure directory exists
            const dir = path.dirname(filePath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
            // Create backup if file exists
            let backupPath;
            if (fs.existsSync(filePath)) {
                backupPath = this.createBackup(filePath);
            }
            fs.writeFileSync(filePath, content, 'utf8');
            const lines = content.split('\n').length;
            const change = {
                type: fs.existsSync(filePath) ? 'modify' : 'create',
                path: filePath,
                linesAdded: lines,
                backup: backupPath
            };
            this.changeHistory.push(change);
            return {
                success: true,
                data: {
                    path: filePath,
                    lines,
                    backup: backupPath,
                    created: !backupPath
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Remove files with undo capability
     */
    async removeFiles(filePaths) {
        try {
            const results = [];
            const backups = [];
            for (const filePath of filePaths) {
                if (!fs.existsSync(filePath)) {
                    results.push({ path: filePath, status: 'not_found' });
                    continue;
                }
                // Create backup
                const backupPath = this.createBackup(filePath);
                backups.push(backupPath);
                // Remove file
                fs.unlinkSync(filePath);
                const change = {
                    type: 'delete',
                    path: filePath,
                    backup: backupPath
                };
                this.changeHistory.push(change);
                results.push({ path: filePath, status: 'deleted', backup: backupPath });
            }
            return {
                success: true,
                data: {
                    results,
                    backups,
                    deletedCount: results.filter(r => r.status === 'deleted').length
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Get change history
     */
    getChangeHistory() {
        return [...this.changeHistory];
    }
    /**
     * Restore file from backup
     */
    async restoreFromBackup(backupPath, targetPath) {
        try {
            if (!fs.existsSync(backupPath)) {
                return {
                    success: false,
                    error: `Backup not found: ${backupPath}`
                };
            }
            const restorePath = targetPath || backupPath.replace(/\.[\d-T]+\.backup$/, '');
            fs.copyFileSync(backupPath, restorePath);
            return {
                success: true,
                data: {
                    restored: restorePath,
                    backup: backupPath
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
}
exports.FileOperations = FileOperations;
//# sourceMappingURL=file-operations.js.map