/**
 * GOC IDE - Main Styles
 * Professional dark theme inspired by VS Code
 */

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 13px;
  background-color: #1e1e1e;
  color: #cccccc;
  overflow: hidden;
}

/* Title Bar */
.titlebar {
  height: 30px;
  background-color: #323233;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #2d2d30;
  -webkit-app-region: drag;
  user-select: none;
}

.titlebar-drag-region {
  display: flex;
  align-items: center;
  height: 100%;
  flex: 1;
  padding-left: 8px;
}

.titlebar-icon {
  margin-right: 8px;
}

.titlebar-title {
  font-size: 12px;
  color: #cccccc;
}

.titlebar-controls {
  display: flex;
  height: 100%;
  -webkit-app-region: no-drag;
}

.titlebar-button {
  width: 46px;
  height: 30px;
  border: none;
  background: transparent;
  color: #cccccc;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.1s;
}

.titlebar-button:hover {
  background-color: #404040;
}

.titlebar-button.close:hover {
  background-color: #e81123;
  color: white;
}

/* Main Container */
.main-container {
  display: flex;
  height: calc(100vh - 30px - 22px); /* Subtract titlebar and status bar */
  background-color: #1e1e1e;
}

/* Activity Bar */
.activity-bar {
  width: 48px;
  background-color: #333333;
  border-right: 1px solid #2d2d30;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 8px 0;
}

.activity-bar-items,
.activity-bar-bottom {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.activity-item {
  width: 48px;
  height: 48px;
  border: none;
  background: transparent;
  color: #858585;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.1s;
  position: relative;
}

.activity-item:hover {
  color: #cccccc;
}

.activity-item.active {
  color: #ffffff;
}

.activity-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 16px;
  background-color: #007acc;
}

/* Sidebar */
.sidebar {
  width: 300px;
  background-color: #252526;
  border-right: 1px solid #2d2d30;
  display: flex;
  flex-direction: column;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
}

/* Panel Headers */
.panel-header {
  padding: 8px 16px;
  background-color: #2d2d30;
  border-bottom: 1px solid #3e3e42;
}

.panel-header h3 {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  color: #cccccc;
  letter-spacing: 0.5px;
}

/* Editor Area */
.editor-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
}

/* Tab Bar */
.tab-bar {
  height: 35px;
  background-color: #2d2d30;
  border-bottom: 1px solid #3e3e42;
  display: flex;
  overflow-x: auto;
  scrollbar-width: none;
}

.tab-bar::-webkit-scrollbar {
  display: none;
}

.tab {
  min-width: 120px;
  max-width: 200px;
  height: 35px;
  background-color: #2d2d30;
  border-right: 1px solid #3e3e42;
  display: flex;
  align-items: center;
  padding: 0 12px;
  cursor: pointer;
  position: relative;
  user-select: none;
}

.tab:hover {
  background-color: #323233;
}

.tab.active {
  background-color: #1e1e1e;
  border-bottom: 1px solid #007acc;
}

.tab-label {
  flex: 1;
  font-size: 13px;
  color: #cccccc;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tab.dirty .tab-label {
  font-style: italic;
}

.tab-close {
  width: 16px;
  height: 16px;
  border: none;
  background: transparent;
  color: #858585;
  cursor: pointer;
  border-radius: 2px;
  font-size: 16px;
  line-height: 1;
  margin-left: 4px;
  opacity: 0;
  transition: all 0.1s;
}

.tab:hover .tab-close {
  opacity: 1;
}

.tab-close:hover {
  background-color: #404040;
  color: #cccccc;
}

/* Editor Container */
.editor-container {
  flex: 1;
  position: relative;
  background-color: #1e1e1e;
}

.monaco-editor-container {
  width: 100%;
  height: 100%;
}

/* Welcome Screen */
.welcome-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #1e1e1e;
}

.welcome-content {
  text-align: center;
  max-width: 400px;
}

.welcome-content h1 {
  font-size: 32px;
  font-weight: 300;
  color: #cccccc;
  margin-bottom: 16px;
}

.welcome-content p {
  font-size: 16px;
  color: #858585;
  margin-bottom: 32px;
}

.welcome-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.welcome-button {
  padding: 12px 24px;
  border: 1px solid #007acc;
  background-color: transparent;
  color: #007acc;
  cursor: pointer;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.1s;
}

.welcome-button:hover {
  background-color: #007acc;
  color: white;
}

/* Panel Area */
.panel-area {
  height: 200px;
  background-color: #1e1e1e;
  border-top: 1px solid #2d2d30;
  display: flex;
  flex-direction: column;
}

.panel-tabs {
  height: 35px;
  background-color: #2d2d30;
  display: flex;
  border-bottom: 1px solid #3e3e42;
}

.panel-tab {
  padding: 0 16px;
  height: 35px;
  border: none;
  background: transparent;
  color: #858585;
  cursor: pointer;
  font-size: 13px;
  border-right: 1px solid #3e3e42;
  transition: all 0.1s;
}

.panel-tab:hover {
  color: #cccccc;
  background-color: #323233;
}

.panel-tab.active {
  color: #ffffff;
  background-color: #1e1e1e;
  border-bottom: 1px solid #007acc;
}

.panel-content {
  flex: 1;
  background-color: #1e1e1e;
  overflow-y: auto;
}

/* Status Bar */
.status-bar {
  height: 22px;
  background-color: #007acc;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  font-size: 12px;
}

.status-left,
.status-right {
  display: flex;
  gap: 16px;
}

.status-item {
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 2px;
  transition: background-color 0.1s;
}

.status-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Scrollbars */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: #1e1e1e;
}

::-webkit-scrollbar-thumb {
  background: #424242;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4f4f4f;
}

/* Error Message */
.error-message {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.error-content {
  background-color: #2d2d30;
  padding: 32px;
  border-radius: 8px;
  text-align: center;
  max-width: 400px;
}

.error-content h2 {
  color: #f48771;
  margin-bottom: 16px;
}

.error-content p {
  color: #cccccc;
  margin-bottom: 24px;
}

.error-content button {
  padding: 8px 16px;
  border: 1px solid #007acc;
  background-color: #007acc;
  color: white;
  cursor: pointer;
  border-radius: 4px;
}

/* File Explorer Styles */
.file-explorer {
  padding: 8px;
}

.file-tree {
  list-style: none;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  cursor: pointer;
  border-radius: 3px;
  font-size: 13px;
  color: #cccccc;
  user-select: none;
}

.file-item:hover {
  background-color: #2a2d2e;
}

.file-item.selected {
  background-color: #37373d;
}

.file-icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
  flex-shrink: 0;
}

.folder-icon {
  color: #dcb67a;
}

.file-icon.js { color: #f7df1e; }
.file-icon.ts { color: #3178c6; }
.file-icon.html { color: #e34c26; }
.file-icon.css { color: #1572b6; }
.file-icon.json { color: #cbcb41; }
.file-icon.md { color: #083fa1; }

/* GOC Agent Panel Styles */
.goc-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.goc-chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.goc-messages {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 16px;
  border: 1px solid #3e3e42;
  border-radius: 4px;
  padding: 8px;
  background-color: #1e1e1e;
}

.goc-message {
  margin-bottom: 12px;
  padding: 8px;
  border-radius: 4px;
}

.goc-message.user {
  background-color: #0e639c;
  color: white;
  margin-left: 20%;
}

.goc-message.assistant {
  background-color: #2d2d30;
  color: #cccccc;
  margin-right: 20%;
}

.goc-input-container {
  display: flex;
  gap: 8px;
}

.goc-input {
  flex: 1;
  padding: 8px;
  border: 1px solid #3e3e42;
  border-radius: 4px;
  background-color: #1e1e1e;
  color: #cccccc;
  font-size: 13px;
  resize: vertical;
  min-height: 36px;
  max-height: 120px;
}

.goc-send {
  padding: 8px 16px;
  border: 1px solid #007acc;
  background-color: #007acc;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
}

.goc-send:hover {
  background-color: #106ebe;
}

.goc-send:disabled {
  background-color: #3e3e42;
  border-color: #3e3e42;
  cursor: not-allowed;
}

/* Terminal Styles */
.terminal-container {
  height: 100%;
  background-color: #1e1e1e;
}

.xterm {
  height: 100%;
  padding: 8px;
}

/* Command Palette Styles */
.command-palette {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 100px;
}

.command-palette.hidden {
  display: none;
}

.command-palette-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.command-palette-container {
  position: relative;
  width: 600px;
  max-width: 90vw;
  background-color: #2d2d30;
  border: 1px solid #3e3e42;
  border-radius: 6px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.command-palette-input {
  width: 100%;
  padding: 16px;
  border: none;
  background: transparent;
  color: #cccccc;
  font-size: 16px;
  outline: none;
  border-bottom: 1px solid #3e3e42;
}

.command-palette-input::placeholder {
  color: #858585;
}

.command-palette-results {
  max-height: 400px;
  overflow-y: auto;
}

.command-palette-item {
  padding: 12px 16px;
  cursor: pointer;
  color: #cccccc;
  border-bottom: 1px solid #3e3e42;
  transition: background-color 0.1s;
}

.command-palette-item:hover {
  background-color: #37373d;
}

.command-palette-item:last-child {
  border-bottom: none;
}

/* Empty State Styles */
.empty-state {
  padding: 32px;
  text-align: center;
  color: #858585;
}

.empty-state p {
  margin-bottom: 16px;
  font-size: 14px;
}

/* Workspace Header */
.workspace-header {
  padding: 8px 16px;
  background-color: #2d2d30;
  border-bottom: 1px solid #3e3e42;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  color: #cccccc;
  letter-spacing: 0.5px;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 250px;
  }

  .panel-area {
    height: 150px;
  }

  .command-palette-container {
    width: 90vw;
  }
}
