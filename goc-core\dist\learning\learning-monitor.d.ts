/**
 * Learning Monitor
 *
 * Tracks learning progress, analytics, and provides monitoring capabilities
 * for the learning system
 */
import { LearningEvent, LearningMetrics, LearningPattern, LearningEventType, GocConfig } from '../types';
export interface MonitoringOptions {
    userId?: string;
    projectId?: string;
    timeRange?: {
        start: Date;
        end: Date;
    };
}
export interface LearningAnalytics {
    totalEvents: number;
    eventsByType: Record<LearningEventType, number>;
    learningRate: number;
    userSatisfaction: number;
    topPatterns: LearningPattern[];
    recentActivity: LearningEvent[];
    performanceMetrics: {
        averageConfidence: number;
        patternUtilization: number;
        learningEfficiency: number;
    };
    trends: {
        daily: Array<{
            date: string;
            events: number;
            patterns: number;
        }>;
        weekly: Array<{
            week: string;
            events: number;
            patterns: number;
        }>;
    };
}
export declare class LearningMonitor {
    private config;
    private knowledgeBase;
    private eventsFile;
    private metricsFile;
    private storageDir;
    private events;
    private initialized;
    constructor(config: GocConfig);
    initialize(): Promise<void>;
    /**
     * Record a learning event
     */
    recordEvent(event: LearningEvent): Promise<void>;
    /**
     * Get learning metrics
     */
    getMetrics(options?: MonitoringOptions): Promise<LearningMetrics>;
    /**
     * Get detailed analytics
     */
    getAnalytics(options?: MonitoringOptions): Promise<LearningAnalytics>;
    /**
     * Get learning progress over time
     */
    getLearningProgress(timeRange: {
        start: Date;
        end: Date;
    }, options?: MonitoringOptions): Promise<Array<{
        date: string;
        patternsLearned: number;
        eventsRecorded: number;
        averageConfidence: number;
    }>>;
    /**
     * Get user-specific learning insights
     */
    getUserInsights(userId: string): Promise<{
        learningVelocity: number;
        preferredPatterns: LearningPattern[];
        learningStrengths: string[];
        improvementAreas: string[];
        activitySummary: {
            totalSessions: number;
            averageSessionLength: number;
            mostActiveHours: number[];
        };
    }>;
    /**
     * Export learning data
     */
    exportData(options?: MonitoringOptions): Promise<{
        events: LearningEvent[];
        patterns: LearningPattern[];
        metrics: LearningMetrics;
        exportedAt: Date;
    }>;
    private filterEvents;
    private countActivePatterns;
    private groupEventsByType;
    private calculateLearningRate;
    private calculateUserSatisfaction;
    private calculatePerformanceMetrics;
    private calculateTrends;
    private calculateLearningVelocity;
    private identifyLearningStrengths;
    private identifyImprovementAreas;
    private calculateActivitySummary;
    private getWeekKey;
    private loadEvents;
    private saveEvents;
    private cleanupOldEvents;
    dispose(): Promise<void>;
}
//# sourceMappingURL=learning-monitor.d.ts.map