"use strict";
/**
 * Web Learner
 *
 * Automatically learns from web research results and integrates knowledge
 * into the learning system
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebLearner = void 0;
const knowledge_base_1 = require("./knowledge-base");
const utils_1 = require("../utils");
class WebLearner {
    constructor(config) {
        this.initialized = false;
        // Common programming patterns to look for
        this.PATTERN_KEYWORDS = {
            'best-practice': [
                'best practice', 'recommended', 'should use', 'avoid', 'dont',
                'performance', 'security', 'maintainable', 'clean code'
            ],
            'code-snippet': [
                'example', 'sample', 'code', 'implementation', 'function',
                'class', 'method', 'snippet', 'demo'
            ],
            'architecture-pattern': [
                'pattern', 'architecture', 'design', 'mvc', 'mvvm', 'repository',
                'factory', 'singleton', 'observer', 'strategy', 'microservices'
            ],
            'error-solution': [
                'error', 'fix', 'solution', 'resolve', 'debug', 'troubleshoot',
                'issue', 'problem', 'bug'
            ]
        };
        this.config = config;
        this.knowledgeBase = new knowledge_base_1.KnowledgeBase(config);
    }
    async initialize() {
        if (this.initialized)
            return;
        try {
            utils_1.logger.info('Initializing Web Learner', 'WebLearner');
            await this.knowledgeBase.initialize();
            this.initialized = true;
        }
        catch (error) {
            utils_1.logger.error('Failed to initialize Web Learner', 'WebLearner', { error });
            throw error;
        }
    }
    /**
     * Learn from web research results
     */
    async learnFromResults(query, results, relevantContent, context = {}) {
        if (!this.config.learning.enabled || !this.config.learning.enableWebLearning) {
            return {
                patternsLearned: 0,
                knowledgeExtracted: [],
                confidence: 0,
                sources: []
            };
        }
        try {
            const patterns = [];
            const knowledgeExtracted = [];
            const sources = [];
            // Process each piece of relevant content
            for (let i = 0; i < relevantContent.length && i < results.length; i++) {
                const content = relevantContent[i];
                const result = results[i];
                if (result?.url) {
                    sources.push(result.url);
                }
                // Extract patterns from content
                const extractedPatterns = await this.extractPatternsFromContent(content, query, context);
                patterns.push(...extractedPatterns);
                // Extract knowledge snippets
                const knowledge = this.extractKnowledge(content, query);
                knowledgeExtracted.push(...knowledge);
            }
            // Store learned patterns
            for (const pattern of patterns) {
                await this.knowledgeBase.storePattern(pattern);
            }
            const confidence = this.calculateLearningConfidence(patterns, query, relevantContent);
            utils_1.logger.debug(`Web learning completed: ${patterns.length} patterns learned`, 'WebLearner');
            return {
                patternsLearned: patterns.length,
                knowledgeExtracted,
                confidence,
                sources
            };
        }
        catch (error) {
            utils_1.logger.error('Failed to learn from web results', 'WebLearner', { error });
            return {
                patternsLearned: 0,
                knowledgeExtracted: [],
                confidence: 0,
                sources: []
            };
        }
    }
    /**
     * Learn from documentation content
     */
    async learnFromDocumentation(documentation, source, context = {}) {
        const patterns = [];
        try {
            // Extract API patterns
            const apiPatterns = this.extractApiPatterns(documentation, context);
            patterns.push(...apiPatterns);
            // Extract configuration patterns
            const configPatterns = this.extractConfigurationPatterns(documentation, context);
            patterns.push(...configPatterns);
            // Extract usage examples
            const examplePatterns = this.extractUsageExamples(documentation, context);
            patterns.push(...examplePatterns);
            // Store patterns
            for (const pattern of patterns) {
                pattern.metadata.source = source;
                pattern.metadata.sourceType = 'documentation';
                await this.knowledgeBase.storePattern(pattern);
            }
            return patterns;
        }
        catch (error) {
            utils_1.logger.error('Failed to learn from documentation', 'WebLearner', { error });
            return [];
        }
    }
    /**
     * Learn from code repositories
     */
    async learnFromRepository(repoContent, repoUrl, context = {}) {
        const patterns = [];
        try {
            for (const content of repoContent) {
                // Extract project structure patterns
                const structurePatterns = this.extractProjectStructure(content, context);
                patterns.push(...structurePatterns);
                // Extract coding patterns
                const codingPatterns = this.extractCodingPatterns(content, context);
                patterns.push(...codingPatterns);
                // Extract dependency patterns
                const dependencyPatterns = this.extractDependencyPatterns(content, context);
                patterns.push(...dependencyPatterns);
            }
            // Store patterns
            for (const pattern of patterns) {
                pattern.metadata.source = repoUrl;
                pattern.metadata.sourceType = 'repository';
                await this.knowledgeBase.storePattern(pattern);
            }
            return patterns;
        }
        catch (error) {
            utils_1.logger.error('Failed to learn from repository', 'WebLearner', { error });
            return [];
        }
    }
    // Private methods for pattern extraction
    async extractPatternsFromContent(content, query, context) {
        const patterns = [];
        // Determine pattern types based on content analysis
        for (const [patternType, keywords] of Object.entries(this.PATTERN_KEYWORDS)) {
            const relevance = this.calculateKeywordRelevance(content, keywords);
            if (relevance > 0.3) {
                const pattern = this.createWebPattern(patternType, content, query, relevance, context);
                if (pattern) {
                    patterns.push(pattern);
                }
            }
        }
        return patterns;
    }
    extractKnowledge(content, query) {
        const knowledge = [];
        // Extract sentences that contain key information
        const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);
        for (const sentence of sentences) {
            const trimmed = sentence.trim();
            // Look for informative sentences
            if (this.isInformativeSentence(trimmed, query)) {
                knowledge.push(trimmed);
            }
        }
        return knowledge.slice(0, 5); // Limit to top 5 pieces of knowledge
    }
    extractApiPatterns(documentation, context) {
        const patterns = [];
        // Look for API endpoint patterns
        const apiRegex = /(?:GET|POST|PUT|DELETE|PATCH)\s+([\/\w\-\{\}]+)/gi;
        const apiMatches = documentation.match(apiRegex);
        if (apiMatches && apiMatches.length > 0) {
            const pattern = this.createPattern({
                type: 'best-practice',
                content: `API endpoints: ${apiMatches.slice(0, 3).join(', ')}`,
                context: 'api-design',
                confidence: 0.7,
                tags: ['api', 'endpoints', 'rest'],
                metadata: {
                    endpoints: apiMatches,
                    framework: context.framework || 'unknown'
                }
            }, context);
            patterns.push(pattern);
        }
        return patterns;
    }
    extractConfigurationPatterns(documentation, context) {
        const patterns = [];
        // Look for configuration examples
        const configKeywords = ['config', 'configuration', 'settings', 'options', 'parameters'];
        const hasConfig = configKeywords.some(keyword => documentation.toLowerCase().includes(keyword));
        if (hasConfig) {
            // Extract JSON/YAML-like configurations
            const configBlocks = this.extractCodeBlocks(documentation);
            for (const block of configBlocks) {
                if (this.looksLikeConfiguration(block)) {
                    const pattern = this.createPattern({
                        type: 'code-snippet',
                        content: block,
                        context: 'configuration',
                        confidence: 0.6,
                        tags: ['config', 'setup', context.framework || 'general'],
                        metadata: {
                            configType: this.detectConfigType(block),
                            framework: context.framework
                        }
                    }, context);
                    patterns.push(pattern);
                }
            }
        }
        return patterns;
    }
    extractUsageExamples(documentation, context) {
        const patterns = [];
        const codeBlocks = this.extractCodeBlocks(documentation);
        for (const block of codeBlocks) {
            if (this.looksLikeUsageExample(block)) {
                const pattern = this.createPattern({
                    type: 'code-snippet',
                    content: block,
                    context: 'usage-example',
                    confidence: 0.8,
                    tags: ['example', 'usage', context.language || 'code'],
                    metadata: {
                        language: context.language || this.detectLanguage(block),
                        complexity: this.estimateComplexity(block)
                    }
                }, context);
                patterns.push(pattern);
            }
        }
        return patterns;
    }
    extractProjectStructure(content, context) {
        const patterns = [];
        // Look for directory structures
        const structureRegex = /(?:├──|└──|│\s+|\/)\s*([a-zA-Z0-9\-_\.]+)/g;
        const structureMatches = content.match(structureRegex);
        if (structureMatches && structureMatches.length > 3) {
            const pattern = this.createPattern({
                type: 'project-structure',
                content: structureMatches.slice(0, 10).join('\n'),
                context: 'directory-structure',
                confidence: 0.6,
                tags: ['structure', 'organization', context.framework || 'project'],
                metadata: {
                    fileCount: structureMatches.length,
                    framework: context.framework
                }
            }, context);
            patterns.push(pattern);
        }
        return patterns;
    }
    extractCodingPatterns(content, context) {
        const patterns = [];
        const codeBlocks = this.extractCodeBlocks(content);
        for (const block of codeBlocks) {
            // Look for common coding patterns
            if (this.containsCodingPattern(block)) {
                const patternType = this.identifyCodingPatternType(block);
                const pattern = this.createPattern({
                    type: patternType,
                    content: block,
                    context: 'coding-pattern',
                    confidence: 0.7,
                    tags: ['pattern', 'code', context.language || 'programming'],
                    metadata: {
                        language: context.language || this.detectLanguage(block),
                        patternType
                    }
                }, context);
                patterns.push(pattern);
            }
        }
        return patterns;
    }
    extractDependencyPatterns(content, context) {
        const patterns = [];
        // Look for package.json, requirements.txt, etc.
        const dependencyFiles = ['package.json', 'requirements.txt', 'composer.json', 'Gemfile'];
        for (const file of dependencyFiles) {
            if (content.includes(file)) {
                const pattern = this.createPattern({
                    type: 'dependency-usage',
                    content: `Uses ${file} for dependency management`,
                    context: 'dependency-management',
                    confidence: 0.8,
                    tags: ['dependencies', 'packages', this.getEcosystem(file)],
                    metadata: {
                        dependencyFile: file,
                        ecosystem: this.getEcosystem(file)
                    }
                }, context);
                patterns.push(pattern);
            }
        }
        return patterns;
    }
    // Helper methods
    calculateKeywordRelevance(content, keywords) {
        const lowerContent = content.toLowerCase();
        const matchCount = keywords.filter(keyword => lowerContent.includes(keyword.toLowerCase())).length;
        return matchCount / keywords.length;
    }
    calculateLearningConfidence(patterns, query, content) {
        if (patterns.length === 0)
            return 0;
        const avgPatternConfidence = patterns.reduce((sum, p) => sum + p.confidence, 0) / patterns.length;
        const contentQuality = this.assessContentQuality(content);
        const queryRelevance = this.assessQueryRelevance(query, content);
        return (avgPatternConfidence * 0.5 + contentQuality * 0.3 + queryRelevance * 0.2);
    }
    isInformativeSentence(sentence, query) {
        const queryWords = query.toLowerCase().split(/\s+/);
        const sentenceLower = sentence.toLowerCase();
        // Check if sentence contains query terms
        const hasQueryTerms = queryWords.some(word => sentenceLower.includes(word));
        // Check for informative indicators
        const informativeIndicators = [
            'should', 'must', 'recommended', 'best', 'avoid', 'use', 'implement',
            'example', 'method', 'function', 'class', 'pattern'
        ];
        const hasIndicators = informativeIndicators.some(indicator => sentenceLower.includes(indicator));
        return hasQueryTerms && hasIndicators && sentence.length > 30;
    }
    createWebPattern(type, content, query, confidence, context) {
        // Extract relevant portion of content
        const relevantContent = this.extractRelevantContent(content, query);
        if (!relevantContent || relevantContent.length < 10)
            return null;
        return this.createPattern({
            type,
            content: relevantContent,
            context: 'web-research',
            confidence,
            tags: ['web-learned', query.toLowerCase().replace(/\s+/g, '-')],
            metadata: {
                query,
                source: 'web-research',
                learnedAt: new Date().toISOString()
            }
        }, context);
    }
    createPattern(data, context) {
        return {
            id: this.generatePatternId(),
            frequency: 1,
            createdAt: new Date(),
            lastUsed: new Date(),
            userId: context.userId,
            projectId: context.projectId,
            ...data
        };
    }
    extractRelevantContent(content, query) {
        const sentences = content.split(/[.!?]+/);
        const queryWords = query.toLowerCase().split(/\s+/);
        // Find sentences that contain query terms
        const relevantSentences = sentences.filter(sentence => {
            const sentenceLower = sentence.toLowerCase();
            return queryWords.some(word => sentenceLower.includes(word));
        });
        return relevantSentences.slice(0, 3).join('. ').trim();
    }
    extractCodeBlocks(content) {
        const codeBlockRegex = /```[\s\S]*?```|`[^`]+`/g;
        const matches = content.match(codeBlockRegex) || [];
        return matches.map(block => block.replace(/```\w*\n?/g, '').replace(/```$/g, '').replace(/`/g, ''));
    }
    looksLikeConfiguration(block) {
        const configIndicators = ['{', '}', ':', '=', 'config', 'settings', 'options'];
        return configIndicators.some(indicator => block.includes(indicator));
    }
    looksLikeUsageExample(block) {
        const exampleIndicators = ['function', 'class', 'import', 'require', '()', '=>'];
        return exampleIndicators.some(indicator => block.includes(indicator));
    }
    detectConfigType(block) {
        if (block.includes('{') && block.includes('}'))
            return 'json';
        if (block.includes(':') && !block.includes('{'))
            return 'yaml';
        if (block.includes('='))
            return 'properties';
        return 'unknown';
    }
    detectLanguage(block) {
        if (block.includes('function') || block.includes('=>'))
            return 'javascript';
        if (block.includes('def ') || block.includes('import '))
            return 'python';
        if (block.includes('<?php'))
            return 'php';
        if (block.includes('class ') && block.includes('{'))
            return 'java';
        return 'unknown';
    }
    estimateComplexity(block) {
        const lines = block.split('\n').length;
        const brackets = (block.match(/[{}()]/g) || []).length;
        return Math.min(10, Math.floor((lines + brackets) / 5));
    }
    containsCodingPattern(block) {
        const patterns = ['class', 'function', 'interface', 'extends', 'implements', 'import'];
        return patterns.some(pattern => block.includes(pattern));
    }
    identifyCodingPatternType(block) {
        if (block.includes('class'))
            return 'architecture-pattern';
        if (block.includes('function'))
            return 'code-snippet';
        if (block.includes('import'))
            return 'dependency-usage';
        return 'best-practice';
    }
    getEcosystem(file) {
        const ecosystems = {
            'package.json': 'npm',
            'requirements.txt': 'pip',
            'composer.json': 'composer',
            'Gemfile': 'gem'
        };
        return ecosystems[file] || 'unknown';
    }
    assessContentQuality(content) {
        const totalLength = content.reduce((sum, c) => sum + c.length, 0);
        const avgLength = totalLength / content.length;
        // Quality based on content length and structure
        if (avgLength > 500)
            return 0.9;
        if (avgLength > 200)
            return 0.7;
        if (avgLength > 100)
            return 0.5;
        return 0.3;
    }
    assessQueryRelevance(query, content) {
        const queryWords = query.toLowerCase().split(/\s+/);
        const allContent = content.join(' ').toLowerCase();
        const matchCount = queryWords.filter(word => allContent.includes(word)).length;
        return matchCount / queryWords.length;
    }
    generatePatternId() {
        return `web_pattern_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    async dispose() {
        if (this.knowledgeBase) {
            await this.knowledgeBase.dispose();
        }
        this.initialized = false;
    }
}
exports.WebLearner = WebLearner;
//# sourceMappingURL=web-learner.js.map