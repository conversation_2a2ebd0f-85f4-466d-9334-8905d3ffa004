{"name": "goc-agent-cli", "version": "1.0.0", "description": "GOC Agent CLI - AI-powered coding assistant with multi-provider support", "main": "dist/cli.js", "bin": {"goc": "goc.js"}, "scripts": {"build": "tsc", "start": "node dist/cli.js", "dev": "npm run build && npm run start", "clean": "<PERSON><PERSON><PERSON> dist", "train": "node ../scripts/continuous-training.js", "train:daemon": "node ../scripts/training-daemon.js start", "train:stop": "node ../scripts/training-daemon.js stop", "train:status": "node ../scripts/training-daemon.js status"}, "keywords": ["ai", "coding", "agent", "ollama", "groq", "gemini", "chatgpt", "cli"], "author": "GOC Agent Team", "license": "MIT", "dependencies": {"@goc-agent/core": "file:../goc-core", "axios": "^1.10.0", "boxen": "^7.1.1", "chalk": "^5.3.0", "cli-table3": "^0.6.3", "commander": "^11.1.0", "fs-extra": "^11.3.0", "inquirer": "^9.2.12", "ora": "^7.0.1", "yaml": "^2.8.0"}, "devDependencies": {"@types/babel__parser": "^7.0.0", "@types/babel__traverse": "^7.20.7", "@types/cheerio": "^0.22.35", "@types/diff": "^5.0.8", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/mime-types": "^2.1.4", "@types/node": "^20.10.0", "rimraf": "^5.0.5", "ts-node": "^10.9.1", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}, "type": "commonjs", "files": ["dist/**/*", "goc.js", "config.example.yaml", "README.md"]}