/**
 * Backend API Client - Full Implementation
 *
 * Complete implementation for GOC Agent <PERSON><PERSON> backend integration
 * with authentication, session management, and API communication.
 */
export interface AuthResponse {
    success: boolean;
    token?: string;
    user?: any;
    message?: string;
}
export interface ChatRequest {
    session_id: string;
    message: string;
    provider?: string;
    model?: string;
}
export interface ChatResponse {
    user_message: any;
    assistant_message: any;
    session: any;
}
export interface SessionResponse {
    id: string;
    title: string;
    status: string;
    provider?: string;
    model?: string;
    created_at: string;
    updated_at: string;
}
export interface ProviderResponse {
    providers: {
        [key: string]: {
            name: string;
            available: boolean;
            models: string[];
        };
    };
}
export interface ApiKeyResponse {
    id: string;
    name: string;
    key: string;
    is_active: boolean;
    created_at: string;
    last_used_at?: string;
}
export interface UserProfile {
    id: string;
    name: string;
    email: string;
    subscription?: {
        plan: string;
        status: string;
        api_requests_remaining: number;
    };
}
export declare class BackendAPIClient {
    private client;
    private baseURL;
    private token;
    private configPath;
    constructor(baseURL?: string);
    private loadToken;
    private saveToken;
    private clearToken;
    isAuthenticated(): boolean;
    register(name: string, email: string, password: string): Promise<AuthResponse>;
    login(email: string, password: string): Promise<AuthResponse>;
    logout(): Promise<void>;
    getCurrentUser(): Promise<UserProfile>;
    createSession(title?: string): Promise<SessionResponse>;
    getSessions(): Promise<SessionResponse[]>;
    getSession(sessionId: string): Promise<SessionResponse>;
    updateSession(sessionId: string, updates: Partial<SessionResponse>): Promise<SessionResponse>;
    deleteSession(sessionId: string): Promise<void>;
    chat(request: ChatRequest): Promise<ChatResponse>;
    executeTask(task: string, sessionId?: string, provider?: string, model?: string, autoMode?: boolean): Promise<any>;
    getProviders(): Promise<ProviderResponse>;
    getStatus(): Promise<any>;
    executeTool(tool: string, parameters?: any, sessionId?: string): Promise<any>;
    healthCheck(): Promise<boolean>;
    getApiKeys(): Promise<ApiKeyResponse[]>;
    createApiKey(name: string): Promise<ApiKeyResponse>;
    deleteApiKey(keyId: string): Promise<void>;
    indexProject(projectPath: string): Promise<any>;
    searchCode(query: string, projectPath?: string): Promise<any>;
    getProjectStats(projectPath?: string): Promise<any>;
    recordLearningEvent(event: any): Promise<void>;
    getLearningMetrics(): Promise<any>;
    getLearningAnalytics(): Promise<any>;
    setBaseURL(url: string): void;
    getBaseURL(): string;
    getToken(): string | null;
}
//# sourceMappingURL=BackendAPIClient.d.ts.map