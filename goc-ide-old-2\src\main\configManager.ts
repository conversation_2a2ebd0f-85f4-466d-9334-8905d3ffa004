/**
 * Configuration Manager - Handles app configuration and settings
 */

import * as path from 'path';
import * as fs from 'fs';
import { app } from 'electron';

interface IDEConfig {
  defaultAiProvider: string;
  defaultAiModel: string;
  logLevel: string;
  theme: string;
  fontSize: number;
  autoSave: boolean;
  wordWrap: boolean;
  minimap: boolean;
}

export class ConfigManager {
  private configPath: string;
  private config: IDEConfig;

  constructor() {
    this.configPath = path.join(app.getPath('userData'), 'config.json');
    this.config = this.getDefaultConfig();
  }

  private getDefaultConfig(): IDEConfig {
    return {
      defaultAiProvider: 'groq',
      defaultAiModel: 'llama-3.1-70b-versatile',
      logLevel: 'info',
      theme: 'dark',
      fontSize: 14,
      autoSave: false,
      wordWrap: true,
      minimap: true
    };
  }

  public async getConfig(): Promise<IDEConfig> {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf8');
        this.config = { ...this.getDefaultConfig(), ...JSON.parse(configData) };
      }
    } catch (error) {
      console.error('Failed to load config:', error);
      this.config = this.getDefaultConfig();
    }

    return this.config;
  }

  public async updateConfig(updates: Partial<IDEConfig>): Promise<void> {
    try {
      this.config = { ...this.config, ...updates };
      
      // Ensure config directory exists
      const configDir = path.dirname(this.configPath);
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }

      fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
    } catch (error) {
      console.error('Failed to save config:', error);
      throw error;
    }
  }

  public getConfigValue<K extends keyof IDEConfig>(key: K): IDEConfig[K] {
    return this.config[key];
  }

  public async setConfigValue<K extends keyof IDEConfig>(key: K, value: IDEConfig[K]): Promise<void> {
    await this.updateConfig({ [key]: value } as Partial<IDEConfig>);
  }
}
