"use strict";
/**
 * Authentication Command
 *
 * Handle user authentication with GOC Agent backend
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthCommand = void 0;
const chalk_1 = __importDefault(require("chalk"));
const inquirer_1 = __importDefault(require("inquirer"));
class AuthCommand {
    constructor(apiClient) {
        this.apiClient = apiClient;
    }
    registerCommands(program) {
        const authCmd = program
            .command('auth')
            .description('Authentication commands');
        authCmd
            .command('login')
            .description('Login to GOC Agent backend')
            .option('-e, --email <email>', 'Email address')
            .option('-p, --password <password>', 'Password')
            .action(async (options) => {
            await this.login(options);
        });
        authCmd
            .command('register')
            .description('Register a new account')
            .option('-n, --name <name>', 'Full name')
            .option('-e, --email <email>', 'Email address')
            .option('-p, --password <password>', 'Password')
            .action(async (options) => {
            await this.register(options);
        });
        authCmd
            .command('logout')
            .description('Logout from GOC Agent backend')
            .action(async () => {
            await this.logout();
        });
        authCmd
            .command('status')
            .description('Check authentication status')
            .action(async () => {
            await this.status();
        });
        authCmd
            .command('profile')
            .description('View user profile')
            .action(async () => {
            await this.profile();
        });
    }
    async login(options) {
        console.log(chalk_1.default.blue('🔐 Login to GOC Agent'));
        try {
            let email = options.email;
            let password = options.password;
            // Prompt for missing credentials
            if (!email || !password) {
                const answers = await inquirer_1.default.prompt([
                    {
                        type: 'input',
                        name: 'email',
                        message: 'Email:',
                        when: !email,
                        validate: (input) => {
                            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                            return emailRegex.test(input) || 'Please enter a valid email address';
                        }
                    },
                    {
                        type: 'password',
                        name: 'password',
                        message: 'Password:',
                        when: !password,
                        validate: (input) => input.length >= 6 || 'Password must be at least 6 characters'
                    }
                ]);
                email = email || answers.email;
                password = password || answers.password;
            }
            console.log(chalk_1.default.dim('Authenticating...'));
            const response = await this.apiClient.login(email, password);
            if (response.success) {
                console.log(chalk_1.default.green('✅ Login successful!'));
                if (response.user) {
                    console.log(chalk_1.default.dim(`Welcome back, ${response.user.name}!`));
                }
            }
            else {
                console.log(chalk_1.default.red('❌ Login failed:'), response.message);
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Login error:'), error);
        }
    }
    async register(options) {
        console.log(chalk_1.default.blue('📝 Register for GOC Agent'));
        try {
            let name = options.name;
            let email = options.email;
            let password = options.password;
            // Prompt for missing information
            if (!name || !email || !password) {
                const answers = await inquirer_1.default.prompt([
                    {
                        type: 'input',
                        name: 'name',
                        message: 'Full Name:',
                        when: !name,
                        validate: (input) => input.trim().length >= 2 || 'Name must be at least 2 characters'
                    },
                    {
                        type: 'input',
                        name: 'email',
                        message: 'Email:',
                        when: !email,
                        validate: (input) => {
                            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                            return emailRegex.test(input) || 'Please enter a valid email address';
                        }
                    },
                    {
                        type: 'password',
                        name: 'password',
                        message: 'Password:',
                        when: !password,
                        validate: (input) => input.length >= 6 || 'Password must be at least 6 characters'
                    },
                    {
                        type: 'password',
                        name: 'confirmPassword',
                        message: 'Confirm Password:',
                        when: !password,
                        validate: (input, answers) => {
                            const pwd = password || answers?.password;
                            return input === pwd || 'Passwords do not match';
                        }
                    }
                ]);
                name = name || answers.name;
                email = email || answers.email;
                password = password || answers.password;
            }
            console.log(chalk_1.default.dim('Creating account...'));
            const response = await this.apiClient.register(name, email, password);
            if (response.success) {
                console.log(chalk_1.default.green('✅ Registration successful!'));
                console.log(chalk_1.default.dim(`Welcome to GOC Agent, ${response.user?.name || name}!`));
                console.log(chalk_1.default.dim('You are now logged in and ready to use GOC Agent.'));
            }
            else {
                console.log(chalk_1.default.red('❌ Registration failed:'), response.message);
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Registration error:'), error);
        }
    }
    async logout() {
        try {
            if (!this.apiClient.isAuthenticated()) {
                console.log(chalk_1.default.yellow('You are not logged in.'));
                return;
            }
            console.log(chalk_1.default.dim('Logging out...'));
            await this.apiClient.logout();
            console.log(chalk_1.default.green('✅ Logged out successfully!'));
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Logout error:'), error);
        }
    }
    async status() {
        try {
            if (this.apiClient.isAuthenticated()) {
                console.log(chalk_1.default.green('✅ Authenticated'));
                try {
                    const user = await this.apiClient.getCurrentUser();
                    console.log(chalk_1.default.dim(`Logged in as: ${user.name} (${user.email})`));
                    if (user.subscription) {
                        console.log(chalk_1.default.dim(`Plan: ${user.subscription.plan} (${user.subscription.status})`));
                        console.log(chalk_1.default.dim(`API Requests Remaining: ${user.subscription.api_requests_remaining}`));
                    }
                }
                catch (error) {
                    console.log(chalk_1.default.yellow('⚠️ Could not fetch user details'));
                }
            }
            else {
                console.log(chalk_1.default.red('❌ Not authenticated'));
                console.log(chalk_1.default.dim('Use "goc auth login" to authenticate'));
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Status check error:'), error);
        }
    }
    async profile() {
        try {
            if (!this.apiClient.isAuthenticated()) {
                console.log(chalk_1.default.red('❌ Not authenticated'));
                console.log(chalk_1.default.dim('Use "goc auth login" to authenticate'));
                return;
            }
            console.log(chalk_1.default.dim('Fetching profile...'));
            const user = await this.apiClient.getCurrentUser();
            console.log(chalk_1.default.blue('👤 User Profile'));
            console.log(chalk_1.default.dim('─'.repeat(40)));
            console.log(`Name: ${user.name}`);
            console.log(`Email: ${user.email}`);
            console.log(`ID: ${user.id}`);
            if (user.subscription) {
                console.log(chalk_1.default.blue('\n💳 Subscription'));
                console.log(chalk_1.default.dim('─'.repeat(40)));
                console.log(`Plan: ${user.subscription.plan}`);
                console.log(`Status: ${user.subscription.status}`);
                console.log(`API Requests Remaining: ${user.subscription.api_requests_remaining}`);
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Profile error:'), error);
        }
    }
}
exports.AuthCommand = AuthCommand;
//# sourceMappingURL=auth.js.map