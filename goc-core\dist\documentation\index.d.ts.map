{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../src/documentation/index.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,EAAE,aAAa,EAAE,MAAM,YAAY,CAAC;AAE3C,MAAM,WAAW,oBAAoB;IACnC,MAAM,EAAE,UAAU,GAAG,MAAM,GAAG,KAAK,GAAG,MAAM,CAAC;IAC7C,eAAe,EAAE,OAAO,CAAC;IACzB,UAAU,EAAE,OAAO,CAAC;IACpB,mBAAmB,EAAE,OAAO,CAAC;IAC7B,iBAAiB,EAAE,OAAO,CAAC;IAC3B,cAAc,EAAE,OAAO,CAAC;IACxB,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,oBAAoB;IACnC,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,MAAM,CAAC;IAChB,KAAK,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,UAAU,GAAG,KAAK,GAAG,SAAS,GAAG,cAAc,GAAG,YAAY,GAAG,SAAS,GAAG,QAAQ,CAAC;IAC5F,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAChC;AAED,MAAM,WAAW,gBAAgB;IAC/B,SAAS,EAAE,KAAK,CAAC;QACf,IAAI,EAAE,MAAM,CAAC;QACb,WAAW,EAAE,MAAM,CAAC;QACpB,UAAU,EAAE,KAAK,CAAC;YAChB,IAAI,EAAE,MAAM,CAAC;YACb,IAAI,EAAE,MAAM,CAAC;YACb,WAAW,EAAE,MAAM,CAAC;YACpB,QAAQ,EAAE,OAAO,CAAC;YAClB,OAAO,CAAC,EAAE,GAAG,CAAC;SACf,CAAC,CAAC;QACH,OAAO,EAAE;YACP,IAAI,EAAE,MAAM,CAAC;YACb,WAAW,EAAE,MAAM,CAAC;SACrB,CAAC;QACF,QAAQ,EAAE,MAAM,EAAE,CAAC;QACnB,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC;KACnB,CAAC,CAAC;IACH,OAAO,EAAE,KAAK,CAAC;QACb,IAAI,EAAE,MAAM,CAAC;QACb,WAAW,EAAE,MAAM,CAAC;QACpB,WAAW,EAAE;YACX,UAAU,EAAE,KAAK,CAAC;gBAChB,IAAI,EAAE,MAAM,CAAC;gBACb,IAAI,EAAE,MAAM,CAAC;gBACb,WAAW,EAAE,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAO,CAAC;aACnB,CAAC,CAAC;SACJ,CAAC;QACF,OAAO,EAAE,KAAK,CAAC;YACb,IAAI,EAAE,MAAM,CAAC;YACb,WAAW,EAAE,MAAM,CAAC;YACpB,UAAU,EAAE,KAAK,CAAC;gBAChB,IAAI,EAAE,MAAM,CAAC;gBACb,IAAI,EAAE,MAAM,CAAC;gBACb,WAAW,EAAE,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAO,CAAC;aACnB,CAAC,CAAC;YACH,OAAO,EAAE;gBACP,IAAI,EAAE,MAAM,CAAC;gBACb,WAAW,EAAE,MAAM,CAAC;aACrB,CAAC;YACF,UAAU,EAAE,QAAQ,GAAG,SAAS,GAAG,WAAW,CAAC;SAChD,CAAC,CAAC;QACH,UAAU,EAAE,KAAK,CAAC;YAChB,IAAI,EAAE,MAAM,CAAC;YACb,IAAI,EAAE,MAAM,CAAC;YACb,WAAW,EAAE,MAAM,CAAC;YACpB,UAAU,EAAE,QAAQ,GAAG,SAAS,GAAG,WAAW,CAAC;SAChD,CAAC,CAAC;KACJ,CAAC,CAAC;IACH,UAAU,EAAE,KAAK,CAAC;QAChB,IAAI,EAAE,MAAM,CAAC;QACb,WAAW,EAAE,MAAM,CAAC;QACpB,UAAU,EAAE,KAAK,CAAC;YAChB,IAAI,EAAE,MAAM,CAAC;YACb,IAAI,EAAE,MAAM,CAAC;YACb,WAAW,EAAE,MAAM,CAAC;YACpB,QAAQ,EAAE,OAAO,CAAC;SACnB,CAAC,CAAC;KACJ,CAAC,CAAC;CACJ;AAED,MAAM,WAAW,oBAAoB;IACnC,KAAK,EAAE,MAAM,CAAC;IACd,WAAW,EAAE,MAAM,CAAC;IACpB,OAAO,EAAE,MAAM,CAAC;IAChB,QAAQ,EAAE,oBAAoB,EAAE,CAAC;IACjC,GAAG,CAAC,EAAE,gBAAgB,CAAC;IACvB,YAAY,CAAC,EAAE;QACb,QAAQ,EAAE,MAAM,CAAC;QACjB,UAAU,EAAE,KAAK,CAAC;YAChB,IAAI,EAAE,MAAM,CAAC;YACb,WAAW,EAAE,MAAM,CAAC;YACpB,YAAY,EAAE,MAAM,EAAE,CAAC;YACvB,IAAI,EAAE,SAAS,GAAG,SAAS,GAAG,WAAW,GAAG,QAAQ,CAAC;SACtD,CAAC,CAAC;QACH,QAAQ,EAAE,MAAM,EAAE,CAAC;KACpB,CAAC;IACF,UAAU,CAAC,EAAE;QACX,YAAY,EAAE,MAAM,EAAE,CAAC;QACvB,KAAK,EAAE,MAAM,EAAE,CAAC;QAChB,aAAa,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QACnC,YAAY,EAAE,MAAM,EAAE,CAAC;KACxB,CAAC;IACF,OAAO,CAAC,EAAE;QACR,SAAS,EAAE,MAAM,CAAC;QAClB,QAAQ,EAAE,MAAM,CAAC;QACjB,SAAS,EAAE,MAAM,EAAE,CAAC;QACpB,eAAe,EAAE,MAAM,EAAE,CAAC;KAC3B,CAAC;CACH;AAED;;GAEG;AACH,qBAAa,sBAAsB;IACjC,OAAO,CAAC,aAAa,CAAgB;gBAEzB,aAAa,EAAE,aAAa;IAIxC;;OAEG;IACG,4BAA4B,CAChC,WAAW,EAAE,MAAM,EACnB,OAAO,EAAE,oBAAoB,GAC5B,OAAO,CAAC,oBAAoB,CAAC;IAwFhC;;OAEG;IACG,wBAAwB,CAAC,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,gBAAgB,CAAC;IA6FhG;;OAEG;IACG,oBAAoB,CAAC,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;QAC/E,KAAK,EAAE,MAAM,CAAC;QACd,WAAW,EAAE,MAAM,CAAC;QACpB,IAAI,EAAE,MAAM,CAAC;QACb,QAAQ,EAAE,MAAM,CAAC;KAClB,CAAC,CAAC;IA2BH;;OAEG;IACG,mBAAmB,CACvB,aAAa,EAAE,oBAAoB,EACnC,MAAM,EAAE,UAAU,GAAG,MAAM,GAAG,KAAK,GAAG,MAAM,EAC5C,UAAU,CAAC,EAAE,MAAM,GAClB,OAAO,CAAC,MAAM,CAAC;YAmCJ,cAAc;IA+D5B,OAAO,CAAC,gBAAgB;YAsBV,uBAAuB;YAiCvB,uBAAuB;YAmBvB,iCAAiC;YAejC,+BAA+B;YAqB/B,4BAA4B;IAa1C,OAAO,CAAC,sBAAsB;IAgD9B,OAAO,CAAC,+BAA+B;IAmBvC,OAAO,CAAC,6BAA6B;IAiBrC,OAAO,CAAC,0BAA0B;IAmBlC,OAAO,CAAC,gBAAgB;IAYxB,OAAO,CAAC,YAAY;IAmBpB,OAAO,CAAC,WAAW;IAKnB,OAAO,CAAC,iBAAiB;IAUzB,OAAO,CAAC,gBAAgB;IAUxB,OAAO,CAAC,cAAc;IAUtB,OAAO,CAAC,uBAAuB;IAS/B,OAAO,CAAC,yBAAyB;IAyBjC,OAAO,CAAC,4BAA4B;CAuCrC"}