/**
 * Window Manager - Handles all window creation and management
 */

import { BrowserWindow, screen, nativeTheme } from 'electron';
import * as path from 'path';
import * as log from 'electron-log';

export class WindowManager {
  private mainWindow: BrowserWindow | null = null;
  private windows: Map<string, BrowserWindow> = new Map();

  public createMainWindow(): BrowserWindow {
    // Get primary display dimensions
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width, height } = primaryDisplay.workAreaSize;

    // Calculate window size (80% of screen)
    const windowWidth = Math.floor(width * 0.8);
    const windowHeight = Math.floor(height * 0.8);

    // Create the main window
    this.mainWindow = new BrowserWindow({
      width: windowWidth,
      height: windowHeight,
      minWidth: 1000,
      minHeight: 700,
      x: Math.floor((width - windowWidth) / 2),
      y: Math.floor((height - windowHeight) / 2),
      
      // Window styling
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'hidden',
      frame: false,
      show: false,
      
      // Icon
      icon: this.getAppIcon(),
      
      // Web preferences
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false
      }
    });

    // Load the renderer
    const isDev = process.env.NODE_ENV === 'development';
    if (isDev) {
      this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    // Window event handlers
    this.setupWindowEvents();

    // Show window when ready
    this.mainWindow.once('ready-to-show', () => {
      if (this.mainWindow) {
        this.mainWindow.show();
        
        // Focus the window
        if (isDev) {
          this.mainWindow.focus();
        }
      }
    });

    // Store window reference
    this.windows.set('main', this.mainWindow);

    log.info('Main window created successfully');
    return this.mainWindow;
  }

  private setupWindowEvents(): void {
    if (!this.mainWindow) return;

    // Handle window closed
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
      this.windows.delete('main');
    });

    // Handle window focus
    this.mainWindow.on('focus', () => {
      log.debug('Main window focused');
    });

    // Handle window blur
    this.mainWindow.on('blur', () => {
      log.debug('Main window blurred');
    });

    // Handle window resize
    this.mainWindow.on('resize', () => {
      const [width, height] = this.mainWindow!.getSize();
      log.debug(`Window resized to ${width}x${height}`);
    });

    // Handle window maximize/unmaximize
    this.mainWindow.on('maximize', () => {
      this.mainWindow!.webContents.send('window-maximized', true);
    });

    this.mainWindow.on('unmaximize', () => {
      this.mainWindow!.webContents.send('window-maximized', false);
    });

    // Handle theme changes
    nativeTheme.on('updated', () => {
      this.mainWindow!.webContents.send('theme-changed', {
        shouldUseDarkColors: nativeTheme.shouldUseDarkColors,
        shouldUseHighContrastColors: nativeTheme.shouldUseHighContrastColors
      });
    });
  }

  private getAppIcon(): string {
    const iconName = process.platform === 'win32' ? 'icon.ico' : 
                     process.platform === 'darwin' ? 'icon.icns' : 'icon.png';
    return path.join(__dirname, '../../assets', iconName);
  }

  public getMainWindow(): BrowserWindow | null {
    return this.mainWindow;
  }

  public getAllWindows(): BrowserWindow[] {
    return Array.from(this.windows.values());
  }

  public closeAllWindows(): void {
    this.windows.forEach(window => {
      if (!window.isDestroyed()) {
        window.close();
      }
    });
    this.windows.clear();
  }

  public minimizeMainWindow(): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.minimize();
    }
  }

  public maximizeMainWindow(): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      if (this.mainWindow.isMaximized()) {
        this.mainWindow.unmaximize();
      } else {
        this.mainWindow.maximize();
      }
    }
  }

  public closeMainWindow(): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.close();
    }
  }

  public focusMainWindow(): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      if (this.mainWindow.isMinimized()) {
        this.mainWindow.restore();
      }
      this.mainWindow.focus();
    }
  }
}
