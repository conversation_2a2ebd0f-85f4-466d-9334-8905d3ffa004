"use strict";
/**
 * Auto Mode System
 *
 * Intelligent autonomous task execution and decision making
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutoModeEngine = void 0;
const utils_1 = require("../utils");
/**
 * Intelligent Auto Mode Engine
 */
class AutoModeEngine {
    constructor(contextEngine, webResearcher, options = {}) {
        this.activePlans = new Map();
        this.runningTasks = new Map();
        this.contextEngine = contextEngine;
        this.webResearcher = webResearcher;
        this.options = {
            maxConcurrentTasks: 3,
            timeoutMinutes: 30,
            enableLearning: true,
            enableWebResearch: true,
            enableFileOperations: false, // Disabled by default for safety
            safeMode: true,
            ...options
        };
    }
    /**
     * Create and execute an autonomous plan for a given goal
     */
    async executeGoal(goal, context = {}) {
        try {
            utils_1.logger.info('Starting auto mode execution', { goal });
            // Create execution plan
            const plan = await this.createPlan(goal, context);
            this.activePlans.set(plan.id, plan);
            // Execute plan
            await this.executePlan(plan);
            utils_1.logger.info('Auto mode execution completed', {
                planId: plan.id,
                status: plan.status,
                tasksCompleted: plan.tasks.filter(t => t.status === 'completed').length
            });
            return plan;
        }
        catch (error) {
            utils_1.logger.error('Auto mode execution failed', error);
            throw error;
        }
    }
    /**
     * Create an intelligent execution plan for a goal
     */
    async createPlan(goal, context) {
        const planId = `plan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        // Analyze the goal and create tasks
        const tasks = await this.analyzeGoalAndCreateTasks(goal, context);
        // Calculate dependencies and priorities
        const optimizedTasks = await this.optimizeTasks(tasks);
        // Estimate completion time
        const totalEstimatedDuration = optimizedTasks.reduce((sum, task) => sum + task.estimatedDuration, 0);
        const estimatedCompletion = new Date(Date.now() + totalEstimatedDuration * 1000);
        const plan = {
            id: planId,
            goal,
            tasks: optimizedTasks,
            status: 'planning',
            progress: 0,
            createdAt: new Date(),
            estimatedCompletion
        };
        utils_1.logger.info('Auto plan created', {
            planId,
            goal,
            tasksCount: tasks.length,
            estimatedDuration: totalEstimatedDuration
        });
        return plan;
    }
    /**
     * Execute a plan with intelligent task scheduling
     */
    async executePlan(plan) {
        plan.status = 'executing';
        try {
            while (this.hasRemainingTasks(plan)) {
                // Get next executable tasks
                const executableTasks = this.getExecutableTasks(plan);
                // Limit concurrent execution
                const tasksToExecute = executableTasks.slice(0, this.options.maxConcurrentTasks);
                if (tasksToExecute.length === 0) {
                    // Check for deadlock or completion
                    if (this.runningTasks.size === 0) {
                        throw new Error('Plan execution deadlocked - no executable tasks remaining');
                    }
                    // Wait for running tasks to complete
                    await this.waitForTaskCompletion();
                    continue;
                }
                // Execute tasks concurrently
                const taskPromises = tasksToExecute.map(task => this.executeTask(task, plan));
                await Promise.allSettled(taskPromises);
                // Update plan progress
                this.updatePlanProgress(plan);
            }
            plan.status = 'completed';
            plan.actualCompletion = new Date();
        }
        catch (error) {
            plan.status = 'failed';
            utils_1.logger.error('Plan execution failed', error);
            throw error;
        }
    }
    /**
     * Execute a single task with intelligent decision making
     */
    async executeTask(task, plan) {
        task.status = 'running';
        task.startedAt = new Date();
        this.runningTasks.set(task.id, task);
        try {
            utils_1.logger.info('Executing auto task', { taskId: task.id, type: task.type });
            let result;
            switch (task.type) {
                case 'code_analysis':
                    result = await this.executeCodeAnalysis(task);
                    break;
                case 'web_research':
                    result = await this.executeWebResearch(task);
                    break;
                case 'context_search':
                    result = await this.executeContextSearch(task);
                    break;
                case 'learning':
                    result = await this.executeLearning(task);
                    break;
                case 'file_operation':
                    result = await this.executeFileOperation(task);
                    break;
                default:
                    throw new Error(`Unknown task type: ${task.type}`);
            }
            task.output = result;
            task.status = 'completed';
            task.completedAt = new Date();
            task.actualDuration = (task.completedAt.getTime() - task.startedAt.getTime()) / 1000;
            utils_1.logger.info('Auto task completed', {
                taskId: task.id,
                duration: task.actualDuration
            });
        }
        catch (error) {
            task.status = 'failed';
            task.error = error instanceof Error ? error.message : String(error);
            task.completedAt = new Date();
            utils_1.logger.error('Auto task failed', {
                taskId: task.id,
                error: task.error
            });
            // Decide whether to continue or abort plan
            if (!this.shouldContinueAfterFailure(task, plan)) {
                throw error;
            }
        }
        finally {
            this.runningTasks.delete(task.id);
        }
    }
    /**
     * Make intelligent decisions based on context
     */
    async makeDecision(context) {
        // Simplified decision making - in production, this would use AI
        const { goal, availableTools, currentContext, previousActions, constraints } = context;
        // Analyze goal and determine best action
        let action = 'analyze_context';
        let reasoning = 'Starting with context analysis to understand the current situation';
        let confidence = 0.8;
        let alternatives = ['web_research', 'code_analysis'];
        // Goal-based decision making
        if (goal.toLowerCase().includes('research') || goal.toLowerCase().includes('learn')) {
            action = 'web_research';
            reasoning = 'Goal involves research, starting with web search';
            confidence = 0.9;
            alternatives = ['context_search', 'analyze_context'];
        }
        else if (goal.toLowerCase().includes('code') || goal.toLowerCase().includes('implement')) {
            action = 'code_analysis';
            reasoning = 'Goal involves coding, starting with code analysis';
            confidence = 0.85;
            alternatives = ['context_search', 'web_research'];
        }
        else if (goal.toLowerCase().includes('find') || goal.toLowerCase().includes('search')) {
            action = 'context_search';
            reasoning = 'Goal involves searching, using context search';
            confidence = 0.9;
            alternatives = ['web_research', 'analyze_context'];
        }
        // Apply constraints
        if (constraints.includes('no_web') && action === 'web_research') {
            action = alternatives[0];
            reasoning += ' (web research disabled by constraints)';
            confidence *= 0.8;
        }
        if (constraints.includes('no_files') && action === 'file_operation') {
            action = alternatives[0];
            reasoning += ' (file operations disabled by constraints)';
            confidence *= 0.8;
        }
        return {
            action,
            reasoning,
            confidence,
            alternatives
        };
    }
    /**
     * Get current auto mode status
     */
    getStatus() {
        const allTasks = Array.from(this.activePlans.values()).flatMap(plan => plan.tasks);
        const completedTasks = allTasks.filter(task => task.status === 'completed');
        const averageTaskDuration = completedTasks.length > 0
            ? completedTasks.reduce((sum, task) => sum + (task.actualDuration || 0), 0) / completedTasks.length
            : 0;
        return {
            activePlans: this.activePlans.size,
            runningTasks: this.runningTasks.size,
            totalTasksCompleted: completedTasks.length,
            averageTaskDuration
        };
    }
    /**
     * Stop all auto mode execution
     */
    async stopAll() {
        utils_1.logger.info('Stopping all auto mode execution');
        // Mark all plans as failed
        for (const plan of this.activePlans.values()) {
            if (plan.status === 'executing') {
                plan.status = 'failed';
            }
        }
        // Clear running tasks
        this.runningTasks.clear();
        this.activePlans.clear();
    }
    // Private helper methods
    async analyzeGoalAndCreateTasks(goal, context) {
        const tasks = [];
        let taskCounter = 0;
        // Analyze goal to determine required tasks
        const goalLower = goal.toLowerCase();
        // Always start with context analysis
        tasks.push({
            id: `task_${++taskCounter}_${Date.now()}`,
            type: 'context_search',
            description: 'Analyze current context and gather relevant information',
            input: { query: goal, context },
            status: 'pending',
            priority: 10,
            dependencies: [],
            estimatedDuration: 30,
            createdAt: new Date()
        });
        // Add web research if goal involves learning or research
        if (this.options.enableWebResearch && (goalLower.includes('research') ||
            goalLower.includes('learn') ||
            goalLower.includes('tutorial') ||
            goalLower.includes('example'))) {
            tasks.push({
                id: `task_${++taskCounter}_${Date.now()}`,
                type: 'web_research',
                description: 'Research topic using web sources',
                input: { topic: goal, depth: 'intermediate' },
                status: 'pending',
                priority: 8,
                dependencies: [tasks[0].id],
                estimatedDuration: 120,
                createdAt: new Date()
            });
        }
        // Add code analysis if goal involves code
        if (goalLower.includes('code') ||
            goalLower.includes('implement') ||
            goalLower.includes('function') ||
            goalLower.includes('class')) {
            tasks.push({
                id: `task_${++taskCounter}_${Date.now()}`,
                type: 'code_analysis',
                description: 'Analyze existing code and patterns',
                input: { goal, context },
                status: 'pending',
                priority: 7,
                dependencies: [tasks[0].id],
                estimatedDuration: 60,
                createdAt: new Date()
            });
        }
        // Add learning task if enabled
        if (this.options.enableLearning) {
            tasks.push({
                id: `task_${++taskCounter}_${Date.now()}`,
                type: 'learning',
                description: 'Learn from the execution process',
                input: { goal, tasks: tasks.map(t => t.type) },
                status: 'pending',
                priority: 1,
                dependencies: tasks.slice(1).map(t => t.id), // Depends on all other tasks
                estimatedDuration: 15,
                createdAt: new Date()
            });
        }
        return tasks;
    }
    async optimizeTasks(tasks) {
        // Sort by priority (higher priority first)
        return tasks.sort((a, b) => b.priority - a.priority);
    }
    hasRemainingTasks(plan) {
        return plan.tasks.some(task => task.status === 'pending' || task.status === 'running');
    }
    getExecutableTasks(plan) {
        return plan.tasks.filter(task => {
            if (task.status !== 'pending')
                return false;
            // Check if all dependencies are completed
            return task.dependencies.every(depId => {
                const depTask = plan.tasks.find(t => t.id === depId);
                return depTask && depTask.status === 'completed';
            });
        });
    }
    async waitForTaskCompletion() {
        // Simple polling - in production, this would use events
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    updatePlanProgress(plan) {
        const completedTasks = plan.tasks.filter(t => t.status === 'completed').length;
        plan.progress = (completedTasks / plan.tasks.length) * 100;
    }
    shouldContinueAfterFailure(task, plan) {
        // In safe mode, stop on any failure
        if (this.options.safeMode) {
            return false;
        }
        // Continue if task is not critical (priority < 5)
        return task.priority < 5;
    }
    async executeCodeAnalysis(task) {
        const { goal, context } = task.input;
        // Use context engine to analyze code
        if (context && context.filePath) {
            return await this.contextEngine.analyzeCode(context.code || '', {
                language: context.language,
                includeMetrics: true,
                includeSuggestions: true,
                filePath: context.filePath
            });
        }
        // Fallback to general analysis
        return {
            summary: `Code analysis for goal: ${goal}`,
            suggestions: ['Consider using modern patterns', 'Add error handling', 'Improve documentation'],
            metrics: { complexity: 1, maintainabilityIndex: 85 }
        };
    }
    async executeWebResearch(task) {
        const { topic, depth } = task.input;
        return await this.webResearcher.researchTopic(topic, {
            depth: depth || 'intermediate',
            maxSources: 5,
            includeExamples: true,
            includeTrends: false
        });
    }
    async executeContextSearch(task) {
        const { query, context } = task.input;
        return await this.contextEngine.searchContext(query, {
            maxResults: 10,
            projectPath: context?.projectPath
        });
    }
    async executeLearning(task) {
        const { goal, tasks } = task.input;
        // Record learning event
        return {
            event: 'auto_mode_execution',
            goal,
            tasksExecuted: tasks,
            timestamp: new Date(),
            insights: ['Auto mode successfully executed plan', 'Task dependencies resolved correctly']
        };
    }
    async executeFileOperation(task) {
        if (!this.options.enableFileOperations) {
            throw new Error('File operations are disabled in auto mode');
        }
        // Placeholder for file operations - would integrate with file tools
        return {
            operation: 'file_operation',
            status: 'simulated',
            message: 'File operations are not implemented in auto mode for safety'
        };
    }
}
exports.AutoModeEngine = AutoModeEngine;
//# sourceMappingURL=index.js.map