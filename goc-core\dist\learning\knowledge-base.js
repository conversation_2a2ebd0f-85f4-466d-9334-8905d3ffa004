"use strict";
/**
 * Knowledge Base
 *
 * Persistent storage and retrieval system for learned patterns, user preferences,
 * and contextual knowledge
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnowledgeBase = void 0;
const fs = __importStar(require("fs/promises"));
const path = __importStar(require("path"));
const utils_1 = require("../utils");
class KnowledgeBase {
    constructor(config) {
        this.patterns = new Map();
        this.preferences = new Map();
        this.patternIndex = new Map();
        this.initialized = false;
        this.config = config;
        this.storageDir = path.join(config.learning.storageDirectory, 'knowledge-base');
        this.patternsFile = path.join(this.storageDir, 'patterns.json');
        this.preferencesFile = path.join(this.storageDir, 'preferences.json');
        this.indexFile = path.join(this.storageDir, 'index.json');
    }
    async initialize() {
        if (this.initialized)
            return;
        try {
            utils_1.logger.info('Initializing Knowledge Base', 'KnowledgeBase');
            // Ensure storage directory exists
            await fs.mkdir(this.storageDir, { recursive: true });
            // Load existing data
            await this.loadPatterns();
            await this.loadPreferences();
            await this.loadIndex();
            // Clean up old patterns if needed
            await this.cleanupOldPatterns();
            this.initialized = true;
            utils_1.logger.info(`Knowledge Base initialized with ${this.patterns.size} patterns`, 'KnowledgeBase');
        }
        catch (error) {
            utils_1.logger.error('Failed to initialize Knowledge Base', 'KnowledgeBase', { error });
            throw error;
        }
    }
    /**
     * Store a learning pattern
     */
    async storePattern(pattern) {
        try {
            // Check if pattern already exists and update frequency
            const existingPattern = this.findSimilarPattern(pattern);
            if (existingPattern) {
                existingPattern.frequency++;
                existingPattern.lastUsed = new Date();
                existingPattern.confidence = Math.min(existingPattern.confidence + 0.1, 1.0);
            }
            else {
                this.patterns.set(pattern.id, pattern);
                this.updateIndex(pattern);
            }
            // Save to disk periodically
            if (this.patterns.size % 10 === 0) {
                await this.savePatterns();
            }
            utils_1.logger.debug(`Stored pattern: ${pattern.type} - ${pattern.context}`, 'KnowledgeBase');
        }
        catch (error) {
            utils_1.logger.error('Failed to store pattern', 'KnowledgeBase', { error });
        }
    }
    /**
     * Search for patterns
     */
    async searchPatterns(query, options = {}) {
        const { userId, projectId, type, limit = 10, minConfidence = 0.5, tags = [] } = options;
        let results = Array.from(this.patterns.values());
        // Filter by user
        if (userId) {
            results = results.filter(p => !p.userId || p.userId === userId);
        }
        // Filter by project
        if (projectId) {
            results = results.filter(p => !p.projectId || p.projectId === projectId);
        }
        // Filter by type
        if (type) {
            results = results.filter(p => p.type === type);
        }
        // Filter by confidence
        results = results.filter(p => p.confidence >= minConfidence);
        // Filter by tags
        if (tags.length > 0) {
            results = results.filter(p => tags.some(tag => p.tags.includes(tag)));
        }
        // Search by content and context
        if (query.trim()) {
            const queryLower = query.toLowerCase();
            results = results.filter(p => p.content.toLowerCase().includes(queryLower) ||
                p.context.toLowerCase().includes(queryLower) ||
                p.tags.some(tag => tag.toLowerCase().includes(queryLower)));
        }
        // Sort by relevance (frequency * confidence * recency)
        results.sort((a, b) => {
            const scoreA = this.calculateRelevanceScore(a, query);
            const scoreB = this.calculateRelevanceScore(b, query);
            return scoreB - scoreA;
        });
        return results.slice(0, limit);
    }
    /**
     * Get user preferences
     */
    async getUserPreferences(userId) {
        return this.preferences.get(userId) || [];
    }
    /**
     * Update user preference
     */
    async updateUserPreference(userId, preference) {
        try {
            const userPrefs = this.preferences.get(userId) || [];
            // Find existing preference
            const existingIndex = userPrefs.findIndex(p => p.category === preference.category && p.key === preference.key);
            if (existingIndex >= 0) {
                // Update existing preference
                userPrefs[existingIndex] = {
                    ...userPrefs[existingIndex],
                    ...preference,
                    updatedAt: new Date()
                };
            }
            else {
                // Create new preference
                const newPreference = {
                    id: this.generateId(),
                    userId,
                    category: preference.category,
                    key: preference.key,
                    value: preference.value,
                    confidence: preference.confidence || 0.8,
                    learnedFrom: preference.learnedFrom || [],
                    createdAt: new Date(),
                    updatedAt: new Date()
                };
                userPrefs.push(newPreference);
            }
            this.preferences.set(userId, userPrefs);
            // Save preferences periodically
            if (userPrefs.length % 5 === 0) {
                await this.savePreferences();
            }
            utils_1.logger.debug(`Updated preference for user ${userId}: ${preference.category}.${preference.key}`, 'KnowledgeBase');
        }
        catch (error) {
            utils_1.logger.error('Failed to update user preference', 'KnowledgeBase', { error });
        }
    }
    /**
     * Get patterns by type
     */
    async getPatternsByType(type, options = {}) {
        return this.searchPatterns('', { ...options, type });
    }
    /**
     * Get most frequent patterns
     */
    async getMostFrequentPatterns(limit = 10, options = {}) {
        const patterns = await this.searchPatterns('', options);
        return patterns
            .sort((a, b) => b.frequency - a.frequency)
            .slice(0, limit);
    }
    /**
     * Get recent patterns
     */
    async getRecentPatterns(limit = 10, options = {}) {
        const patterns = await this.searchPatterns('', options);
        return patterns
            .sort((a, b) => b.lastUsed.getTime() - a.lastUsed.getTime())
            .slice(0, limit);
    }
    /**
     * Delete pattern
     */
    async deletePattern(patternId) {
        const deleted = this.patterns.delete(patternId);
        if (deleted) {
            await this.savePatterns();
            utils_1.logger.debug(`Deleted pattern: ${patternId}`, 'KnowledgeBase');
        }
        return deleted;
    }
    /**
     * Get storage statistics
     */
    getStatistics() {
        const patternsByType = {};
        for (const pattern of this.patterns.values()) {
            patternsByType[pattern.type] = (patternsByType[pattern.type] || 0) + 1;
        }
        const totalPreferences = Array.from(this.preferences.values())
            .reduce((sum, prefs) => sum + prefs.length, 0);
        return {
            totalPatterns: this.patterns.size,
            patternsByType,
            totalUsers: this.preferences.size,
            totalPreferences,
            storageSize: this.estimateStorageSize()
        };
    }
    // Private methods
    findSimilarPattern(pattern) {
        for (const existing of this.patterns.values()) {
            if (existing.type === pattern.type &&
                existing.context === pattern.context &&
                existing.content === pattern.content &&
                existing.userId === pattern.userId &&
                existing.projectId === pattern.projectId) {
                return existing;
            }
        }
        return undefined;
    }
    updateIndex(pattern) {
        // Create searchable index
        const keywords = [
            ...pattern.tags,
            pattern.type,
            pattern.context,
            ...pattern.content.split(/\s+/).filter(word => word.length > 2)
        ];
        for (const keyword of keywords) {
            const normalizedKeyword = keyword.toLowerCase();
            if (!this.patternIndex.has(normalizedKeyword)) {
                this.patternIndex.set(normalizedKeyword, []);
            }
            this.patternIndex.get(normalizedKeyword).push(pattern.id);
        }
    }
    calculateRelevanceScore(pattern, query) {
        const now = Date.now();
        const ageInDays = (now - pattern.lastUsed.getTime()) / (1000 * 60 * 60 * 24);
        const recencyScore = Math.max(0, 1 - (ageInDays / 30)); // Decay over 30 days
        const frequencyScore = Math.min(pattern.frequency / 10, 1); // Normalize frequency
        const confidenceScore = pattern.confidence;
        // Query relevance (simple text matching)
        let queryScore = 0;
        if (query.trim()) {
            const queryLower = query.toLowerCase();
            if (pattern.content.toLowerCase().includes(queryLower))
                queryScore += 0.5;
            if (pattern.context.toLowerCase().includes(queryLower))
                queryScore += 0.3;
            if (pattern.tags.some(tag => tag.toLowerCase().includes(queryLower)))
                queryScore += 0.2;
        }
        else {
            queryScore = 1; // No query penalty
        }
        return (frequencyScore * 0.3 + confidenceScore * 0.4 + recencyScore * 0.2 + queryScore * 0.1);
    }
    async loadPatterns() {
        try {
            const data = await fs.readFile(this.patternsFile, 'utf-8');
            const patternsArray = JSON.parse(data);
            for (const pattern of patternsArray) {
                // Convert date strings back to Date objects
                pattern.createdAt = new Date(pattern.createdAt);
                pattern.lastUsed = new Date(pattern.lastUsed);
                this.patterns.set(pattern.id, pattern);
                this.updateIndex(pattern);
            }
        }
        catch (error) {
            // File doesn't exist or is corrupted, start fresh
            utils_1.logger.debug('No existing patterns file found, starting fresh', 'KnowledgeBase');
        }
    }
    async savePatterns() {
        try {
            const patternsArray = Array.from(this.patterns.values());
            await fs.writeFile(this.patternsFile, JSON.stringify(patternsArray, null, 2));
        }
        catch (error) {
            utils_1.logger.error('Failed to save patterns', 'KnowledgeBase', { error });
        }
    }
    async loadPreferences() {
        try {
            const data = await fs.readFile(this.preferencesFile, 'utf-8');
            const preferencesData = JSON.parse(data);
            for (const [userId, prefs] of Object.entries(preferencesData)) {
                // Convert date strings back to Date objects
                const convertedPrefs = prefs.map(pref => ({
                    ...pref,
                    createdAt: new Date(pref.createdAt),
                    updatedAt: new Date(pref.updatedAt)
                }));
                this.preferences.set(userId, convertedPrefs);
            }
        }
        catch (error) {
            utils_1.logger.debug('No existing preferences file found, starting fresh', 'KnowledgeBase');
        }
    }
    async savePreferences() {
        try {
            const preferencesData = Object.fromEntries(this.preferences);
            await fs.writeFile(this.preferencesFile, JSON.stringify(preferencesData, null, 2));
        }
        catch (error) {
            utils_1.logger.error('Failed to save preferences', 'KnowledgeBase', { error });
        }
    }
    async loadIndex() {
        try {
            const data = await fs.readFile(this.indexFile, 'utf-8');
            const indexData = JSON.parse(data);
            this.patternIndex = new Map(Object.entries(indexData));
        }
        catch (error) {
            utils_1.logger.debug('No existing index file found, will rebuild', 'KnowledgeBase');
        }
    }
    async saveIndex() {
        try {
            const indexData = Object.fromEntries(this.patternIndex);
            await fs.writeFile(this.indexFile, JSON.stringify(indexData, null, 2));
        }
        catch (error) {
            utils_1.logger.error('Failed to save index', 'KnowledgeBase', { error });
        }
    }
    async cleanupOldPatterns() {
        if (!this.config.learning.retentionDays)
            return;
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - this.config.learning.retentionDays);
        let removedCount = 0;
        for (const [id, pattern] of this.patterns.entries()) {
            if (pattern.lastUsed < cutoffDate && pattern.frequency < 2) {
                this.patterns.delete(id);
                removedCount++;
            }
        }
        if (removedCount > 0) {
            await this.savePatterns();
            utils_1.logger.info(`Cleaned up ${removedCount} old patterns`, 'KnowledgeBase');
        }
    }
    estimateStorageSize() {
        // Rough estimate of storage size in bytes
        const patternsSize = JSON.stringify(Array.from(this.patterns.values())).length;
        const preferencesSize = JSON.stringify(Object.fromEntries(this.preferences)).length;
        const indexSize = JSON.stringify(Object.fromEntries(this.patternIndex)).length;
        return patternsSize + preferencesSize + indexSize;
    }
    generateId() {
        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    async dispose() {
        if (!this.initialized)
            return;
        try {
            // Save all data before disposing
            await Promise.all([
                this.savePatterns(),
                this.savePreferences(),
                this.saveIndex()
            ]);
            // Clear memory
            this.patterns.clear();
            this.preferences.clear();
            this.patternIndex.clear();
            this.initialized = false;
            utils_1.logger.info('Knowledge Base disposed', 'KnowledgeBase');
        }
        catch (error) {
            utils_1.logger.error('Failed to dispose Knowledge Base', 'KnowledgeBase', { error });
        }
    }
}
exports.KnowledgeBase = KnowledgeBase;
//# sourceMappingURL=knowledge-base.js.map