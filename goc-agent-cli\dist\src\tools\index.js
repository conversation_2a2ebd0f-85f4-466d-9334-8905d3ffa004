"use strict";
/**
 * Enhanced Tool System
 *
 * Implements advanced file operations, process management, and development environment integration
 * inspired by Augment Agent's tool system.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolRegistry = exports.WorkspaceDetector = exports.PackageManager = exports.TerminalManager = exports.DiagnosticsProvider = exports.ProcessManager = exports.FileOperations = void 0;
exports.initializeTools = initializeTools;
// Re-export all tools
var file_operations_1 = require("./file-operations");
Object.defineProperty(exports, "FileOperations", { enumerable: true, get: function () { return file_operations_1.FileOperations; } });
var process_manager_1 = require("./process-manager");
Object.defineProperty(exports, "ProcessManager", { enumerable: true, get: function () { return process_manager_1.ProcessManager; } });
var diagnostics_1 = require("./diagnostics");
Object.defineProperty(exports, "DiagnosticsProvider", { enumerable: true, get: function () { return diagnostics_1.DiagnosticsProvider; } });
var terminal_1 = require("./terminal");
Object.defineProperty(exports, "TerminalManager", { enumerable: true, get: function () { return terminal_1.TerminalManager; } });
var package_manager_1 = require("./package-manager");
Object.defineProperty(exports, "PackageManager", { enumerable: true, get: function () { return package_manager_1.PackageManager; } });
var workspace_1 = require("./workspace");
Object.defineProperty(exports, "WorkspaceDetector", { enumerable: true, get: function () { return workspace_1.WorkspaceDetector; } });
// Tool registry
class ToolRegistry {
    constructor() {
        this.tools = new Map();
    }
    static getInstance() {
        if (!ToolRegistry.instance) {
            ToolRegistry.instance = new ToolRegistry();
        }
        return ToolRegistry.instance;
    }
    registerTool(name, tool) {
        this.tools.set(name, tool);
    }
    getTool(name) {
        return this.tools.get(name);
    }
    getAvailableTools() {
        return Array.from(this.tools.keys());
    }
    async executeTool(name, method, ...args) {
        try {
            const tool = this.getTool(name);
            if (!tool) {
                return {
                    success: false,
                    error: `Tool '${name}' not found`
                };
            }
            if (typeof tool[method] !== 'function') {
                return {
                    success: false,
                    error: `Method '${method}' not found on tool '${name}'`
                };
            }
            const result = await tool[method](...args);
            return {
                success: true,
                data: result
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
}
exports.ToolRegistry = ToolRegistry;
// Initialize default tools
function initializeTools() {
    const registry = ToolRegistry.getInstance();
    // Register all tools
    registry.registerTool('file', new (require('./file-operations').FileOperations)());
    registry.registerTool('process', new (require('./process-manager').ProcessManager)());
    registry.registerTool('diagnostics', new (require('./diagnostics').DiagnosticsProvider)());
    registry.registerTool('terminal', new (require('./terminal').TerminalManager)());
    registry.registerTool('package', new (require('./package-manager').PackageManager)());
    registry.registerTool('workspace', new (require('./workspace').WorkspaceDetector)());
}
//# sourceMappingURL=index.js.map