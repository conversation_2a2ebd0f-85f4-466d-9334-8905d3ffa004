"use strict";
/**
 * Auto Mode Command
 *
 * Intelligent autonomous task execution and decision making
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutoCommand = void 0;
const chalk_1 = __importDefault(require("chalk"));
const inquirer_1 = __importDefault(require("inquirer"));
class AutoCommand {
    constructor(core) {
        this.core = core;
    }
    register(program) {
        const autoCmd = program
            .command('auto')
            .description('Intelligent autonomous task execution');
        autoCmd
            .command('execute <goal>')
            .description('Execute a goal autonomously')
            .option('--safe', 'Enable safe mode (default)', true)
            .option('--no-web', 'Disable web research')
            .option('--no-learning', 'Disable learning')
            .option('--timeout <minutes>', 'Timeout in minutes', '30')
            .option('--max-tasks <number>', 'Maximum concurrent tasks', '3')
            .action(async (goal, options) => {
            await this.executeGoal(goal, options);
        });
        autoCmd
            .command('status')
            .description('Show auto mode status')
            .action(async () => {
            await this.showStatus();
        });
        autoCmd
            .command('stop')
            .description('Stop all auto mode execution')
            .action(async () => {
            await this.stopExecution();
        });
        autoCmd
            .command('plan <goal>')
            .description('Create an execution plan without executing')
            .action(async (goal) => {
            await this.createPlan(goal);
        });
        autoCmd
            .command('decide')
            .description('Make an intelligent decision based on context')
            .option('--goal <goal>', 'Goal to achieve')
            .option('--context <context>', 'Current context (JSON)')
            .action(async (options) => {
            await this.makeDecision(options);
        });
    }
    async executeGoal(goal, options) {
        try {
            console.log(chalk_1.default.blue(`🤖 Starting autonomous execution: "${goal}"`));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            const autoEngine = this.core.getAutoModeEngine();
            // Show configuration
            console.log(chalk_1.default.blue('⚙️ Configuration:'));
            console.log(`Safe Mode: ${options.safe ? chalk_1.default.green('Enabled') : chalk_1.default.red('Disabled')}`);
            console.log(`Web Research: ${options.web !== false ? chalk_1.default.green('Enabled') : chalk_1.default.red('Disabled')}`);
            console.log(`Learning: ${options.learning !== false ? chalk_1.default.green('Enabled') : chalk_1.default.red('Disabled')}`);
            console.log(`Timeout: ${options.timeout} minutes`);
            console.log(`Max Concurrent Tasks: ${options.maxTasks}`);
            console.log();
            // Confirm execution in non-safe mode
            if (!options.safe) {
                const { confirm } = await inquirer_1.default.prompt([{
                        type: 'confirm',
                        name: 'confirm',
                        message: 'Safe mode is disabled. This may perform file operations. Continue?',
                        default: false
                    }]);
                if (!confirm) {
                    console.log(chalk_1.default.yellow('Execution cancelled.'));
                    return;
                }
            }
            console.log(chalk_1.default.blue('🚀 Executing goal...'));
            const startTime = Date.now();
            const plan = await autoEngine.executeGoal(goal, {
                projectPath: process.cwd(),
                safeMode: options.safe,
                enableWebResearch: options.web !== false,
                enableLearning: options.learning !== false,
                timeoutMinutes: parseInt(options.timeout),
                maxConcurrentTasks: parseInt(options.maxTasks)
            });
            const duration = Date.now() - startTime;
            console.log(chalk_1.default.green('✅ Execution completed!'));
            console.log();
            // Show plan results
            console.log(chalk_1.default.blue('📋 Execution Summary:'));
            console.log(`Plan ID: ${plan.id}`);
            console.log(`Status: ${this.getStatusColor(plan.status)}${plan.status}${chalk_1.default.reset()}`);
            console.log(`Progress: ${plan.progress.toFixed(1)}%`);
            console.log(`Duration: ${(duration / 1000).toFixed(1)}s`);
            console.log(`Tasks: ${plan.tasks.length}`);
            console.log();
            // Show task results
            console.log(chalk_1.default.blue('📝 Task Results:'));
            plan.tasks.forEach((task, index) => {
                const statusIcon = task.status === 'completed' ? '✅' :
                    task.status === 'failed' ? '❌' :
                        task.status === 'running' ? '🔄' : '⏳';
                console.log(`${index + 1}. ${statusIcon} ${task.description}`);
                console.log(chalk_1.default.dim(`   Type: ${task.type}, Priority: ${task.priority}`));
                if (task.actualDuration) {
                    console.log(chalk_1.default.dim(`   Duration: ${task.actualDuration.toFixed(1)}s`));
                }
                if (task.error) {
                    console.log(chalk_1.default.red(`   Error: ${task.error}`));
                }
                if (task.output && typeof task.output === 'object') {
                    const outputPreview = JSON.stringify(task.output).substring(0, 100);
                    console.log(chalk_1.default.dim(`   Output: ${outputPreview}${JSON.stringify(task.output).length > 100 ? '...' : ''}`));
                }
                console.log();
            });
            // Show recommendations
            const completedTasks = plan.tasks.filter(t => t.status === 'completed');
            if (completedTasks.length > 0) {
                console.log(chalk_1.default.blue('💡 Recommendations:'));
                console.log('• Review the generated outputs for accuracy');
                console.log('• Consider running tests if code was generated');
                console.log('• Check for any manual steps that may be required');
                if (plan.tasks.some(t => t.type === 'web_research')) {
                    console.log('• Verify web research results are current and accurate');
                }
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Auto execution failed:'), error);
        }
    }
    async showStatus() {
        try {
            console.log(chalk_1.default.blue('🤖 Auto Mode Status'));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            const autoEngine = this.core.getAutoModeEngine();
            const status = autoEngine.getStatus();
            console.log(chalk_1.default.blue('📊 Current Status:'));
            console.log(`Active Plans: ${status.activePlans}`);
            console.log(`Running Tasks: ${status.runningTasks}`);
            console.log(`Total Tasks Completed: ${status.totalTasksCompleted}`);
            console.log(`Average Task Duration: ${status.averageTaskDuration.toFixed(1)}s`);
            console.log();
            if (status.activePlans === 0 && status.runningTasks === 0) {
                console.log(chalk_1.default.green('✅ No active auto mode execution'));
                console.log(chalk_1.default.dim('Use "goc auto execute <goal>" to start autonomous execution'));
            }
            else {
                console.log(chalk_1.default.yellow('⚠️ Auto mode is currently active'));
                console.log(chalk_1.default.dim('Use "goc auto stop" to stop all execution'));
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Failed to get auto mode status:'), error);
        }
    }
    async stopExecution() {
        try {
            console.log(chalk_1.default.yellow('🛑 Stopping all auto mode execution...'));
            const autoEngine = this.core.getAutoModeEngine();
            await autoEngine.stopAll();
            console.log(chalk_1.default.green('✅ All auto mode execution stopped'));
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Failed to stop auto mode execution:'), error);
        }
    }
    async createPlan(goal) {
        try {
            console.log(chalk_1.default.blue(`📋 Creating execution plan for: "${goal}"`));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            const autoEngine = this.core.getAutoModeEngine();
            const plan = await autoEngine.createPlan(goal, {
                projectPath: process.cwd()
            });
            console.log(chalk_1.default.blue('📝 Execution Plan:'));
            console.log(`Plan ID: ${plan.id}`);
            console.log(`Goal: ${plan.goal}`);
            console.log(`Tasks: ${plan.tasks.length}`);
            console.log(`Estimated Duration: ${Math.ceil((plan.estimatedCompletion.getTime() - plan.createdAt.getTime()) / 1000)}s`);
            console.log();
            console.log(chalk_1.default.blue('📋 Task Breakdown:'));
            plan.tasks.forEach((task, index) => {
                console.log(`${index + 1}. ${task.description}`);
                console.log(chalk_1.default.dim(`   Type: ${task.type}`));
                console.log(chalk_1.default.dim(`   Priority: ${task.priority}`));
                console.log(chalk_1.default.dim(`   Estimated Duration: ${task.estimatedDuration}s`));
                if (task.dependencies.length > 0) {
                    console.log(chalk_1.default.dim(`   Dependencies: ${task.dependencies.length} task(s)`));
                }
                console.log();
            });
            console.log(chalk_1.default.blue('💡 Next Steps:'));
            console.log('• Review the plan to ensure it meets your requirements');
            console.log('• Use "goc auto execute" to run the plan');
            console.log('• Modify the goal description if needed for better results');
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Failed to create execution plan:'), error);
        }
    }
    async makeDecision(options) {
        try {
            console.log(chalk_1.default.blue('🧠 Making intelligent decision...'));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            if (!options.goal) {
                const { goal } = await inquirer_1.default.prompt([{
                        type: 'input',
                        name: 'goal',
                        message: 'What goal do you want to achieve?',
                        validate: (input) => input.trim().length > 0 || 'Goal cannot be empty'
                    }]);
                options.goal = goal;
            }
            let context = {};
            if (options.context) {
                try {
                    context = JSON.parse(options.context);
                }
                catch {
                    console.log(chalk_1.default.yellow('⚠️ Invalid context JSON, using empty context'));
                }
            }
            const autoEngine = this.core.getAutoModeEngine();
            const decision = await autoEngine.makeDecision({
                goal: options.goal,
                availableTools: ['context_search', 'web_research', 'code_analysis', 'learning'],
                currentContext: context,
                previousActions: [],
                constraints: ['no_files'] // Safe mode constraints
            });
            console.log(chalk_1.default.blue('🎯 Decision Result:'));
            console.log(`Recommended Action: ${chalk_1.default.bold(decision.action)}`);
            console.log(`Confidence: ${(decision.confidence * 100).toFixed(0)}%`);
            console.log(`Reasoning: ${decision.reasoning}`);
            console.log();
            if (decision.alternatives.length > 0) {
                console.log(chalk_1.default.blue('🔄 Alternative Actions:'));
                decision.alternatives.forEach((alt, index) => {
                    console.log(`${index + 1}. ${alt}`);
                });
                console.log();
            }
            console.log(chalk_1.default.blue('💡 Next Steps:'));
            console.log(`• Use "goc auto execute '${options.goal}'" to execute the goal`);
            console.log('• Consider the alternative actions if the recommended action is not suitable');
            console.log('• Provide more context for better decision making');
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Failed to make decision:'), error);
        }
    }
    getStatusColor(status) {
        switch (status) {
            case 'completed':
                return chalk_1.default.green.bold;
            case 'failed':
                return chalk_1.default.red.bold;
            case 'executing':
                return chalk_1.default.yellow.bold;
            case 'planning':
                return chalk_1.default.blue.bold;
            default:
                return chalk_1.default.dim;
        }
    }
}
exports.AutoCommand = AutoCommand;
//# sourceMappingURL=auto.js.map