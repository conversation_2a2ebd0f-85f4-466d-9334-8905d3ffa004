"use strict";
/**
 * Backend API Client - Full Implementation
 *
 * Complete implementation for GOC Agent Laravel backend integration
 * with authentication, session management, and API communication.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BackendAPIClient = void 0;
const axios_1 = __importDefault(require("axios"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const os = __importStar(require("os"));
class BackendAPIClient {
    constructor(baseURL = 'http://localhost:8000/api') {
        this.token = null;
        this.baseURL = baseURL;
        this.configPath = path.join(os.homedir(), '.goc-agent', 'auth.json');
        this.client = axios_1.default.create({
            baseURL: this.baseURL,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
        });
        // Load saved token
        this.loadToken();
        // Add request interceptor for authentication
        this.client.interceptors.request.use((config) => {
            if (this.token) {
                config.headers.Authorization = `Bearer ${this.token}`;
            }
            return config;
        });
        // Add response interceptor for error handling
        this.client.interceptors.response.use((response) => response, async (error) => {
            if (error.response?.status === 401) {
                // Token expired or invalid
                this.clearToken();
            }
            return Promise.reject(error);
        });
    }
    loadToken() {
        try {
            if (fs.existsSync(this.configPath)) {
                const config = JSON.parse(fs.readFileSync(this.configPath, 'utf8'));
                this.token = config.token;
            }
        }
        catch (error) {
            // Ignore errors, token will remain null
        }
    }
    saveToken(token) {
        try {
            const configDir = path.dirname(this.configPath);
            if (!fs.existsSync(configDir)) {
                fs.mkdirSync(configDir, { recursive: true });
            }
            const config = { token, savedAt: new Date().toISOString() };
            fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2));
            this.token = token;
        }
        catch (error) {
            console.warn('Failed to save authentication token:', error);
        }
    }
    clearToken() {
        try {
            if (fs.existsSync(this.configPath)) {
                fs.unlinkSync(this.configPath);
            }
        }
        catch (error) {
            // Ignore errors
        }
        this.token = null;
    }
    isAuthenticated() {
        return this.token !== null;
    }
    async register(name, email, password) {
        try {
            const response = await this.client.post('/auth/register', {
                name,
                email,
                password,
                password_confirmation: password,
            });
            if (response.data.success && response.data.token) {
                this.saveToken(response.data.token);
            }
            return {
                success: response.data.success,
                token: response.data.token,
                user: response.data.user,
                message: response.data.message,
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Registration failed',
            };
        }
    }
    async login(email, password) {
        try {
            const response = await this.client.post('/auth/login', {
                email,
                password,
            });
            if (response.data.success && response.data.token) {
                this.saveToken(response.data.token);
            }
            return {
                success: response.data.success,
                token: response.data.token,
                user: response.data.user,
                message: response.data.message,
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Login failed',
            };
        }
    }
    async logout() {
        try {
            if (this.isAuthenticated()) {
                await this.client.post('/auth/logout');
            }
        }
        catch (error) {
            // Ignore logout errors
        }
        finally {
            this.clearToken();
        }
    }
    async getCurrentUser() {
        try {
            const response = await this.client.get('/auth/user');
            return response.data.user;
        }
        catch (error) {
            throw new Error(error.response?.data?.message || 'Failed to get user profile');
        }
    }
    async createSession(title) {
        try {
            const response = await this.client.post('/sessions', {
                title: title || `Session ${new Date().toLocaleString()}`,
            });
            return response.data.session;
        }
        catch (error) {
            throw new Error(error.response?.data?.message || 'Failed to create session');
        }
    }
    async getSessions() {
        try {
            const response = await this.client.get('/sessions');
            return response.data.sessions || [];
        }
        catch (error) {
            throw new Error(error.response?.data?.message || 'Failed to get sessions');
        }
    }
    async getSession(sessionId) {
        try {
            const response = await this.client.get(`/sessions/${sessionId}`);
            return response.data.session;
        }
        catch (error) {
            throw new Error(error.response?.data?.message || 'Failed to get session');
        }
    }
    async updateSession(sessionId, updates) {
        try {
            const response = await this.client.put(`/sessions/${sessionId}`, updates);
            return response.data.session;
        }
        catch (error) {
            throw new Error(error.response?.data?.message || 'Failed to update session');
        }
    }
    async deleteSession(sessionId) {
        try {
            await this.client.delete(`/sessions/${sessionId}`);
        }
        catch (error) {
            throw new Error(error.response?.data?.message || 'Failed to delete session');
        }
    }
    async chat(request) {
        try {
            const response = await this.client.post('/agent/chat', request);
            return response.data;
        }
        catch (error) {
            throw new Error(error.response?.data?.message || 'Chat request failed');
        }
    }
    async executeTask(task, sessionId, provider, model, autoMode) {
        try {
            const response = await this.client.post('/agent/task', {
                task,
                session_id: sessionId,
                provider,
                model,
                auto_mode: autoMode,
            });
            return response.data;
        }
        catch (error) {
            throw new Error(error.response?.data?.message || 'Task execution failed');
        }
    }
    async getProviders() {
        try {
            const response = await this.client.get('/agent/providers');
            return response.data;
        }
        catch (error) {
            throw new Error(error.response?.data?.message || 'Failed to get providers');
        }
    }
    async getStatus() {
        try {
            const response = await this.client.get('/agent/status');
            return response.data;
        }
        catch (error) {
            return { status: 'error', message: 'Backend not available' };
        }
    }
    async executeTool(tool, parameters = {}, sessionId) {
        try {
            const response = await this.client.post(`/agent/tools/${tool}`, {
                parameters,
                session_id: sessionId,
            });
            return response.data;
        }
        catch (error) {
            throw new Error(error.response?.data?.message || 'Tool execution failed');
        }
    }
    async healthCheck() {
        try {
            const response = await this.client.get('/health');
            return response.data.status === 'ok';
        }
        catch (error) {
            return false;
        }
    }
    // API Key Management
    async getApiKeys() {
        try {
            const response = await this.client.get('/api-keys');
            return response.data.api_keys || [];
        }
        catch (error) {
            throw new Error(error.response?.data?.message || 'Failed to get API keys');
        }
    }
    async createApiKey(name) {
        try {
            const response = await this.client.post('/api-keys', { name });
            return response.data.api_key;
        }
        catch (error) {
            throw new Error(error.response?.data?.message || 'Failed to create API key');
        }
    }
    async deleteApiKey(keyId) {
        try {
            await this.client.delete(`/api-keys/${keyId}`);
        }
        catch (error) {
            throw new Error(error.response?.data?.message || 'Failed to delete API key');
        }
    }
    // Context Management
    async indexProject(projectPath) {
        try {
            const response = await this.client.post('/context/index', {
                project_path: projectPath,
            });
            return response.data;
        }
        catch (error) {
            throw new Error(error.response?.data?.message || 'Failed to index project');
        }
    }
    async searchCode(query, projectPath) {
        try {
            const response = await this.client.post('/context/search', {
                query,
                project_path: projectPath,
            });
            return response.data;
        }
        catch (error) {
            throw new Error(error.response?.data?.message || 'Code search failed');
        }
    }
    async getProjectStats(projectPath) {
        try {
            const response = await this.client.get('/context/stats', {
                params: { project_path: projectPath },
            });
            return response.data;
        }
        catch (error) {
            throw new Error(error.response?.data?.message || 'Failed to get project stats');
        }
    }
    // Learning Analytics
    async recordLearningEvent(event) {
        try {
            await this.client.post('/learning/events', event);
        }
        catch (error) {
            // Silently fail for learning events
        }
    }
    async getLearningMetrics() {
        try {
            const response = await this.client.get('/learning/metrics');
            return response.data;
        }
        catch (error) {
            throw new Error(error.response?.data?.message || 'Failed to get learning metrics');
        }
    }
    async getLearningAnalytics() {
        try {
            const response = await this.client.get('/learning/analytics');
            return response.data;
        }
        catch (error) {
            throw new Error(error.response?.data?.message || 'Failed to get learning analytics');
        }
    }
    // Utility Methods
    setBaseURL(url) {
        this.baseURL = url;
        this.client.defaults.baseURL = url;
    }
    getBaseURL() {
        return this.baseURL;
    }
    getToken() {
        return this.token;
    }
}
exports.BackendAPIClient = BackendAPIClient;
//# sourceMappingURL=BackendAPIClient.js.map