"use strict";
/**
 * Learning Engine
 *
 * Core learning system that orchestrates pattern recognition, knowledge storage,
 * and context enhancement across all AI providers
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.LearningEngine = void 0;
const pattern_recognition_1 = require("./pattern-recognition");
const knowledge_base_1 = require("./knowledge-base");
const context_enhancer_1 = require("./context-enhancer");
const learning_monitor_1 = require("./learning-monitor");
const web_learner_1 = require("./web-learner");
const utils_1 = require("../utils");
class LearningEngine {
    constructor(options) {
        this.initialized = false;
        this.config = options.config;
        this.userId = options.userId;
        this.projectId = options.projectId;
        this.enableAutoLearning = options.enableAutoLearning ?? true;
        // Initialize components
        this.patternRecognizer = new pattern_recognition_1.PatternRecognizer(this.config);
        this.knowledgeBase = new knowledge_base_1.KnowledgeBase(this.config);
        this.contextEnhancer = new context_enhancer_1.ContextEnhancer(this.config);
        this.learningMonitor = new learning_monitor_1.LearningMonitor(this.config);
        this.webLearner = new web_learner_1.WebLearner(this.config);
    }
    async initialize() {
        if (this.initialized)
            return;
        try {
            utils_1.logger.info('Initializing Learning Engine', 'LearningEngine');
            // Initialize all components
            await Promise.all([
                this.knowledgeBase.initialize(),
                this.patternRecognizer.initialize(),
                this.contextEnhancer.initialize(),
                this.learningMonitor.initialize(),
                this.webLearner.initialize()
            ]);
            this.initialized = true;
            utils_1.logger.info('Learning Engine initialized successfully', 'LearningEngine');
        }
        catch (error) {
            utils_1.logger.error('Failed to initialize Learning Engine', 'LearningEngine', { error });
            throw error;
        }
    }
    /**
     * Learn from user interaction with AI response
     */
    async learnFromInteraction(messages, response, userFeedback) {
        if (!this.config.learning.enabled || !this.enableAutoLearning)
            return;
        try {
            // Extract patterns from the interaction
            const patterns = await this.patternRecognizer.extractPatterns({
                messages,
                response,
                userFeedback,
                userId: this.userId,
                projectId: this.projectId
            });
            // Store learned patterns
            for (const pattern of patterns) {
                await this.knowledgeBase.storePattern(pattern);
            }
            // Record learning event
            await this.recordLearningEvent({
                type: userFeedback?.accepted ? 'code-accepted' : 'code-generated',
                data: {
                    messageCount: messages.length,
                    responseLength: response.content.length,
                    feedback: userFeedback
                },
                patterns: patterns.map(p => p.id),
                confidence: patterns.reduce((sum, p) => sum + p.confidence, 0) / patterns.length
            });
            utils_1.logger.debug(`Learned ${patterns.length} patterns from interaction`, 'LearningEngine');
        }
        catch (error) {
            utils_1.logger.error('Failed to learn from interaction', 'LearningEngine', { error });
        }
    }
    /**
     * Enhance AI messages with learned context and patterns
     */
    async enhanceMessages(messages) {
        if (!this.config.learning.enabled)
            return messages;
        try {
            return await this.contextEnhancer.enhanceMessages(messages, {
                userId: this.userId,
                projectId: this.projectId
            });
        }
        catch (error) {
            utils_1.logger.error('Failed to enhance messages', 'LearningEngine', { error });
            return messages;
        }
    }
    /**
     * Learn from web research results
     */
    async learnFromWebResearch(query, results, relevantContent) {
        if (!this.config.learning.enabled || !this.config.learning.enableWebLearning)
            return;
        try {
            await this.webLearner.learnFromResults(query, results, relevantContent, {
                userId: this.userId,
                projectId: this.projectId
            });
        }
        catch (error) {
            utils_1.logger.error('Failed to learn from web research', 'LearningEngine', { error });
        }
    }
    /**
     * Get learning metrics and analytics
     */
    async getMetrics() {
        return await this.learningMonitor.getMetrics({
            userId: this.userId,
            projectId: this.projectId
        });
    }
    /**
     * Get user preferences
     */
    async getUserPreferences() {
        if (!this.userId)
            return [];
        return await this.knowledgeBase.getUserPreferences(this.userId);
    }
    /**
     * Update user preference
     */
    async updateUserPreference(preference) {
        if (!this.userId)
            return;
        await this.knowledgeBase.updateUserPreference(this.userId, preference);
    }
    /**
     * Search for relevant patterns
     */
    async searchPatterns(query, options) {
        return await this.knowledgeBase.searchPatterns(query, {
            userId: this.userId,
            projectId: this.projectId,
            ...options
        });
    }
    /**
     * Record a learning event
     */
    async recordLearningEvent(event) {
        const learningEvent = {
            id: this.generateId(),
            timestamp: new Date(),
            userId: this.userId,
            projectId: this.projectId,
            ...event
        };
        await this.learningMonitor.recordEvent(learningEvent);
    }
    /**
     * Generate unique ID
     */
    generateId() {
        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * Dispose of resources
     */
    async dispose() {
        if (!this.initialized)
            return;
        await Promise.all([
            this.knowledgeBase.dispose(),
            this.patternRecognizer.dispose(),
            this.contextEnhancer.dispose(),
            this.learningMonitor.dispose(),
            this.webLearner.dispose()
        ]);
        this.initialized = false;
    }
}
exports.LearningEngine = LearningEngine;
//# sourceMappingURL=learning-engine.js.map