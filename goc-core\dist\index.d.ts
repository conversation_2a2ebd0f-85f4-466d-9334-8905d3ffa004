/**
 * GOC Core - Main Entry Point
 *
 * The core library for the GOC Agent ecosystem, providing AI providers,
 * context engine, file operations, web research, and configuration management.
 */
export * from './types';
export * from './ai';
export * from './context';
export * from './files';
export * from './web';
export * from './config';
export * from './learning';
export * from './auto';
export * from './generation';
export * from './monitoring';
export * from './documentation';
export * from './deployment';
export * from './utils';
import { GocCoreOptions, GocConfig, AIMessage, AIResponse } from './types';
import { LearningEngine } from './learning/learning-engine';
import { ContextEngine } from './context';
import { WebResearcher } from './web';
import { AutoModeEngine } from './auto';
import { CodeGenerator } from './generation';
import { MonitoringSystem } from './monitoring';
import { DocumentationGenerator } from './documentation';
import { DeploymentAssistant } from './deployment';
export declare class GocCore {
    private config;
    private configLoader;
    private learningEngine?;
    private contextEngine?;
    private webResearcher?;
    private autoModeEngine?;
    private codeGenerator?;
    private monitoringSystem?;
    private documentationGenerator?;
    private deploymentAssistant?;
    private initialized;
    constructor(options?: GocCoreOptions);
    initialize(): Promise<void>;
    isInitialized(): boolean;
    dispose(): Promise<void>;
    getConfig(): GocConfig;
    updateConfig(updates: GocCoreOptions): void;
    /**
     * Get the learning engine instance
     */
    getLearningEngine(): LearningEngine | undefined;
    /**
     * Get the context engine instance
     */
    getContextEngine(): ContextEngine;
    /**
     * Get the web researcher instance
     */
    getWebResearcher(): WebResearcher;
    /**
     * Get the auto mode engine instance
     */
    getAutoModeEngine(): AutoModeEngine;
    /**
     * Get the code generator instance
     */
    getCodeGenerator(): CodeGenerator;
    /**
     * Get the monitoring system instance
     */
    getMonitoringSystem(): MonitoringSystem;
    /**
     * Get the documentation generator instance
     */
    getDocumentationGenerator(): DocumentationGenerator;
    /**
     * Get the deployment assistant instance
     */
    getDeploymentAssistant(): DeploymentAssistant;
    /**
     * Learn from AI interaction
     */
    learnFromInteraction(messages: AIMessage[], response: AIResponse, userFeedback?: {
        accepted: boolean;
        modifications?: string;
        rating?: number;
    }): Promise<void>;
    /**
     * Enhance messages with learned context
     */
    enhanceMessages(messages: AIMessage[]): Promise<AIMessage[]>;
    /**
     * Learn from web research
     */
    learnFromWebResearch(query: string, results: any[], relevantContent: string[]): Promise<void>;
    /**
     * Get learning metrics
     */
    getLearningMetrics(): Promise<any>;
    private mergeConfig;
}
export declare function createGocCore(options?: GocCoreOptions): Promise<GocCore>;
//# sourceMappingURL=index.d.ts.map