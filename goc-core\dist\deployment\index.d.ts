/**
 * Deployment Assistance System
 *
 * Intelligent deployment automation and infrastructure management
 */
export interface DeploymentTarget {
    name: string;
    type: 'docker' | 'kubernetes' | 'serverless' | 'vm' | 'static' | 'paas';
    provider: 'aws' | 'gcp' | 'azure' | 'digitalocean' | 'heroku' | 'vercel' | 'netlify' | 'local';
    configuration: Record<string, any>;
    environment: 'development' | 'staging' | 'production';
}
export interface DeploymentPlan {
    id: string;
    projectPath: string;
    target: DeploymentTarget;
    steps: DeploymentStep[];
    estimatedDuration: number;
    requirements: string[];
    warnings: string[];
    createdAt: Date;
}
export interface DeploymentStep {
    id: string;
    name: string;
    description: string;
    type: 'build' | 'test' | 'package' | 'upload' | 'configure' | 'deploy' | 'verify';
    command?: string;
    script?: string;
    dependencies: string[];
    estimatedDuration: number;
    required: boolean;
}
export interface DeploymentResult {
    success: boolean;
    planId: string;
    completedSteps: string[];
    failedStep?: string;
    error?: string;
    deploymentUrl?: string;
    logs: Array<{
        step: string;
        level: 'info' | 'warn' | 'error';
        message: string;
        timestamp: Date;
    }>;
    duration: number;
}
export interface InfrastructureTemplate {
    name: string;
    description: string;
    type: 'docker' | 'kubernetes' | 'terraform' | 'cloudformation' | 'compose';
    template: string;
    variables: Array<{
        name: string;
        description: string;
        type: string;
        required: boolean;
        default?: any;
    }>;
    dependencies: string[];
}
/**
 * Intelligent Deployment Assistant
 */
export declare class DeploymentAssistant {
    private templates;
    private activeDeployments;
    constructor();
    /**
     * Analyze project and suggest deployment options
     */
    analyzeProject(projectPath: string): Promise<{
        projectType: string;
        framework: string;
        dependencies: string[];
        suggestedTargets: DeploymentTarget[];
        requirements: string[];
    }>;
    /**
     * Create deployment plan
     */
    createDeploymentPlan(projectPath: string, target: DeploymentTarget): Promise<DeploymentPlan>;
    /**
     * Execute deployment plan
     */
    executeDeployment(planId: string): Promise<DeploymentResult>;
    /**
     * Generate infrastructure templates
     */
    generateInfrastructure(projectType: string, target: DeploymentTarget, options?: {
        includeDatabase?: boolean;
        includeCache?: boolean;
        includeLoadBalancer?: boolean;
        includeMonitoring?: boolean;
    }): Promise<InfrastructureTemplate[]>;
    /**
     * Get deployment status
     */
    getDeploymentStatus(planId: string): {
        plan: DeploymentPlan | null;
        status: 'pending' | 'running' | 'completed' | 'failed';
    };
    /**
     * List available deployment templates
     */
    getAvailableTemplates(): InfrastructureTemplate[];
    private scanProject;
    private suggestDeploymentTargets;
    private getDeploymentRequirements;
    private getTargetRequirements;
    private getDeploymentWarnings;
    private generateDeploymentSteps;
    private executeStep;
    private getDeploymentUrl;
    private getBuildCommand;
    private getTestCommand;
    private getDeployCommand;
    private generateDockerfile;
    private generateDockerCompose;
    private generateKubernetesManifests;
    private generateTerraformConfig;
    private initializeTemplates;
}
//# sourceMappingURL=index.d.ts.map