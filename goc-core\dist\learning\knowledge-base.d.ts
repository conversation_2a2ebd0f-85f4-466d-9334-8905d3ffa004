/**
 * Knowledge Base
 *
 * Persistent storage and retrieval system for learned patterns, user preferences,
 * and contextual knowledge
 */
import { LearningPattern, UserPreference, PatternType, GocConfig } from '../types';
export interface SearchOptions {
    userId?: string;
    projectId?: string;
    type?: PatternType;
    limit?: number;
    minConfidence?: number;
    tags?: string[];
}
export declare class KnowledgeBase {
    private config;
    private storageDir;
    private patternsFile;
    private preferencesFile;
    private indexFile;
    private patterns;
    private preferences;
    private patternIndex;
    private initialized;
    constructor(config: GocConfig);
    initialize(): Promise<void>;
    /**
     * Store a learning pattern
     */
    storePattern(pattern: LearningPattern): Promise<void>;
    /**
     * Search for patterns
     */
    searchPatterns(query: string, options?: SearchOptions): Promise<LearningPattern[]>;
    /**
     * Get user preferences
     */
    getUserPreferences(userId: string): Promise<UserPreference[]>;
    /**
     * Update user preference
     */
    updateUserPreference(userId: string, preference: Partial<UserPreference>): Promise<void>;
    /**
     * Get patterns by type
     */
    getPatternsByType(type: PatternType, options?: SearchOptions): Promise<LearningPattern[]>;
    /**
     * Get most frequent patterns
     */
    getMostFrequentPatterns(limit?: number, options?: SearchOptions): Promise<LearningPattern[]>;
    /**
     * Get recent patterns
     */
    getRecentPatterns(limit?: number, options?: SearchOptions): Promise<LearningPattern[]>;
    /**
     * Delete pattern
     */
    deletePattern(patternId: string): Promise<boolean>;
    /**
     * Get storage statistics
     */
    getStatistics(): {
        totalPatterns: number;
        patternsByType: Record<PatternType, number>;
        totalUsers: number;
        totalPreferences: number;
        storageSize: number;
    };
    private findSimilarPattern;
    private updateIndex;
    private calculateRelevanceScore;
    private loadPatterns;
    private savePatterns;
    private loadPreferences;
    private savePreferences;
    private loadIndex;
    private saveIndex;
    private cleanupOldPatterns;
    private estimateStorageSize;
    private generateId;
    dispose(): Promise<void>;
}
//# sourceMappingURL=knowledge-base.d.ts.map