"use strict";
/**
 * Terminal Manager Tool
 *
 * Implements read-terminal for terminal access and interaction
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalManager = void 0;
const child_process_1 = require("child_process");
class TerminalManager {
    constructor() {
        this.terminals = new Map();
        this.terminalCounter = 0;
    }
    /**
     * Create a new terminal session
     */
    async createTerminal(options = {}) {
        try {
            const terminalId = `term_${++this.terminalCounter}_${Date.now()}`;
            const { shell = this.getDefaultShell(), cwd = process.cwd(), env = process.env, rows = 24, cols = 80 } = options;
            const terminalProcess = (0, child_process_1.spawn)(shell, [], {
                cwd,
                env: env,
                stdio: ['pipe', 'pipe', 'pipe']
            });
            const session = {
                id: terminalId,
                shell,
                cwd,
                process: terminalProcess,
                output: '',
                isActive: true,
                createdAt: new Date(),
                lastActivity: new Date()
            };
            // Handle output
            terminalProcess.stdout?.on('data', (data) => {
                session.output += data.toString();
                session.lastActivity = new Date();
            });
            terminalProcess.stderr?.on('data', (data) => {
                session.output += data.toString();
                session.lastActivity = new Date();
            });
            terminalProcess.on('close', () => {
                session.isActive = false;
            });
            this.terminals.set(terminalId, session);
            return {
                success: true,
                data: {
                    terminalId,
                    shell,
                    cwd,
                    pid: terminalProcess.pid
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Read terminal output
     */
    async readTerminal(terminalId, options = {}) {
        try {
            if (terminalId) {
                const session = this.terminals.get(terminalId);
                if (!session) {
                    return {
                        success: false,
                        error: `Terminal not found: ${terminalId}`
                    };
                }
                const output = session.output;
                if (options.clear) {
                    session.output = '';
                }
                return {
                    success: true,
                    data: {
                        terminalId,
                        output,
                        isActive: session.isActive,
                        lastActivity: session.lastActivity
                    }
                };
            }
            else {
                // Read from most recent active terminal
                const activeSessions = Array.from(this.terminals.values())
                    .filter(s => s.isActive)
                    .sort((a, b) => b.lastActivity.getTime() - a.lastActivity.getTime());
                if (activeSessions.length === 0) {
                    return {
                        success: false,
                        error: 'No active terminals found'
                    };
                }
                const session = activeSessions[0];
                const output = session.output;
                if (options.clear) {
                    session.output = '';
                }
                return {
                    success: true,
                    data: {
                        terminalId: session.id,
                        output,
                        isActive: session.isActive,
                        lastActivity: session.lastActivity
                    }
                };
            }
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Write to terminal
     */
    async writeTerminal(terminalId, command) {
        try {
            const session = this.terminals.get(terminalId);
            if (!session) {
                return {
                    success: false,
                    error: `Terminal not found: ${terminalId}`
                };
            }
            if (!session.isActive) {
                return {
                    success: false,
                    error: `Terminal is not active: ${terminalId}`
                };
            }
            if (!session.process.stdin) {
                return {
                    success: false,
                    error: `Terminal stdin not available: ${terminalId}`
                };
            }
            // Add newline if not present
            const commandToSend = command.endsWith('\n') ? command : command + '\n';
            session.process.stdin.write(commandToSend);
            session.lastActivity = new Date();
            return {
                success: true,
                data: {
                    terminalId,
                    command: commandToSend.trim(),
                    timestamp: session.lastActivity
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Execute command in terminal and wait for output
     */
    async executeCommand(terminalId, command, timeoutMs = 10000) {
        try {
            const session = this.terminals.get(terminalId);
            if (!session) {
                return {
                    success: false,
                    error: `Terminal not found: ${terminalId}`
                };
            }
            const initialOutputLength = session.output.length;
            // Send command
            const writeResult = await this.writeTerminal(terminalId, command);
            if (!writeResult.success) {
                return writeResult;
            }
            // Wait for output
            return new Promise((resolve) => {
                const startTime = Date.now();
                const checkOutput = () => {
                    const currentTime = Date.now();
                    if (currentTime - startTime > timeoutMs) {
                        resolve({
                            success: false,
                            error: `Command timeout after ${timeoutMs}ms`
                        });
                        return;
                    }
                    const newOutput = session.output.substring(initialOutputLength);
                    // Simple heuristic: if output contains prompt-like pattern, command is done
                    if (newOutput.includes('$') || newOutput.includes('>') || newOutput.includes('#')) {
                        resolve({
                            success: true,
                            data: {
                                terminalId,
                                command,
                                output: newOutput,
                                executionTime: currentTime - startTime
                            }
                        });
                        return;
                    }
                    // Check again in 100ms
                    setTimeout(checkOutput, 100);
                };
                checkOutput();
            });
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Close terminal
     */
    async closeTerminal(terminalId) {
        try {
            const session = this.terminals.get(terminalId);
            if (!session) {
                return {
                    success: false,
                    error: `Terminal not found: ${terminalId}`
                };
            }
            if (session.isActive) {
                session.process.kill('SIGTERM');
                session.isActive = false;
            }
            this.terminals.delete(terminalId);
            return {
                success: true,
                data: {
                    terminalId,
                    closed: true
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * List all terminals
     */
    listTerminals() {
        const terminals = Array.from(this.terminals.values()).map(session => ({
            id: session.id,
            shell: session.shell,
            cwd: session.cwd,
            isActive: session.isActive,
            createdAt: session.createdAt,
            lastActivity: session.lastActivity,
            outputLength: session.output.length
        }));
        return {
            success: true,
            data: {
                terminals,
                active: terminals.filter(t => t.isActive).length,
                total: terminals.length
            }
        };
    }
    /**
     * Get terminal info
     */
    getTerminalInfo(terminalId) {
        const session = this.terminals.get(terminalId);
        if (!session) {
            return {
                success: false,
                error: `Terminal not found: ${terminalId}`
            };
        }
        return {
            success: true,
            data: {
                id: session.id,
                shell: session.shell,
                cwd: session.cwd,
                isActive: session.isActive,
                createdAt: session.createdAt,
                lastActivity: session.lastActivity,
                outputLength: session.output.length,
                pid: session.process.pid
            }
        };
    }
    /**
     * Get default shell based on platform
     */
    getDefaultShell() {
        const platform = process.platform;
        if (platform === 'win32') {
            return process.env.COMSPEC || 'cmd.exe';
        }
        else {
            return process.env.SHELL || '/bin/bash';
        }
    }
    /**
     * Cleanup inactive terminals
     */
    cleanup() {
        const cutoff = new Date(Date.now() - 60 * 60 * 1000); // 1 hour ago
        for (const [id, session] of this.terminals.entries()) {
            if (!session.isActive && session.lastActivity < cutoff) {
                this.terminals.delete(id);
            }
        }
    }
}
exports.TerminalManager = TerminalManager;
//# sourceMappingURL=terminal.js.map