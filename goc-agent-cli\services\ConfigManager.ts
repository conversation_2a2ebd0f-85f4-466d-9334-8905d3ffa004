import * as fs from 'fs-extra';
import * as path from 'path';
import * as yaml from 'yaml';
import { GocConfig } from '@goc-agent/core';

export interface CLIConfig {
  defaultProvider?: string;
  defaultModel?: string;
  apiKeys?: {
    [provider: string]: string;
  };
  backend?: {
    url: string;
    apiKey?: string;
  };
  preferences?: {
    theme?: string;
    verbose?: boolean;
    autoSave?: boolean;
  };
}

export class ConfigManager {
  private configPath: string;
  private config: CLIConfig = {};

  constructor(configPath?: string) {
    this.configPath = configPath || this.getDefaultConfigPath();
  }

  private getDefaultConfigPath(): string {
    const homeDir = process.env.HOME || process.env.USERPROFILE || '';
    return path.join(homeDir, '.goc-agent', 'config.yaml');
  }

  async loadConfig(): Promise<CLIConfig> {
    try {
      if (await fs.pathExists(this.configPath)) {
        const content = await fs.readFile(this.configPath, 'utf8');
        this.config = yaml.parse(content) || {};
      }
    } catch (error) {
      console.warn(`Failed to load config from ${this.configPath}:`, error);
    }
    return this.config;
  }

  async saveConfig(config: CLIConfig): Promise<void> {
    try {
      await fs.ensureDir(path.dirname(this.configPath));
      const content = yaml.stringify(config);
      await fs.writeFile(this.configPath, content, 'utf8');
      this.config = config;
    } catch (error) {
      throw new Error(`Failed to save config to ${this.configPath}: ${error}`);
    }
  }

  getConfig(): CLIConfig {
    return { ...this.config };
  }

  updateConfig(updates: Partial<CLIConfig>): void {
    this.config = { ...this.config, ...updates };
  }

  getApiKey(provider: string): string | undefined {
    return this.config.apiKeys?.[provider];
  }

  setApiKey(provider: string, apiKey: string): void {
    if (!this.config.apiKeys) {
      this.config.apiKeys = {};
    }
    this.config.apiKeys[provider] = apiKey;
  }

  getBackendConfig(): { url: string; apiKey?: string } | undefined {
    return this.config.backend;
  }

  setBackendConfig(url: string, apiKey?: string): void {
    this.config.backend = { url, apiKey };
  }

  getDefaultProvider(): string | undefined {
    return this.config.defaultProvider;
  }

  setDefaultProvider(provider: string): void {
    this.config.defaultProvider = provider;
  }

  getDefaultModel(): string | undefined {
    return this.config.defaultModel;
  }

  setDefaultModel(model: string): void {
    this.config.defaultModel = model;
  }

  isVerbose(): boolean {
    return this.config.preferences?.verbose || false;
  }

  setVerbose(verbose: boolean): void {
    if (!this.config.preferences) {
      this.config.preferences = {};
    }
    this.config.preferences.verbose = verbose;
  }
}
