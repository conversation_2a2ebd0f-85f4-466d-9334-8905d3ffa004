#!/usr/bin/env node

/**
 * Build and Run Script for GOC IDE
 * Handles the complete build process and launches the IDE
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 GOC IDE - Build and Run Script');
console.log('=====================================\n');

async function runCommand(command, description) {
  console.log(`📦 ${description}...`);
  
  return new Promise((resolve, reject) => {
    const child = spawn(command, { shell: true, stdio: 'inherit' });
    
    child.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ ${description} completed successfully\n`);
        resolve();
      } else {
        console.error(`❌ ${description} failed with code ${code}\n`);
        reject(new Error(`${description} failed`));
      }
    });
    
    child.on('error', (error) => {
      console.error(`❌ ${description} error:`, error.message);
      reject(error);
    });
  });
}

async function checkDependencies() {
  console.log('🔍 Checking dependencies...');
  
  if (!fs.existsSync('node_modules')) {
    console.log('📦 Installing dependencies...');
    await runCommand('npm install', 'Installing dependencies');
  } else {
    console.log('✅ Dependencies already installed\n');
  }
}

async function buildProject() {
  console.log('🔨 Building project...');
  
  // Build main process
  await runCommand('npm run build:main', 'Building main process');
  
  // Build renderer process
  await runCommand('npm run build:renderer', 'Building renderer process');
}

async function startIDE() {
  console.log('🚀 Starting GOC IDE...');
  
  const child = spawn('npm start', { shell: true, stdio: 'inherit' });
  
  child.on('close', (code) => {
    console.log(`\n🔄 GOC IDE exited with code ${code}`);
  });
  
  child.on('error', (error) => {
    console.error('❌ Failed to start GOC IDE:', error.message);
  });
}

async function main() {
  try {
    await checkDependencies();
    await buildProject();
    await startIDE();
  } catch (error) {
    console.error('❌ Build process failed:', error.message);
    process.exit(1);
  }
}

// Run the script
main();
