/**
 * Auto Mode System
 *
 * Intelligent autonomous task execution and decision making
 */
import { ContextEngine } from '../context';
import { WebResearcher } from '../web';
export interface AutoTask {
    id: string;
    type: 'code_analysis' | 'web_research' | 'file_operation' | 'context_search' | 'learning';
    description: string;
    input: any;
    output?: any;
    status: 'pending' | 'running' | 'completed' | 'failed';
    priority: number;
    dependencies: string[];
    estimatedDuration: number;
    actualDuration?: number;
    createdAt: Date;
    startedAt?: Date;
    completedAt?: Date;
    error?: string;
}
export interface AutoPlan {
    id: string;
    goal: string;
    tasks: AutoTask[];
    status: 'planning' | 'executing' | 'completed' | 'failed';
    progress: number;
    createdAt: Date;
    estimatedCompletion: Date;
    actualCompletion?: Date;
}
export interface AutoModeOptions {
    maxConcurrentTasks?: number;
    timeoutMinutes?: number;
    enableLearning?: boolean;
    enableWebResearch?: boolean;
    enableFileOperations?: boolean;
    safeMode?: boolean;
}
export interface DecisionContext {
    goal: string;
    availableTools: string[];
    currentContext: any;
    previousActions: string[];
    constraints: string[];
}
/**
 * Intelligent Auto Mode Engine
 */
export declare class AutoModeEngine {
    private contextEngine;
    private webResearcher;
    private activePlans;
    private runningTasks;
    private options;
    constructor(contextEngine: ContextEngine, webResearcher: WebResearcher, options?: AutoModeOptions);
    /**
     * Create and execute an autonomous plan for a given goal
     */
    executeGoal(goal: string, context?: any): Promise<AutoPlan>;
    /**
     * Create an intelligent execution plan for a goal
     */
    createPlan(goal: string, context: any): Promise<AutoPlan>;
    /**
     * Execute a plan with intelligent task scheduling
     */
    executePlan(plan: AutoPlan): Promise<void>;
    /**
     * Execute a single task with intelligent decision making
     */
    executeTask(task: AutoTask, plan: AutoPlan): Promise<void>;
    /**
     * Make intelligent decisions based on context
     */
    makeDecision(context: DecisionContext): Promise<{
        action: string;
        reasoning: string;
        confidence: number;
        alternatives: string[];
    }>;
    /**
     * Get current auto mode status
     */
    getStatus(): {
        activePlans: number;
        runningTasks: number;
        totalTasksCompleted: number;
        averageTaskDuration: number;
    };
    /**
     * Stop all auto mode execution
     */
    stopAll(): Promise<void>;
    private analyzeGoalAndCreateTasks;
    private optimizeTasks;
    private hasRemainingTasks;
    private getExecutableTasks;
    private waitForTaskCompletion;
    private updatePlanProgress;
    private shouldContinueAfterFailure;
    private executeCodeAnalysis;
    private executeWebResearch;
    private executeContextSearch;
    private executeLearning;
    private executeFileOperation;
}
//# sourceMappingURL=index.d.ts.map