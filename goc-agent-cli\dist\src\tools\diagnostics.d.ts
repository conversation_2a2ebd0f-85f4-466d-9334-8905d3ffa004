/**
 * Diagnostics Provider Tool
 *
 * Implements IDE error detection and code analysis
 */
import { ToolResult } from './index';
export interface Diagnostic {
    file: string;
    line: number;
    column: number;
    severity: 'error' | 'warning' | 'info' | 'hint';
    message: string;
    code?: string;
    source?: string;
    range?: {
        start: {
            line: number;
            character: number;
        };
        end: {
            line: number;
            character: number;
        };
    };
}
export interface DiagnosticSummary {
    totalFiles: number;
    totalIssues: number;
    errors: number;
    warnings: number;
    infos: number;
    hints: number;
    fileIssues: Record<string, Diagnostic[]>;
}
export declare class DiagnosticsProvider {
    private diagnostics;
    /**
     * Get diagnostics for specific files
     */
    getDiagnostics(filePaths: string[]): Promise<ToolResult>;
    /**
     * Analyze a single file for issues
     */
    private analyzeFile;
    /**
     * Analyze JavaScript/TypeScript files
     */
    private analyzeJavaScript;
    /**
     * Analyze Python files
     */
    private analyzePython;
    /**
     * Analyze PHP files
     */
    private analyzePHP;
    /**
     * Analyze JSON files
     */
    private analyzeJSON;
    /**
     * Analyze YAML files
     */
    private analyzeYAML;
    /**
     * Generic file analysis
     */
    private analyzeGeneric;
    /**
     * Helper: Check if variable is used
     */
    private isVariableUsed;
    /**
     * Helper: Check if import exists
     */
    private hasImport;
    /**
     * Clear diagnostics for a file
     */
    clearDiagnostics(filePath: string): void;
    /**
     * Get all cached diagnostics
     */
    getAllDiagnostics(): Record<string, Diagnostic[]>;
}
//# sourceMappingURL=diagnostics.d.ts.map