/**
 * Command Palette Component - Quick command access
 */

export class CommandPalette {
  private isVisible = false;
  private commands: Array<{ id: string; label: string; action: () => void }> = [];

  public async initialize(): Promise<void> {
    console.log('Initializing Command Palette...');
    this.setupCommands();
    this.createPaletteElement();
    console.log('Command Palette initialized');
  }

  private setupCommands(): void {
    this.commands = [
      {
        id: 'file.new',
        label: 'File: New File',
        action: () => console.log('New file command')
      },
      {
        id: 'file.open',
        label: 'File: Open File',
        action: () => console.log('Open file command')
      },
      {
        id: 'file.save',
        label: 'File: Save',
        action: () => console.log('Save file command')
      },
      {
        id: 'goc.chat',
        label: 'GOC Agent: Open Chat',
        action: () => console.log('Open GOC chat command')
      },
      {
        id: 'goc.explain',
        label: 'GOC Agent: Explain Code',
        action: () => console.log('Explain code command')
      }
    ];
  }

  private createPaletteElement(): void {
    const palette = document.createElement('div');
    palette.id = 'command-palette';
    palette.className = 'command-palette hidden';
    palette.innerHTML = `
      <div class="command-palette-overlay"></div>
      <div class="command-palette-container">
        <input type="text" class="command-palette-input" placeholder="Type a command...">
        <div class="command-palette-results"></div>
      </div>
    `;

    document.body.appendChild(palette);
    this.setupPaletteEvents(palette);
  }

  private setupPaletteEvents(palette: HTMLElement): void {
    const overlay = palette.querySelector('.command-palette-overlay') as HTMLElement;
    const input = palette.querySelector('.command-palette-input') as HTMLInputElement;

    // Close on overlay click
    overlay.addEventListener('click', () => {
      this.hide();
    });

    // Close on Escape key
    input.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.hide();
      }
    });

    // Filter commands on input
    input.addEventListener('input', () => {
      this.filterCommands(input.value);
    });
  }

  private filterCommands(query: string): void {
    const results = this.commands.filter(cmd => 
      cmd.label.toLowerCase().includes(query.toLowerCase())
    );

    const resultsContainer = document.querySelector('.command-palette-results') as HTMLElement;
    if (resultsContainer) {
      resultsContainer.innerHTML = results.map(cmd => `
        <div class="command-palette-item" data-command-id="${cmd.id}">
          ${cmd.label}
        </div>
      `).join('');

      // Setup click handlers for results
      resultsContainer.querySelectorAll('.command-palette-item').forEach(item => {
        item.addEventListener('click', () => {
          const commandId = (item as HTMLElement).dataset.commandId;
          const command = this.commands.find(cmd => cmd.id === commandId);
          if (command) {
            command.action();
            this.hide();
          }
        });
      });
    }
  }

  public show(): void {
    const palette = document.getElementById('command-palette');
    if (palette) {
      palette.classList.remove('hidden');
      this.isVisible = true;

      // Focus input
      const input = palette.querySelector('.command-palette-input') as HTMLInputElement;
      if (input) {
        input.focus();
        input.value = '';
      }

      // Show all commands initially
      this.filterCommands('');
    }
  }

  public hide(): void {
    const palette = document.getElementById('command-palette');
    if (palette) {
      palette.classList.add('hidden');
      this.isVisible = false;
    }
  }

  public toggle(): void {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }
}
