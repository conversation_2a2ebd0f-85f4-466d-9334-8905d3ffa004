/**
 * File System Manager - Handles file operations and watching
 */

import * as fs from 'fs/promises';
import * as fsSync from 'fs';
import * as path from 'path';
import { watch, FSWatcher } from 'chokidar';

export class FileSystemManager {
  private watchers: Map<string, FSWatcher> = new Map();

  public initialize(): void {
    console.log('File System Manager initialized');
  }

  public async readFile(filePath: string): Promise<string> {
    try {
      return await fs.readFile(filePath, 'utf8');
    } catch (error) {
      console.error('Failed to read file:', error);
      throw new Error(`Failed to read file: ${filePath}`);
    }
  }

  public async writeFile(filePath: string, content: string): Promise<void> {
    try {
      // Ensure directory exists
      const dir = path.dirname(filePath);
      await fs.mkdir(dir, { recursive: true });
      
      await fs.writeFile(filePath, content, 'utf8');
    } catch (error) {
      console.error('Failed to write file:', error);
      throw new Error(`Failed to write file: ${filePath}`);
    }
  }

  public async readDirectory(dirPath: string): Promise<string[]> {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      return entries.map(entry => {
        const fullPath = path.join(dirPath, entry.name);
        return entry.isDirectory() ? `${fullPath}/` : fullPath;
      });
    } catch (error) {
      console.error('Failed to read directory:', error);
      throw new Error(`Failed to read directory: ${dirPath}`);
    }
  }

  public async createFile(filePath: string): Promise<void> {
    try {
      // Ensure directory exists
      const dir = path.dirname(filePath);
      await fs.mkdir(dir, { recursive: true });
      
      // Create empty file if it doesn't exist
      if (!fsSync.existsSync(filePath)) {
        await fs.writeFile(filePath, '', 'utf8');
      }
    } catch (error) {
      console.error('Failed to create file:', error);
      throw new Error(`Failed to create file: ${filePath}`);
    }
  }

  public async createDirectory(dirPath: string): Promise<void> {
    try {
      await fs.mkdir(dirPath, { recursive: true });
    } catch (error) {
      console.error('Failed to create directory:', error);
      throw new Error(`Failed to create directory: ${dirPath}`);
    }
  }

  public async deleteFile(filePath: string): Promise<void> {
    try {
      await fs.unlink(filePath);
    } catch (error) {
      console.error('Failed to delete file:', error);
      throw new Error(`Failed to delete file: ${filePath}`);
    }
  }

  public async deleteDirectory(dirPath: string): Promise<void> {
    try {
      await fs.rmdir(dirPath, { recursive: true });
    } catch (error) {
      console.error('Failed to delete directory:', error);
      throw new Error(`Failed to delete directory: ${dirPath}`);
    }
  }

  public async watchDirectory(dirPath: string): Promise<void> {
    try {
      if (this.watchers.has(dirPath)) {
        return; // Already watching
      }

      const watcher = watch(dirPath, {
        ignored: /(^|[\/\\])\../, // ignore dotfiles
        persistent: true,
        ignoreInitial: true
      });

      watcher
        .on('add', path => console.log(`File ${path} has been added`))
        .on('change', path => console.log(`File ${path} has been changed`))
        .on('unlink', path => console.log(`File ${path} has been removed`))
        .on('addDir', path => console.log(`Directory ${path} has been added`))
        .on('unlinkDir', path => console.log(`Directory ${path} has been removed`))
        .on('error', error => console.error(`Watcher error: ${error}`));

      this.watchers.set(dirPath, watcher);
    } catch (error) {
      console.error('Failed to watch directory:', error);
      throw new Error(`Failed to watch directory: ${dirPath}`);
    }
  }

  public async unwatchDirectory(dirPath: string): Promise<void> {
    try {
      const watcher = this.watchers.get(dirPath);
      if (watcher) {
        await watcher.close();
        this.watchers.delete(dirPath);
      }
    } catch (error) {
      console.error('Failed to unwatch directory:', error);
      throw new Error(`Failed to unwatch directory: ${dirPath}`);
    }
  }

  public dispose(): void {
    // Close all watchers
    this.watchers.forEach(async (watcher) => {
      try {
        await watcher.close();
      } catch (error) {
        console.error('Error closing watcher:', error);
      }
    });
    this.watchers.clear();
    
    console.log('File System Manager disposed');
  }
}
