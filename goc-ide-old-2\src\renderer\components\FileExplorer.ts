/**
 * File Explorer Component - File and folder navigation
 */

import { EventEmitter } from 'events';

interface FileItem {
  name: string;
  path: string;
  isDirectory: boolean;
  children?: FileItem[];
}

export class FileExplorer extends EventEmitter {
  private currentWorkspace: string | null = null;
  private fileTree: FileItem[] = [];

  constructor() {
    super();
  }

  public async initialize(): Promise<void> {
    console.log('Initializing File Explorer...');
    console.log('File Explorer initialized');
  }

  public async loadWorkspace(workspacePath: string): Promise<void> {
    try {
      this.currentWorkspace = workspacePath;
      this.fileTree = await this.buildFileTree(workspacePath);
      console.log('Workspace loaded:', workspacePath);
    } catch (error) {
      console.error('Failed to load workspace:', error);
      throw error;
    }
  }

  private async buildFileTree(dirPath: string): Promise<FileItem[]> {
    try {
      const entries = await window.electronAPI.fs.readDirectory(dirPath);
      const items: FileItem[] = [];

      for (const entry of entries) {
        const isDirectory = entry.endsWith('/');
        const cleanPath = isDirectory ? entry.slice(0, -1) : entry;
        const name = cleanPath.split(/[/\\]/).pop() || '';

        // Skip hidden files and common ignore patterns
        if (this.shouldIgnoreFile(name)) {
          continue;
        }

        const item: FileItem = {
          name,
          path: cleanPath,
          isDirectory,
          children: isDirectory ? [] : undefined
        };

        items.push(item);
      }

      // Sort: directories first, then files, both alphabetically
      items.sort((a, b) => {
        if (a.isDirectory && !b.isDirectory) return -1;
        if (!a.isDirectory && b.isDirectory) return 1;
        return a.name.localeCompare(b.name);
      });

      return items;
    } catch (error) {
      console.error('Failed to build file tree:', error);
      return [];
    }
  }

  private shouldIgnoreFile(name: string): boolean {
    const ignorePatterns = [
      /^\./,           // Hidden files
      /^node_modules$/, // Node modules
      /^\.git$/,       // Git directory
      /^dist$/,        // Build output
      /^build$/,       // Build output
      /^coverage$/,    // Test coverage
      /\.log$/,        // Log files
      /\.tmp$/,        // Temporary files
    ];

    return ignorePatterns.some(pattern => pattern.test(name));
  }

  public render(container: HTMLElement): void {
    container.innerHTML = `
      <div class="panel-header">
        <h3>Explorer</h3>
      </div>
      <div class="file-explorer">
        ${this.currentWorkspace ? this.renderFileTree() : this.renderEmptyState()}
      </div>
    `;

    this.setupFileTreeEvents(container);
  }

  private renderEmptyState(): string {
    return `
      <div class="empty-state">
        <p>No folder opened</p>
        <button id="open-folder-explorer" class="welcome-button">Open Folder</button>
      </div>
    `;
  }

  private renderFileTree(): string {
    if (this.fileTree.length === 0) {
      return '<div class="empty-state"><p>Empty folder</p></div>';
    }

    return `
      <div class="workspace-header">
        <span class="workspace-name">${this.getWorkspaceName()}</span>
      </div>
      <ul class="file-tree">
        ${this.fileTree.map(item => this.renderFileItem(item)).join('')}
      </ul>
    `;
  }

  private renderFileItem(item: FileItem, level: number = 0): string {
    const indent = level * 16;
    const icon = this.getFileIcon(item);
    
    return `
      <li class="file-item" 
          data-path="${item.path}" 
          data-is-directory="${item.isDirectory}"
          style="padding-left: ${indent + 8}px">
        <span class="file-icon ${icon}">${this.getFileIconSymbol(item)}</span>
        <span class="file-name">${item.name}</span>
      </li>
    `;
  }

  private getFileIcon(item: FileItem): string {
    if (item.isDirectory) {
      return 'folder-icon';
    }

    const extension = item.name.split('.').pop()?.toLowerCase();
    return `file-icon ${extension || 'default'}`;
  }

  private getFileIconSymbol(item: FileItem): string {
    if (item.isDirectory) {
      return '📁';
    }

    const extension = item.name.split('.').pop()?.toLowerCase();
    const iconMap: { [key: string]: string } = {
      'js': '📄',
      'ts': '📘',
      'html': '🌐',
      'css': '🎨',
      'json': '📋',
      'md': '📝',
      'txt': '📄',
      'png': '🖼️',
      'jpg': '🖼️',
      'jpeg': '🖼️',
      'gif': '🖼️',
      'svg': '🖼️'
    };

    return iconMap[extension || ''] || '📄';
  }

  private setupFileTreeEvents(container: HTMLElement): void {
    // Open folder button
    const openFolderBtn = container.querySelector('#open-folder-explorer');
    if (openFolderBtn) {
      openFolderBtn.addEventListener('click', () => {
        this.emit('folder-selected', '');
      });
    }

    // File item clicks
    const fileItems = container.querySelectorAll('.file-item');
    fileItems.forEach(item => {
      item.addEventListener('click', () => {
        const element = item as HTMLElement;
        const path = element.dataset.path;
        const isDirectory = element.dataset.isDirectory === 'true';

        if (path) {
          if (isDirectory) {
            this.handleDirectoryClick(path);
          } else {
            this.handleFileClick(path);
          }
        }
      });
    });
  }

  private handleFileClick(filePath: string): void {
    // Update selection
    this.updateSelection(filePath);
    
    // Emit file selected event
    this.emit('file-selected', filePath);
  }

  private handleDirectoryClick(dirPath: string): void {
    // For now, just select the directory
    // In the future, this could expand/collapse the directory
    this.updateSelection(dirPath);
    console.log('Directory clicked:', dirPath);
  }

  private updateSelection(path: string): void {
    // Remove previous selection
    document.querySelectorAll('.file-item.selected').forEach(item => {
      item.classList.remove('selected');
    });

    // Add selection to clicked item
    const item = document.querySelector(`[data-path="${path}"]`);
    if (item) {
      item.classList.add('selected');
    }
  }

  private getWorkspaceName(): string {
    if (!this.currentWorkspace) return '';
    return this.currentWorkspace.split(/[/\\]/).pop() || '';
  }

  public getCurrentWorkspace(): string | null {
    return this.currentWorkspace;
  }

  public getFileTree(): FileItem[] {
    return this.fileTree;
  }
}
