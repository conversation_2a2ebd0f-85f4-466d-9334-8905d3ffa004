/**
 * GOC IDE - Renderer Process Entry Point
 */

import './styles/main.css';
import { IDEApplication } from './core/IDEApplication';
import { ThemeManager } from './core/ThemeManager';
import { WindowManager } from './core/WindowManager';

class GOCIDERenderer {
  private app: IDEApplication;
  private themeManager: ThemeManager;
  private windowManager: WindowManager;

  constructor() {
    this.initializeRenderer();
  }

  private async initializeRenderer(): Promise<void> {
    try {
      console.log('🚀 Initializing GOC IDE Renderer...');

      // Initialize core managers
      this.themeManager = new ThemeManager();
      this.windowManager = new WindowManager();

      // Initialize the main application
      this.app = new IDEApplication();

      // Setup theme
      await this.themeManager.initialize();

      // Setup window controls
      this.windowManager.initialize();

      // Initialize the IDE application
      await this.app.initialize();

      console.log('✅ GOC IDE Renderer initialized successfully');

    } catch (error) {
      console.error('❌ Failed to initialize GOC IDE Renderer:', error);
      this.showErrorMessage('Failed to initialize GOC IDE', error);
    }
  }

  private showErrorMessage(title: string, error: any): void {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.innerHTML = `
      <div class="error-content">
        <h2>${title}</h2>
        <p>${error.message || error}</p>
        <button onclick="location.reload()">Reload</button>
      </div>
    `;
    document.body.appendChild(errorDiv);
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new GOCIDERenderer();
  });
} else {
  new GOCIDERenderer();
}

// Handle unhandled errors
window.addEventListener('error', (event) => {
  console.error('Unhandled error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});
