"use strict";
/**
 * Learning Monitor
 *
 * Tracks learning progress, analytics, and provides monitoring capabilities
 * for the learning system
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LearningMonitor = void 0;
const fs = __importStar(require("fs/promises"));
const path = __importStar(require("path"));
const knowledge_base_1 = require("./knowledge-base");
const utils_1 = require("../utils");
class LearningMonitor {
    constructor(config) {
        this.events = [];
        this.initialized = false;
        this.config = config;
        this.knowledgeBase = new knowledge_base_1.KnowledgeBase(config);
        this.storageDir = path.join(config.learning.storageDirectory, 'monitoring');
        this.eventsFile = path.join(this.storageDir, 'events.json');
        this.metricsFile = path.join(this.storageDir, 'metrics.json');
    }
    async initialize() {
        if (this.initialized)
            return;
        try {
            utils_1.logger.info('Initializing Learning Monitor', 'LearningMonitor');
            // Ensure storage directory exists
            await fs.mkdir(this.storageDir, { recursive: true });
            // Initialize knowledge base
            await this.knowledgeBase.initialize();
            // Load existing events
            await this.loadEvents();
            // Clean up old events
            await this.cleanupOldEvents();
            this.initialized = true;
            utils_1.logger.info(`Learning Monitor initialized with ${this.events.length} events`, 'LearningMonitor');
        }
        catch (error) {
            utils_1.logger.error('Failed to initialize Learning Monitor', 'LearningMonitor', { error });
            throw error;
        }
    }
    /**
     * Record a learning event
     */
    async recordEvent(event) {
        try {
            this.events.push(event);
            // Save events periodically
            if (this.events.length % 20 === 0) {
                await this.saveEvents();
            }
            utils_1.logger.debug(`Recorded learning event: ${event.type}`, 'LearningMonitor');
        }
        catch (error) {
            utils_1.logger.error('Failed to record learning event', 'LearningMonitor', { error });
        }
    }
    /**
     * Get learning metrics
     */
    async getMetrics(options = {}) {
        try {
            const filteredEvents = this.filterEvents(options);
            const knowledgeStats = this.knowledgeBase.getStatistics();
            const recentEvents = filteredEvents
                .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
                .slice(0, 10);
            const topPatterns = await this.knowledgeBase.getMostFrequentPatterns(5, {
                userId: options.userId,
                projectId: options.projectId
            });
            const averageConfidence = filteredEvents.length > 0
                ? filteredEvents.reduce((sum, e) => sum + e.confidence, 0) / filteredEvents.length
                : 0;
            return {
                totalPatterns: knowledgeStats.totalPatterns,
                activePatterns: this.countActivePatterns(topPatterns),
                userPreferences: knowledgeStats.totalPreferences,
                learningEvents: filteredEvents.length,
                averageConfidence,
                patternsByType: knowledgeStats.patternsByType,
                recentActivity: recentEvents,
                topPatterns
            };
        }
        catch (error) {
            utils_1.logger.error('Failed to get learning metrics', 'LearningMonitor', { error });
            throw error;
        }
    }
    /**
     * Get detailed analytics
     */
    async getAnalytics(options = {}) {
        try {
            const filteredEvents = this.filterEvents(options);
            const metrics = await this.getMetrics(options);
            const eventsByType = this.groupEventsByType(filteredEvents);
            const learningRate = this.calculateLearningRate(filteredEvents);
            const userSatisfaction = this.calculateUserSatisfaction(filteredEvents);
            const performanceMetrics = this.calculatePerformanceMetrics(filteredEvents);
            const trends = this.calculateTrends(filteredEvents);
            return {
                totalEvents: filteredEvents.length,
                eventsByType,
                learningRate,
                userSatisfaction,
                topPatterns: metrics.topPatterns,
                recentActivity: metrics.recentActivity,
                performanceMetrics,
                trends
            };
        }
        catch (error) {
            utils_1.logger.error('Failed to get learning analytics', 'LearningMonitor', { error });
            throw error;
        }
    }
    /**
     * Get learning progress over time
     */
    async getLearningProgress(timeRange, options = {}) {
        const filteredEvents = this.filterEvents({
            ...options,
            timeRange
        });
        const dailyData = new Map();
        // Group events by day
        for (const event of filteredEvents) {
            const dateKey = event.timestamp.toISOString().split('T')[0];
            if (!dailyData.has(dateKey)) {
                dailyData.set(dateKey, { events: [], patterns: new Set() });
            }
            const dayData = dailyData.get(dateKey);
            dayData.events.push(event);
            event.patterns.forEach(p => dayData.patterns.add(p));
        }
        // Convert to progress array
        const progress = Array.from(dailyData.entries()).map(([date, data]) => ({
            date,
            patternsLearned: data.patterns.size,
            eventsRecorded: data.events.length,
            averageConfidence: data.events.length > 0
                ? data.events.reduce((sum, e) => sum + e.confidence, 0) / data.events.length
                : 0
        }));
        return progress.sort((a, b) => a.date.localeCompare(b.date));
    }
    /**
     * Get user-specific learning insights
     */
    async getUserInsights(userId) {
        const userEvents = this.events.filter(e => e.userId === userId);
        const userPatterns = await this.knowledgeBase.searchPatterns('', { userId, limit: 100 });
        const learningVelocity = this.calculateLearningVelocity(userEvents);
        const preferredPatterns = userPatterns
            .sort((a, b) => b.frequency - a.frequency)
            .slice(0, 5);
        const learningStrengths = this.identifyLearningStrengths(userEvents);
        const improvementAreas = this.identifyImprovementAreas(userEvents);
        const activitySummary = this.calculateActivitySummary(userEvents);
        return {
            learningVelocity,
            preferredPatterns,
            learningStrengths,
            improvementAreas,
            activitySummary
        };
    }
    /**
     * Export learning data
     */
    async exportData(options = {}) {
        const filteredEvents = this.filterEvents(options);
        const patterns = await this.knowledgeBase.searchPatterns('', {
            userId: options.userId,
            projectId: options.projectId,
            limit: 1000
        });
        const metrics = await this.getMetrics(options);
        return {
            events: filteredEvents,
            patterns,
            metrics,
            exportedAt: new Date()
        };
    }
    // Private helper methods
    filterEvents(options) {
        let filtered = [...this.events];
        if (options.userId) {
            filtered = filtered.filter(e => e.userId === options.userId);
        }
        if (options.projectId) {
            filtered = filtered.filter(e => e.projectId === options.projectId);
        }
        if (options.timeRange) {
            filtered = filtered.filter(e => e.timestamp >= options.timeRange.start &&
                e.timestamp <= options.timeRange.end);
        }
        return filtered;
    }
    countActivePatterns(patterns) {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        return patterns.filter(p => p.lastUsed >= thirtyDaysAgo).length;
    }
    groupEventsByType(events) {
        const grouped = {};
        for (const event of events) {
            grouped[event.type] = (grouped[event.type] || 0) + 1;
        }
        return grouped;
    }
    calculateLearningRate(events) {
        if (events.length === 0)
            return 0;
        const sortedEvents = events.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
        const firstEvent = sortedEvents[0];
        const lastEvent = sortedEvents[sortedEvents.length - 1];
        const daysDiff = Math.max(1, (lastEvent.timestamp.getTime() - firstEvent.timestamp.getTime()) / (1000 * 60 * 60 * 24));
        const totalPatterns = new Set(events.flatMap(e => e.patterns)).size;
        return totalPatterns / daysDiff;
    }
    calculateUserSatisfaction(events) {
        const feedbackEvents = events.filter(e => e.type === 'code-accepted' || e.type === 'code-rejected');
        if (feedbackEvents.length === 0)
            return 0.5;
        const acceptedEvents = feedbackEvents.filter(e => e.type === 'code-accepted');
        return acceptedEvents.length / feedbackEvents.length;
    }
    calculatePerformanceMetrics(events) {
        const averageConfidence = events.length > 0
            ? events.reduce((sum, e) => sum + e.confidence, 0) / events.length
            : 0;
        const patternApplicationEvents = events.filter(e => e.type === 'pattern-applied');
        const patternUtilization = patternApplicationEvents.length / Math.max(1, events.length);
        const learningEvents = events.filter(e => e.type === 'preference-learned' || e.type === 'pattern-applied');
        const learningEfficiency = learningEvents.length / Math.max(1, events.length);
        return {
            averageConfidence,
            patternUtilization,
            learningEfficiency
        };
    }
    calculateTrends(events) {
        const dailyData = new Map();
        const weeklyData = new Map();
        for (const event of events) {
            const date = event.timestamp.toISOString().split('T')[0];
            const week = this.getWeekKey(event.timestamp);
            // Daily data
            if (!dailyData.has(date)) {
                dailyData.set(date, { events: 0, patterns: new Set() });
            }
            const dayData = dailyData.get(date);
            dayData.events++;
            event.patterns.forEach(p => dayData.patterns.add(p));
            // Weekly data
            if (!weeklyData.has(week)) {
                weeklyData.set(week, { events: 0, patterns: new Set() });
            }
            const weekData = weeklyData.get(week);
            weekData.events++;
            event.patterns.forEach(p => weekData.patterns.add(p));
        }
        const daily = Array.from(dailyData.entries()).map(([date, data]) => ({
            date,
            events: data.events,
            patterns: data.patterns.size
        }));
        const weekly = Array.from(weeklyData.entries()).map(([week, data]) => ({
            week,
            events: data.events,
            patterns: data.patterns.size
        }));
        return { daily, weekly };
    }
    calculateLearningVelocity(events) {
        const last7Days = events.filter(e => {
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            return e.timestamp >= sevenDaysAgo;
        });
        return last7Days.length / 7; // Events per day
    }
    identifyLearningStrengths(events) {
        const typeFrequency = this.groupEventsByType(events);
        const strengths = [];
        if (typeFrequency['code-accepted'] > typeFrequency['code-rejected']) {
            strengths.push('High code acceptance rate');
        }
        if (typeFrequency['pattern-applied'] > 5) {
            strengths.push('Effective pattern utilization');
        }
        if (typeFrequency['preference-learned'] > 3) {
            strengths.push('Active preference learning');
        }
        return strengths;
    }
    identifyImprovementAreas(events) {
        const typeFrequency = this.groupEventsByType(events);
        const areas = [];
        if (typeFrequency['code-rejected'] > typeFrequency['code-accepted']) {
            areas.push('Code quality and accuracy');
        }
        if ((typeFrequency['pattern-applied'] || 0) < 2) {
            areas.push('Pattern recognition and application');
        }
        if ((typeFrequency['web-research'] || 0) < 1) {
            areas.push('Web research utilization');
        }
        return areas;
    }
    calculateActivitySummary(events) {
        // Group events into sessions (events within 1 hour of each other)
        const sessions = [];
        const sortedEvents = events.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
        let currentSession = [];
        for (const event of sortedEvents) {
            if (currentSession.length === 0) {
                currentSession.push(event);
            }
            else {
                const lastEvent = currentSession[currentSession.length - 1];
                const timeDiff = event.timestamp.getTime() - lastEvent.timestamp.getTime();
                if (timeDiff <= 60 * 60 * 1000) { // 1 hour
                    currentSession.push(event);
                }
                else {
                    sessions.push(currentSession);
                    currentSession = [event];
                }
            }
        }
        if (currentSession.length > 0) {
            sessions.push(currentSession);
        }
        const averageSessionLength = sessions.length > 0
            ? sessions.reduce((sum, session) => sum + session.length, 0) / sessions.length
            : 0;
        // Find most active hours
        const hourFrequency = new Map();
        for (const event of events) {
            const hour = event.timestamp.getHours();
            hourFrequency.set(hour, (hourFrequency.get(hour) || 0) + 1);
        }
        const mostActiveHours = Array.from(hourFrequency.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 3)
            .map(([hour]) => hour);
        return {
            totalSessions: sessions.length,
            averageSessionLength,
            mostActiveHours
        };
    }
    getWeekKey(date) {
        const year = date.getFullYear();
        const week = Math.ceil(date.getDate() / 7);
        const month = date.getMonth() + 1;
        return `${year}-${month.toString().padStart(2, '0')}-W${week}`;
    }
    async loadEvents() {
        try {
            const data = await fs.readFile(this.eventsFile, 'utf-8');
            const eventsData = JSON.parse(data);
            // Convert date strings back to Date objects
            this.events = eventsData.map(event => ({
                ...event,
                timestamp: new Date(event.timestamp)
            }));
        }
        catch (error) {
            utils_1.logger.debug('No existing events file found, starting fresh', 'LearningMonitor');
        }
    }
    async saveEvents() {
        try {
            await fs.writeFile(this.eventsFile, JSON.stringify(this.events, null, 2));
        }
        catch (error) {
            utils_1.logger.error('Failed to save events', 'LearningMonitor', { error });
        }
    }
    async cleanupOldEvents() {
        if (!this.config.learning.retentionDays)
            return;
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - this.config.learning.retentionDays);
        const originalLength = this.events.length;
        this.events = this.events.filter(event => event.timestamp >= cutoffDate);
        const removedCount = originalLength - this.events.length;
        if (removedCount > 0) {
            await this.saveEvents();
            utils_1.logger.info(`Cleaned up ${removedCount} old events`, 'LearningMonitor');
        }
    }
    async dispose() {
        if (!this.initialized)
            return;
        try {
            await this.saveEvents();
            await this.knowledgeBase.dispose();
            this.events = [];
            this.initialized = false;
            utils_1.logger.info('Learning Monitor disposed', 'LearningMonitor');
        }
        catch (error) {
            utils_1.logger.error('Failed to dispose Learning Monitor', 'LearningMonitor', { error });
        }
    }
}
exports.LearningMonitor = LearningMonitor;
//# sourceMappingURL=learning-monitor.js.map