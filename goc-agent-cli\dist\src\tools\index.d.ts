/**
 * Enhanced Tool System
 *
 * Implements advanced file operations, process management, and development environment integration
 * inspired by Augment Agent's tool system.
 */
export interface ToolResult {
    success: boolean;
    data?: any;
    error?: string;
    metadata?: any;
}
export interface FileRange {
    start: number;
    end: number;
}
export interface ProcessOptions {
    cwd?: string;
    timeout?: number;
    env?: Record<string, string>;
    input?: string;
}
export interface ProcessResult {
    stdout: string;
    stderr: string;
    exitCode: number;
    pid?: number;
}
export { FileOperations } from './file-operations';
export { ProcessManager } from './process-manager';
export { DiagnosticsProvider } from './diagnostics';
export { TerminalManager } from './terminal';
export { PackageManager } from './package-manager';
export { WorkspaceDetector } from './workspace';
export declare class ToolRegistry {
    private static instance;
    private tools;
    static getInstance(): ToolRegistry;
    registerTool(name: string, tool: any): void;
    getTool(name: string): any;
    getAvailableTools(): string[];
    executeTool(name: string, method: string, ...args: any[]): Promise<ToolResult>;
}
export declare function initializeTools(): void;
//# sourceMappingURL=index.d.ts.map