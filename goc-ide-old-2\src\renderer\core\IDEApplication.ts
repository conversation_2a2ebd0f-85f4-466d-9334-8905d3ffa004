/**
 * Main IDE Application Controller
 */

import { EditorManager } from './EditorManager';
import { FileExplorer } from '../components/FileExplorer';
import { ActivityBar } from '../components/ActivityBar';
import { StatusBar } from '../components/StatusBar';
import { CommandPalette } from '../components/CommandPalette';
import { GOCAgentPanel } from '../components/GOCAgentPanel';
import { TerminalManager } from './TerminalManager';
import { KeyboardManager } from './KeyboardManager';

export class IDEApplication {
  private editorManager: EditorManager;
  private fileExplorer: FileExplorer;
  private activityBar: ActivityBar;
  private statusBar: StatusBar;
  private commandPalette: CommandPalette;
  private gocAgentPanel: GOCAgentPanel;
  private terminalManager: TerminalManager;
  private keyboardManager: KeyboardManager;

  private currentWorkspace: string | null = null;

  constructor() {
    this.initializeComponents();
  }

  private initializeComponents(): void {
    // Initialize core components
    this.editorManager = new EditorManager();
    this.fileExplorer = new FileExplorer();
    this.activityBar = new ActivityBar();
    this.statusBar = new StatusBar();
    this.commandPalette = new CommandPalette();
    this.gocAgentPanel = new GOCAgentPanel();
    this.terminalManager = new TerminalManager();
    this.keyboardManager = new KeyboardManager();
  }

  public async initialize(): Promise<void> {
    try {
      console.log('Initializing IDE Application...');

      // Initialize components in order
      await this.initializeUI();
      await this.setupEventHandlers();
      await this.setupKeyboardShortcuts();
      await this.loadWorkspace();

      console.log('IDE Application initialized successfully');

    } catch (error) {
      console.error('Failed to initialize IDE Application:', error);
      throw error;
    }
  }

  private async initializeUI(): Promise<void> {
    // Initialize activity bar
    await this.activityBar.initialize();

    // Initialize file explorer
    await this.fileExplorer.initialize();

    // Initialize editor manager
    await this.editorManager.initialize();

    // Initialize status bar
    await this.statusBar.initialize();

    // Initialize command palette
    await this.commandPalette.initialize();

    // Initialize GOC Agent panel
    await this.gocAgentPanel.initialize();

    // Initialize terminal manager
    await this.terminalManager.initialize();

    // Show welcome screen initially
    this.showWelcomeScreen();
  }

  private async setupEventHandlers(): Promise<void> {
    // Activity bar events
    this.activityBar.on('panel-changed', (panelId: string) => {
      this.handlePanelChange(panelId);
    });

    // File explorer events
    this.fileExplorer.on('file-selected', (filePath: string) => {
      this.openFile(filePath);
    });

    this.fileExplorer.on('folder-selected', (folderPath: string) => {
      this.openWorkspace(folderPath);
    });

    // Editor events
    this.editorManager.on('file-changed', (filePath: string, isDirty: boolean) => {
      this.statusBar.updateFileStatus(filePath, isDirty);
    });

    this.editorManager.on('cursor-changed', (line: number, column: number) => {
      this.statusBar.updateCursorPosition(line, column);
    });

    // Welcome screen events
    this.setupWelcomeScreenEvents();

    // Window events
    window.electronAPI.on('menu-new-file', () => {
      this.createNewFile();
    });

    window.electronAPI.on('menu-open-file', () => {
      this.openFileDialog();
    });

    window.electronAPI.on('menu-open-folder', () => {
      this.openFolderDialog();
    });

    window.electronAPI.on('menu-save', () => {
      this.saveCurrentFile();
    });
  }

  private async setupKeyboardShortcuts(): Promise<void> {
    // Initialize keyboard manager
    await this.keyboardManager.initialize();

    // Register shortcuts
    this.keyboardManager.register('Ctrl+N', () => this.createNewFile());
    this.keyboardManager.register('Ctrl+O', () => this.openFileDialog());
    this.keyboardManager.register('Ctrl+S', () => this.saveCurrentFile());
    this.keyboardManager.register('Ctrl+Shift+P', () => this.commandPalette.show());
    this.keyboardManager.register('Ctrl+`', () => this.terminalManager.toggle());
    this.keyboardManager.register('Ctrl+Shift+E', () => this.activityBar.selectPanel('explorer'));
    this.keyboardManager.register('Ctrl+Shift+F', () => this.activityBar.selectPanel('search'));
    this.keyboardManager.register('Ctrl+Shift+G', () => this.activityBar.selectPanel('git'));
    this.keyboardManager.register('Ctrl+Shift+A', () => this.activityBar.selectPanel('goc-agent'));
  }

  private setupWelcomeScreenEvents(): void {
    const openFolderBtn = document.getElementById('open-folder-btn');
    const newFileBtn = document.getElementById('new-file-btn');

    if (openFolderBtn) {
      openFolderBtn.addEventListener('click', () => {
        this.openFolderDialog();
      });
    }

    if (newFileBtn) {
      newFileBtn.addEventListener('click', () => {
        this.createNewFile();
      });
    }
  }

  private showWelcomeScreen(): void {
    const welcomeScreen = document.getElementById('welcome-screen');
    if (welcomeScreen) {
      welcomeScreen.style.display = 'flex';
    }
  }

  private hideWelcomeScreen(): void {
    const welcomeScreen = document.getElementById('welcome-screen');
    if (welcomeScreen) {
      welcomeScreen.style.display = 'none';
    }
  }

  private handlePanelChange(panelId: string): void {
    const sidebarContent = document.getElementById('sidebar-content');
    if (!sidebarContent) return;

    // Clear current content
    sidebarContent.innerHTML = '';

    // Load panel content
    switch (panelId) {
      case 'explorer':
        this.fileExplorer.render(sidebarContent);
        break;
      case 'search':
        this.renderSearchPanel(sidebarContent);
        break;
      case 'git':
        this.renderGitPanel(sidebarContent);
        break;
      case 'goc-agent':
        this.gocAgentPanel.render(sidebarContent);
        break;
      case 'extensions':
        this.renderExtensionsPanel(sidebarContent);
        break;
      case 'settings':
        this.renderSettingsPanel(sidebarContent);
        break;
    }
  }

  private renderSearchPanel(container: HTMLElement): void {
    container.innerHTML = `
      <div class="panel-header">
        <h3>Search</h3>
      </div>
      <div class="search-container">
        <input type="text" placeholder="Search..." class="search-input">
        <button class="search-button">Search</button>
      </div>
      <div class="search-results">
        <!-- Search results will appear here -->
      </div>
    `;
  }

  private renderGitPanel(container: HTMLElement): void {
    container.innerHTML = `
      <div class="panel-header">
        <h3>Source Control</h3>
      </div>
      <div class="git-container">
        <p>Git integration coming soon...</p>
      </div>
    `;
  }

  private renderExtensionsPanel(container: HTMLElement): void {
    container.innerHTML = `
      <div class="panel-header">
        <h3>Extensions</h3>
      </div>
      <div class="extensions-container">
        <p>Extension system coming soon...</p>
      </div>
    `;
  }

  private renderSettingsPanel(container: HTMLElement): void {
    container.innerHTML = `
      <div class="panel-header">
        <h3>Settings</h3>
      </div>
      <div class="settings-container">
        <p>Settings panel coming soon...</p>
      </div>
    `;
  }

  public async openFile(filePath: string): Promise<void> {
    try {
      this.hideWelcomeScreen();
      await this.editorManager.openFile(filePath);
    } catch (error) {
      console.error('Failed to open file:', error);
    }
  }

  public async createNewFile(): Promise<void> {
    this.hideWelcomeScreen();
    await this.editorManager.createNewFile();
  }

  public async openFileDialog(): Promise<void> {
    try {
      const result = await window.electronAPI.dialog.showOpenDialog({
        properties: ['openFile'],
        filters: [
          { name: 'All Files', extensions: ['*'] },
          { name: 'Text Files', extensions: ['txt', 'md'] },
          { name: 'Code Files', extensions: ['js', 'ts', 'html', 'css', 'json'] }
        ]
      });

      if (!result.canceled && result.filePaths.length > 0) {
        await this.openFile(result.filePaths[0]);
      }
    } catch (error) {
      console.error('Failed to open file dialog:', error);
    }
  }

  public async openFolderDialog(): Promise<void> {
    try {
      const result = await window.electronAPI.dialog.showOpenDialog({
        properties: ['openDirectory']
      });

      if (!result.canceled && result.filePaths.length > 0) {
        await this.openWorkspace(result.filePaths[0]);
      }
    } catch (error) {
      console.error('Failed to open folder dialog:', error);
    }
  }

  public async openWorkspace(workspacePath: string): Promise<void> {
    try {
      this.currentWorkspace = workspacePath;
      this.hideWelcomeScreen();
      
      // Load workspace in file explorer
      await this.fileExplorer.loadWorkspace(workspacePath);
      
      // Switch to explorer panel
      this.activityBar.selectPanel('explorer');
      
      console.log('Workspace opened:', workspacePath);
    } catch (error) {
      console.error('Failed to open workspace:', error);
    }
  }

  public async saveCurrentFile(): Promise<void> {
    try {
      await this.editorManager.saveCurrentFile();
    } catch (error) {
      console.error('Failed to save file:', error);
    }
  }

  private async loadWorkspace(): Promise<void> {
    // Load last workspace if available
    // This would typically come from settings/preferences
  }
}
