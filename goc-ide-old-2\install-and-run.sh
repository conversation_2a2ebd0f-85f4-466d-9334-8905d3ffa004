#!/bin/bash

echo "========================================"
echo "   GOC IDE - Installation and Setup"
echo "========================================"
echo

echo "[1/4] Installing dependencies..."
npm install
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install dependencies"
    exit 1
fi

echo
echo "[2/4] Building main process..."
npm run build:main
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to build main process"
    exit 1
fi

echo
echo "[3/4] Building renderer process..."
npm run build:renderer
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to build renderer process"
    exit 1
fi

echo
echo "[4/4] Starting GOC IDE..."
npm start

echo
echo "GOC IDE setup complete!"
