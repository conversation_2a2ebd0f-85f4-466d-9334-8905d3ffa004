/**
 * Documentation Command
 *
 * Intelligent documentation generation with code analysis and best practices
 */
import { Command } from 'commander';
import { Goc<PERSON>ore } from '@goc-agent/core';
export declare class DocsCommand {
    private core;
    constructor(core: GocCore);
    register(program: Command): void;
    private generateDocumentation;
    private generateAPIDocumentation;
    private generateExamples;
    private generateReadme;
    private interactiveGeneration;
    private createDocumentationGenerator;
    private analyzeProject;
    private generateProjectDocs;
    private extractAPIDocumentation;
    private createCodeExamples;
    private generateInstallationSection;
    private generateUsageSection;
    private generateBasicExample;
    private generateAdvancedExample;
    private formatAPIDocumentation;
    private formatExamples;
    private generateReadmeContent;
    private exportDocumentation;
    private convertToMarkdown;
    private convertToHTML;
}
//# sourceMappingURL=docs.d.ts.map