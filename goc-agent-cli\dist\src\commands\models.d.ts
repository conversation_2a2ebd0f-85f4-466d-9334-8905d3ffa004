/**
 * Model Selection Commands
 *
 * Interactive commands for selecting between Ollama and GOC Agent models
 */
import { Command } from 'commander';
export declare class ModelsCommand {
    private configManager;
    private apiClient;
    constructor();
    register(program: Command): void;
    private selectModel;
    private selectProvider;
    private selectOllamaModel;
    private selectGocAgentModel;
    private handleGocAgentAuth;
    private checkOllamaAvailability;
    private getOllamaModels;
    private showOllamaSetup;
    private showOllamaModelSetup;
    private formatOllamaModelName;
    private getOllamaModelDescription;
    private listModels;
    private showStatus;
    private testModel;
}
//# sourceMappingURL=models.d.ts.map