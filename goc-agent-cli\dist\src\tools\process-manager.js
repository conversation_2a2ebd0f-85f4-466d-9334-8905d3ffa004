"use strict";
/**
 * Process Management Tool
 *
 * Implements launch-process, read-process, write-process, kill-process with monitoring
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessManager = void 0;
const child_process_1 = require("child_process");
const events_1 = require("events");
class ProcessManager extends events_1.EventEmitter {
    constructor() {
        super(...arguments);
        this.processes = new Map();
        this.childProcesses = new Map();
        this.processCounter = 0;
    }
    /**
     * Launch a new process
     */
    async launchProcess(command, args = [], options = {}) {
        try {
            const processId = `proc_${++this.processCounter}_${Date.now()}`;
            const { cwd = process.cwd(), timeout = 30000, env = process.env, input, wait = false, maxWaitSeconds = 600, streamOutput = false } = options;
            const processInfo = {
                id: processId,
                command,
                args,
                status: 'running',
                startTime: new Date(),
                output: '',
                errorOutput: ''
            };
            this.processes.set(processId, processInfo);
            const childProcess = (0, child_process_1.spawn)(command, args, {
                cwd,
                env: env,
                stdio: ['pipe', 'pipe', 'pipe']
            });
            processInfo.pid = childProcess.pid;
            this.childProcesses.set(processId, childProcess);
            // Handle output
            childProcess.stdout?.on('data', (data) => {
                const chunk = data.toString();
                processInfo.output += chunk;
                if (streamOutput) {
                    process.stdout.write(chunk);
                }
                this.emit('output', processId, chunk, 'stdout');
            });
            childProcess.stderr?.on('data', (data) => {
                const chunk = data.toString();
                processInfo.errorOutput += chunk;
                if (streamOutput) {
                    process.stderr.write(chunk);
                }
                this.emit('output', processId, chunk, 'stderr');
            });
            // Handle process completion
            childProcess.on('close', (code) => {
                processInfo.status = code === 0 ? 'completed' : 'failed';
                processInfo.exitCode = code || 0;
                processInfo.endTime = new Date();
                this.childProcesses.delete(processId);
                this.emit('processComplete', processId, code);
            });
            childProcess.on('error', (error) => {
                processInfo.status = 'failed';
                processInfo.errorOutput += error.message;
                processInfo.endTime = new Date();
                this.childProcesses.delete(processId);
                this.emit('processError', processId, error);
            });
            // Send input if provided
            if (input && childProcess.stdin) {
                childProcess.stdin.write(input);
                childProcess.stdin.end();
            }
            if (wait) {
                // Wait for process to complete
                const result = await this.waitForProcess(processId, maxWaitSeconds * 1000);
                return {
                    success: result.exitCode === 0,
                    data: {
                        processId,
                        ...result
                    }
                };
            }
            else {
                // Return immediately
                return {
                    success: true,
                    data: {
                        processId,
                        pid: processInfo.pid,
                        status: 'running'
                    }
                };
            }
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Read output from a process
     */
    async readProcess(processId, options = {}) {
        try {
            const processInfo = this.processes.get(processId);
            if (!processInfo) {
                return {
                    success: false,
                    error: `Process not found: ${processId}`
                };
            }
            if (options.wait && processInfo.status === 'running') {
                const result = await this.waitForProcess(processId, (options.maxWaitSeconds || 60) * 1000);
                return {
                    success: true,
                    data: result
                };
            }
            return {
                success: true,
                data: {
                    processId,
                    status: processInfo.status,
                    output: processInfo.output,
                    errorOutput: processInfo.errorOutput,
                    exitCode: processInfo.exitCode,
                    pid: processInfo.pid
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Write input to a process
     */
    async writeProcess(processId, input) {
        try {
            const childProcess = this.childProcesses.get(processId);
            if (!childProcess) {
                return {
                    success: false,
                    error: `Process not found or not running: ${processId}`
                };
            }
            if (!childProcess.stdin) {
                return {
                    success: false,
                    error: `Process stdin not available: ${processId}`
                };
            }
            childProcess.stdin.write(input);
            return {
                success: true,
                data: {
                    processId,
                    bytesWritten: Buffer.byteLength(input, 'utf8')
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Kill a process
     */
    async killProcess(processId, signal = 'SIGTERM') {
        try {
            const processInfo = this.processes.get(processId);
            const childProcess = this.childProcesses.get(processId);
            if (!processInfo) {
                return {
                    success: false,
                    error: `Process not found: ${processId}`
                };
            }
            if (processInfo.status !== 'running') {
                return {
                    success: true,
                    data: {
                        processId,
                        status: processInfo.status,
                        message: 'Process already terminated'
                    }
                };
            }
            if (childProcess) {
                childProcess.kill(signal);
                processInfo.status = 'killed';
                processInfo.endTime = new Date();
                this.childProcesses.delete(processId);
            }
            return {
                success: true,
                data: {
                    processId,
                    signal,
                    status: 'killed'
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * List all processes
     */
    listProcesses() {
        const processes = Array.from(this.processes.values()).map(proc => ({
            id: proc.id,
            command: proc.command,
            args: proc.args,
            pid: proc.pid,
            status: proc.status,
            startTime: proc.startTime,
            endTime: proc.endTime,
            exitCode: proc.exitCode
        }));
        return {
            success: true,
            data: {
                processes,
                running: processes.filter(p => p.status === 'running').length,
                total: processes.length
            }
        };
    }
    /**
     * Get process info
     */
    getProcessInfo(processId) {
        const processInfo = this.processes.get(processId);
        if (!processInfo) {
            return {
                success: false,
                error: `Process not found: ${processId}`
            };
        }
        return {
            success: true,
            data: processInfo
        };
    }
    /**
     * Wait for process to complete
     */
    waitForProcess(processId, timeoutMs) {
        return new Promise((resolve, reject) => {
            const processInfo = this.processes.get(processId);
            if (!processInfo) {
                reject(new Error(`Process not found: ${processId}`));
                return;
            }
            if (processInfo.status !== 'running') {
                resolve({
                    stdout: processInfo.output,
                    stderr: processInfo.errorOutput,
                    exitCode: processInfo.exitCode || 0,
                    pid: processInfo.pid
                });
                return;
            }
            const timeout = setTimeout(() => {
                reject(new Error(`Process timeout after ${timeoutMs}ms`));
            }, timeoutMs);
            const onComplete = (completedProcessId, exitCode) => {
                if (completedProcessId === processId) {
                    clearTimeout(timeout);
                    this.removeListener('processComplete', onComplete);
                    this.removeListener('processError', onError);
                    const info = this.processes.get(processId);
                    resolve({
                        stdout: info.output,
                        stderr: info.errorOutput,
                        exitCode,
                        pid: info.pid
                    });
                }
            };
            const onError = (errorProcessId, error) => {
                if (errorProcessId === processId) {
                    clearTimeout(timeout);
                    this.removeListener('processComplete', onComplete);
                    this.removeListener('processError', onError);
                    reject(error);
                }
            };
            this.on('processComplete', onComplete);
            this.on('processError', onError);
        });
    }
    /**
     * Cleanup completed processes
     */
    cleanup() {
        const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
        for (const [id, info] of this.processes.entries()) {
            if (info.status !== 'running' && info.endTime && info.endTime < cutoff) {
                this.processes.delete(id);
            }
        }
    }
}
exports.ProcessManager = ProcessManager;
//# sourceMappingURL=process-manager.js.map