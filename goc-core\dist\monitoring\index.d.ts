/**
 * Monitoring and Analytics System
 *
 * Performance monitoring, usage analytics, and system health tracking
 */
export interface MetricEvent {
    id: string;
    type: 'performance' | 'usage' | 'error' | 'feature' | 'system';
    name: string;
    value: number;
    unit: string;
    tags: Record<string, string>;
    timestamp: Date;
    metadata?: Record<string, any>;
}
export interface PerformanceMetrics {
    responseTime: number;
    throughput: number;
    errorRate: number;
    memoryUsage: number;
    cpuUsage: number;
    activeConnections: number;
}
export interface UsageMetrics {
    totalRequests: number;
    uniqueUsers: number;
    featuresUsed: Record<string, number>;
    averageSessionDuration: number;
    topQueries: Array<{
        query: string;
        count: number;
    }>;
    topErrors: Array<{
        error: string;
        count: number;
    }>;
}
export interface SystemHealth {
    status: 'healthy' | 'degraded' | 'unhealthy';
    uptime: number;
    version: string;
    components: Record<string, {
        status: 'up' | 'down' | 'degraded';
        responseTime?: number;
        lastCheck: Date;
        error?: string;
    }>;
    alerts: Array<{
        level: 'info' | 'warning' | 'error' | 'critical';
        message: string;
        timestamp: Date;
        component?: string;
    }>;
}
export interface MonitoringOptions {
    enablePerformanceTracking?: boolean;
    enableUsageAnalytics?: boolean;
    enableErrorTracking?: boolean;
    retentionDays?: number;
    alertThresholds?: {
        responseTime?: number;
        errorRate?: number;
        memoryUsage?: number;
    };
}
/**
 * Comprehensive Monitoring System
 */
export declare class MonitoringSystem {
    private metrics;
    private performanceData;
    private usageData;
    private healthChecks;
    private options;
    private startTime;
    constructor(options?: MonitoringOptions);
    /**
     * Record a metric event
     */
    recordMetric(event: Omit<MetricEvent, 'id' | 'timestamp'>): void;
    /**
     * Record performance metrics
     */
    recordPerformance(metrics: PerformanceMetrics): void;
    /**
     * Record usage analytics
     */
    recordUsage(event: {
        userId?: string;
        feature: string;
        action: string;
        duration?: number;
        metadata?: Record<string, any>;
    }): void;
    /**
     * Record error event
     */
    recordError(error: Error, context?: {
        userId?: string;
        feature?: string;
        action?: string;
        metadata?: Record<string, any>;
    }): void;
    /**
     * Get current system health
     */
    getSystemHealth(): Promise<SystemHealth>;
    /**
     * Get performance analytics
     */
    getPerformanceAnalytics(timeRange?: {
        start: Date;
        end: Date;
    }): {
        averageResponseTime: number;
        throughput: number;
        errorRate: number;
        trends: Array<{
            timestamp: Date;
            responseTime: number;
            throughput: number;
            errorRate: number;
        }>;
    };
    /**
     * Get usage analytics
     */
    getUsageAnalytics(timeRange?: {
        start: Date;
        end: Date;
    }): UsageMetrics;
    /**
     * Register a health check
     */
    registerHealthCheck(name: string, check: () => Promise<boolean>): void;
    /**
     * Get metrics by type and name
     */
    getMetrics(type?: string, name?: string, timeRange?: {
        start: Date;
        end: Date;
    }): MetricEvent[];
    /**
     * Export metrics for external analysis
     */
    exportMetrics(format?: 'json' | 'csv'): string;
    private shouldRecordMetric;
    private cleanupOldMetrics;
    private checkPerformanceAlerts;
    private initializeHealthChecks;
    private startPerformanceMonitoring;
}
//# sourceMappingURL=index.d.ts.map