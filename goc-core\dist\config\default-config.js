"use strict";
/**
 * Default Configuration
 *
 * Provides default configuration values for GOC Agent
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMinimalConfig = exports.getDefaultConfig = void 0;
function getDefaultConfig() {
    return {
        version: '1.0.0',
        ai: {
            defaultProvider: 'ollama',
            defaultModel: 'llama3.2:3b',
            providers: {
                ollama: {
                    name: 'ollama',
                    displayName: 'Ollama (Local)',
                    tier: 'free',
                    baseUrl: 'http://localhost:11434',
                    models: [], // Models will be dynamically loaded from user's Ollama installation
                    defaultModel: 'llama3.2:3b', // Fallback default
                    maxTokens: 4096,
                    temperature: 0.7,
                    timeout: 60000,
                    requiresAuth: false,
                    isLocal: true,
                    setupInstructions: 'Install Ollama from https://ollama.ai and pull models using "ollama pull <model>"'
                },
                goc: {
                    name: 'goc',
                    displayName: 'GOC Agent Model',
                    tier: 'freemium',
                    baseUrl: 'https://api.goc-agent.com/v1',
                    models: [
                        {
                            id: 'goc-agent-cloud',
                            name: 'GOC Agent Model (Free)',
                            tier: 'free',
                            contextLength: 200000,
                            capabilities: ['code-generation', 'code-analysis', 'general-chat'],
                            description: '50 requests/month forever - perfect for trying GOC Agent',
                            requiresAuth: true,
                            isLocal: false,
                            pricing: {
                                requestCost: 0,
                                currency: 'USD'
                            }
                        },
                        {
                            id: 'goc-agent-dev',
                            name: 'GOC Agent Model (Developer)',
                            tier: 'paid',
                            contextLength: 200000,
                            capabilities: ['code-generation', 'code-analysis', 'code-review', 'debugging', 'web-search', 'general-chat'],
                            description: 'Full-featured model for professional development - $29/month for 500 requests',
                            requiresAuth: true,
                            isLocal: false,
                            pricing: {
                                requestCost: 0.058,
                                currency: 'USD'
                            }
                        }
                    ],
                    defaultModel: 'goc-agent-cloud',
                    maxTokens: 4096,
                    temperature: 0.7,
                    timeout: 30000,
                    requiresAuth: true,
                    isLocal: false,
                    authenticationUrl: 'https://goc-agent.com/auth',
                    setupInstructions: 'Sign up for GOC Agent to access hosted AI models'
                }
                // Other providers can be enabled later
                // groq: { ... },
                // openai: { ... },
                // gemini: { ... }
            },
            temperature: 0.7,
            maxTokens: 4096,
            timeout: 60000
        },
        context: {
            maxContextLines: 1000,
            enableSemanticSearch: true,
            cacheEnabled: true,
            cacheSize: 100,
            analysisDepth: 'medium'
        },
        files: {
            maxFileSize: 10 * 1024 * 1024, // 10MB
            allowedExtensions: [
                '.js', '.jsx', '.ts', '.tsx',
                '.py', '.php', '.html', '.css',
                '.md', '.json', '.txt', '.yml', '.yaml'
            ],
            backupEnabled: true,
            backupDirectory: '.goc/backups',
            autoSave: false
        },
        web: {
            enabled: true,
            searchEngine: 'duckduckgo',
            maxResults: 10,
            timeout: 30000,
            userAgent: 'GOC-Agent/1.0',
            enableCaching: true
        },
        learning: {
            enabled: true,
            autoLearn: true,
            maxPatterns: 10000,
            confidenceThreshold: 0.5,
            retentionDays: 90,
            enableWebLearning: true,
            enablePatternSharing: false,
            storageDirectory: '.goc/learning'
        },
        global: {
            logLevel: 'info',
            cacheDirectory: '.goc/cache',
            tempDirectory: '.goc/temp',
            enableTelemetry: false,
            autoUpdate: false
        }
    };
}
exports.getDefaultConfig = getDefaultConfig;
function getMinimalConfig() {
    return {
        ai: {
            defaultProvider: 'ollama',
            defaultModel: 'llama3.2:3b',
            temperature: 0.7,
            maxTokens: 4096,
            timeout: 60000,
            providers: {}
        },
        global: {
            logLevel: 'info',
            cacheDirectory: '.goc/cache',
            tempDirectory: '.goc/temp',
            enableTelemetry: false,
            autoUpdate: false
        }
    };
}
exports.getMinimalConfig = getMinimalConfig;
//# sourceMappingURL=default-config.js.map