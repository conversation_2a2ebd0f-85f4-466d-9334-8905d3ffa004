"use strict";
/**
 * Documentation Generation System
 *
 * Intelligent documentation generation with code analysis and best practices
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentationGenerator = void 0;
const utils_1 = require("../utils");
/**
 * Intelligent Documentation Generator
 */
class DocumentationGenerator {
    constructor(contextEngine) {
        this.contextEngine = contextEngine;
    }
    /**
     * Generate comprehensive project documentation
     */
    async generateProjectDocumentation(projectPath, options) {
        try {
            utils_1.logger.info('Starting project documentation generation', { projectPath, options });
            // Analyze project structure
            const projectAnalysis = await this.analyzeProject(projectPath);
            // Generate sections based on options
            const sections = [];
            // Overview section
            sections.push(await this.generateOverviewSection(projectAnalysis));
            // API documentation
            if (options.includeAPI) {
                const apiDocs = await this.generateAPIDocumentation(projectPath, options.language);
                sections.push({
                    title: 'API Reference',
                    content: this.formatAPIDocumentation(apiDocs),
                    level: 1,
                    type: 'api'
                });
            }
            // Architecture documentation
            let architecture;
            if (options.includeArchitecture) {
                architecture = await this.generateArchitectureDocumentation(projectAnalysis);
                sections.push({
                    title: 'Architecture',
                    content: this.formatArchitectureDocumentation(architecture),
                    level: 1,
                    type: 'architecture'
                });
            }
            // Examples section
            if (options.includeExamples) {
                sections.push(await this.generateExamplesSection(projectAnalysis));
            }
            // Deployment documentation
            let deployment;
            if (options.includeDeployment) {
                deployment = await this.generateDeploymentDocumentation(projectAnalysis);
                sections.push({
                    title: 'Deployment',
                    content: this.formatDeploymentDocumentation(deployment),
                    level: 1,
                    type: 'deployment'
                });
            }
            // Testing documentation
            let testing;
            if (options.includeTesting) {
                testing = await this.generateTestingDocumentation(projectAnalysis);
                sections.push({
                    title: 'Testing',
                    content: this.formatTestingDocumentation(testing),
                    level: 1,
                    type: 'testing'
                });
            }
            const documentation = {
                title: projectAnalysis.name || 'Project Documentation',
                description: projectAnalysis.description || 'Generated project documentation',
                version: projectAnalysis.version || '1.0.0',
                sections,
                api: options.includeAPI ? await this.generateAPIDocumentation(projectPath, options.language) : undefined,
                architecture,
                deployment,
                testing
            };
            utils_1.logger.info('Project documentation generation completed', {
                sectionsCount: sections.length,
                format: options.format
            });
            return documentation;
        }
        catch (error) {
            utils_1.logger.error('Project documentation generation failed', error);
            throw error;
        }
    }
    /**
     * Generate API documentation from code
     */
    async generateAPIDocumentation(projectPath, language) {
        try {
            // Use context engine to analyze code and extract API information
            const codeAnalysis = await this.contextEngine.analyzeProject(projectPath, {
                includeSymbols: true,
                includeRelationships: true
            });
            const api = {
                functions: [],
                classes: [],
                interfaces: []
            };
            // Extract functions
            if (codeAnalysis.symbols?.functions) {
                api.functions = codeAnalysis.symbols.functions.map(func => ({
                    name: func.name,
                    description: func.description || `${func.name} function`,
                    parameters: func.parameters?.map(param => ({
                        name: param.name,
                        type: param.type || 'any',
                        description: param.description || `${param.name} parameter`,
                        required: param.required !== false,
                        default: param.default
                    })) || [],
                    returns: {
                        type: func.returnType || 'void',
                        description: func.returnDescription || 'Return value'
                    },
                    examples: func.examples || [],
                    throws: func.throws
                }));
            }
            // Extract classes
            if (codeAnalysis.symbols?.classes) {
                api.classes = codeAnalysis.symbols.classes.map(cls => ({
                    name: cls.name,
                    description: cls.description || `${cls.name} class`,
                    constructor: {
                        parameters: cls.constructor?.parameters?.map(param => ({
                            name: param.name,
                            type: param.type || 'any',
                            description: param.description || `${param.name} parameter`,
                            required: param.required !== false
                        })) || []
                    },
                    methods: cls.methods?.map(method => ({
                        name: method.name,
                        description: method.description || `${method.name} method`,
                        parameters: method.parameters?.map(param => ({
                            name: param.name,
                            type: param.type || 'any',
                            description: param.description || `${param.name} parameter`,
                            required: param.required !== false
                        })) || [],
                        returns: {
                            type: method.returnType || 'void',
                            description: method.returnDescription || 'Return value'
                        },
                        visibility: method.visibility || 'public'
                    })) || [],
                    properties: cls.properties?.map(prop => ({
                        name: prop.name,
                        type: prop.type || 'any',
                        description: prop.description || `${prop.name} property`,
                        visibility: prop.visibility || 'public'
                    })) || []
                }));
            }
            // Extract interfaces (for TypeScript/similar languages)
            if (codeAnalysis.symbols?.interfaces) {
                api.interfaces = codeAnalysis.symbols.interfaces.map(iface => ({
                    name: iface.name,
                    description: iface.description || `${iface.name} interface`,
                    properties: iface.properties?.map(prop => ({
                        name: prop.name,
                        type: prop.type || 'any',
                        description: prop.description || `${prop.name} property`,
                        required: prop.required !== false
                    })) || []
                }));
            }
            return api;
        }
        catch (error) {
            utils_1.logger.error('API documentation generation failed', error);
            return { functions: [], classes: [], interfaces: [] };
        }
    }
    /**
     * Generate code examples
     */
    async generateCodeExamples(projectPath, language) {
        try {
            const examples = [];
            // Basic usage example
            examples.push({
                title: 'Basic Usage',
                description: 'Basic example of how to use this project',
                code: this.generateBasicUsageExample(language),
                language
            });
            // Advanced example
            examples.push({
                title: 'Advanced Usage',
                description: 'Advanced example with configuration options',
                code: this.generateAdvancedUsageExample(language),
                language
            });
            return examples;
        }
        catch (error) {
            utils_1.logger.error('Code examples generation failed', error);
            return [];
        }
    }
    /**
     * Export documentation in specified format
     */
    async exportDocumentation(documentation, format, outputPath) {
        try {
            let content;
            switch (format) {
                case 'markdown':
                    content = this.exportToMarkdown(documentation);
                    break;
                case 'html':
                    content = this.exportToHTML(documentation);
                    break;
                case 'json':
                    content = JSON.stringify(documentation, null, 2);
                    break;
                case 'pdf':
                    content = this.exportToPDF(documentation);
                    break;
                default:
                    throw new Error(`Unsupported format: ${format}`);
            }
            if (outputPath) {
                const fs = await Promise.resolve().then(() => __importStar(require('fs')));
                fs.writeFileSync(outputPath, content, 'utf-8');
                utils_1.logger.info('Documentation exported', { format, outputPath });
            }
            return content;
        }
        catch (error) {
            utils_1.logger.error('Documentation export failed', error);
            throw error;
        }
    }
    // Private helper methods
    async analyzeProject(projectPath) {
        // Analyze project structure, package.json, README, etc.
        const fs = await Promise.resolve().then(() => __importStar(require('fs')));
        const path = await Promise.resolve().then(() => __importStar(require('path')));
        const analysis = {
            name: path.basename(projectPath),
            description: '',
            version: '1.0.0',
            files: [],
            dependencies: [],
            scripts: {},
            type: 'general'
        };
        try {
            // Try to read package.json
            const packageJsonPath = path.join(projectPath, 'package.json');
            if (fs.existsSync(packageJsonPath)) {
                const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
                analysis.name = packageJson.name || analysis.name;
                analysis.description = packageJson.description || analysis.description;
                analysis.version = packageJson.version || analysis.version;
                analysis.dependencies = Object.keys(packageJson.dependencies || {});
                analysis.scripts = packageJson.scripts || {};
                analysis.type = 'nodejs';
            }
            // Try to read composer.json
            const composerJsonPath = path.join(projectPath, 'composer.json');
            if (fs.existsSync(composerJsonPath)) {
                const composerJson = JSON.parse(fs.readFileSync(composerJsonPath, 'utf-8'));
                analysis.name = composerJson.name || analysis.name;
                analysis.description = composerJson.description || analysis.description;
                analysis.version = composerJson.version || analysis.version;
                analysis.type = 'php';
            }
            // Try to read README
            const readmePaths = ['README.md', 'README.txt', 'README'];
            for (const readmePath of readmePaths) {
                const fullReadmePath = path.join(projectPath, readmePath);
                if (fs.existsSync(fullReadmePath)) {
                    const readmeContent = fs.readFileSync(fullReadmePath, 'utf-8');
                    if (!analysis.description && readmeContent.length > 0) {
                        // Extract first paragraph as description
                        const firstParagraph = readmeContent.split('\n\n')[0];
                        analysis.description = firstParagraph.replace(/^#+\s*/, '').trim();
                    }
                    break;
                }
            }
            // Scan for files
            analysis.files = this.scanProjectFiles(projectPath);
        }
        catch (error) {
            utils_1.logger.warn('Project analysis partial failure', error);
        }
        return analysis;
    }
    scanProjectFiles(projectPath) {
        const fs = require('fs');
        const path = require('path');
        const files = [];
        try {
            const items = fs.readdirSync(projectPath);
            for (const item of items) {
                const itemPath = path.join(projectPath, item);
                const stat = fs.statSync(itemPath);
                if (stat.isFile()) {
                    files.push(item);
                }
            }
        }
        catch (error) {
            utils_1.logger.warn('File scanning failed', error);
        }
        return files;
    }
    async generateOverviewSection(projectAnalysis) {
        return {
            title: 'Overview',
            content: `# ${projectAnalysis.name}

${projectAnalysis.description}

## Features

- Modern ${projectAnalysis.type} project
- Well-structured codebase
- Comprehensive documentation
- Easy to use and extend

## Installation

\`\`\`bash
# Clone the repository
git clone <repository-url>
cd ${projectAnalysis.name}

# Install dependencies
${this.getInstallCommand(projectAnalysis.type)}
\`\`\`

## Quick Start

${this.generateQuickStartGuide(projectAnalysis)}`,
            level: 1,
            type: 'overview'
        };
    }
    async generateExamplesSection(projectAnalysis) {
        const examples = await this.generateCodeExamples('.', projectAnalysis.type);
        let content = '# Examples\n\n';
        examples.forEach(example => {
            content += `## ${example.title}\n\n`;
            content += `${example.description}\n\n`;
            content += `\`\`\`${example.language}\n${example.code}\n\`\`\`\n\n`;
        });
        return {
            title: 'Examples',
            content,
            level: 1,
            type: 'example'
        };
    }
    async generateArchitectureDocumentation(projectAnalysis) {
        return {
            overview: 'This project follows modern architectural patterns and best practices.',
            components: [
                {
                    name: 'Core',
                    description: 'Main application logic',
                    dependencies: [],
                    type: 'module'
                }
            ],
            patterns: ['MVC', 'Repository', 'Factory']
        };
    }
    async generateDeploymentDocumentation(projectAnalysis) {
        return {
            requirements: [
                `${projectAnalysis.type} runtime`,
                'Package manager',
                'Environment configuration'
            ],
            steps: [
                'Install dependencies',
                'Configure environment',
                'Build application',
                'Deploy to target environment'
            ],
            configuration: {
                environment: 'production',
                port: 3000
            },
            environments: ['development', 'staging', 'production']
        };
    }
    async generateTestingDocumentation(projectAnalysis) {
        return {
            framework: this.getTestFramework(projectAnalysis.type),
            coverage: 85,
            testTypes: ['unit', 'integration', 'e2e'],
            runInstructions: [
                `${this.getTestCommand(projectAnalysis.type)}`,
                'npm run test:coverage',
                'npm run test:e2e'
            ]
        };
    }
    formatAPIDocumentation(api) {
        let content = '# API Reference\n\n';
        // Functions
        if (api.functions.length > 0) {
            content += '## Functions\n\n';
            api.functions.forEach(func => {
                content += `### ${func.name}\n\n`;
                content += `${func.description}\n\n`;
                if (func.parameters.length > 0) {
                    content += '**Parameters:**\n\n';
                    func.parameters.forEach(param => {
                        const required = param.required ? ' (required)' : ' (optional)';
                        content += `- \`${param.name}\` (${param.type})${required}: ${param.description}\n`;
                    });
                    content += '\n';
                }
                content += `**Returns:** ${func.returns.type} - ${func.returns.description}\n\n`;
                if (func.examples.length > 0) {
                    content += '**Example:**\n\n';
                    content += `\`\`\`javascript\n${func.examples[0]}\n\`\`\`\n\n`;
                }
            });
        }
        // Classes
        if (api.classes.length > 0) {
            content += '## Classes\n\n';
            api.classes.forEach(cls => {
                content += `### ${cls.name}\n\n`;
                content += `${cls.description}\n\n`;
                if (cls.methods.length > 0) {
                    content += '**Methods:**\n\n';
                    cls.methods.forEach(method => {
                        content += `#### ${method.name}\n\n`;
                        content += `${method.description}\n\n`;
                    });
                }
            });
        }
        return content;
    }
    formatArchitectureDocumentation(architecture) {
        let content = '# Architecture\n\n';
        content += `${architecture.overview}\n\n`;
        content += '## Components\n\n';
        architecture.components.forEach((component) => {
            content += `### ${component.name}\n\n`;
            content += `${component.description}\n\n`;
            content += `Type: ${component.type}\n\n`;
        });
        content += '## Patterns\n\n';
        architecture.patterns.forEach((pattern) => {
            content += `- ${pattern}\n`;
        });
        return content;
    }
    formatDeploymentDocumentation(deployment) {
        let content = '# Deployment\n\n';
        content += '## Requirements\n\n';
        deployment.requirements.forEach((req) => {
            content += `- ${req}\n`;
        });
        content += '\n';
        content += '## Steps\n\n';
        deployment.steps.forEach((step, index) => {
            content += `${index + 1}. ${step}\n`;
        });
        return content;
    }
    formatTestingDocumentation(testing) {
        let content = '# Testing\n\n';
        content += `Framework: ${testing.framework}\n\n`;
        content += `Coverage: ${testing.coverage}%\n\n`;
        content += '## Test Types\n\n';
        testing.testTypes.forEach((type) => {
            content += `- ${type}\n`;
        });
        content += '\n';
        content += '## Running Tests\n\n';
        testing.runInstructions.forEach((instruction) => {
            content += `\`\`\`bash\n${instruction}\n\`\`\`\n\n`;
        });
        return content;
    }
    exportToMarkdown(documentation) {
        let content = `# ${documentation.title}\n\n`;
        content += `${documentation.description}\n\n`;
        content += `Version: ${documentation.version}\n\n`;
        documentation.sections.forEach(section => {
            content += section.content + '\n\n';
        });
        return content;
    }
    exportToHTML(documentation) {
        const markdown = this.exportToMarkdown(documentation);
        // In a real implementation, this would use a markdown-to-HTML converter
        return `<!DOCTYPE html>
<html>
<head>
    <title>${documentation.title}</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        code { background: #f5f5f5; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <pre>${markdown}</pre>
</body>
</html>`;
    }
    exportToPDF(documentation) {
        // In a real implementation, this would generate actual PDF content
        return this.exportToMarkdown(documentation);
    }
    getInstallCommand(projectType) {
        const commands = {
            nodejs: 'npm install',
            php: 'composer install',
            python: 'pip install -r requirements.txt',
            general: '# Follow project-specific installation instructions'
        };
        return commands[projectType] || commands.general;
    }
    getTestFramework(projectType) {
        const frameworks = {
            nodejs: 'Jest',
            php: 'PHPUnit',
            python: 'pytest',
            general: 'Project-specific'
        };
        return frameworks[projectType] || frameworks.general;
    }
    getTestCommand(projectType) {
        const commands = {
            nodejs: 'npm test',
            php: 'vendor/bin/phpunit',
            python: 'pytest',
            general: '# Run project-specific test command'
        };
        return commands[projectType] || commands.general;
    }
    generateQuickStartGuide(projectAnalysis) {
        return `Follow these steps to get started:

1. Install dependencies
2. Configure your environment
3. Run the application
4. Start building!`;
    }
    generateBasicUsageExample(language) {
        const examples = {
            javascript: `const project = require('./index');

// Basic usage
const result = project.run();
console.log(result);`,
            php: `<?php
require_once 'vendor/autoload.php';

// Basic usage
$project = new Project();
$result = $project->run();
echo $result;`,
            python: `from project import main

# Basic usage
result = main()
print(result)`,
            general: `// Basic usage example
// Replace with actual code`
        };
        return examples[language] || examples.general;
    }
    generateAdvancedUsageExample(language) {
        const examples = {
            javascript: `const project = require('./index');

// Advanced usage with configuration
const config = {
  option1: 'value1',
  option2: true
};

const result = project.run(config);
console.log(result);`,
            php: `<?php
require_once 'vendor/autoload.php';

// Advanced usage with configuration
$config = [
    'option1' => 'value1',
    'option2' => true
];

$project = new Project($config);
$result = $project->run();
echo $result;`,
            python: `from project import main

# Advanced usage with configuration
config = {
    'option1': 'value1',
    'option2': True
}

result = main(config)
print(result)`,
            general: `// Advanced usage example
// Replace with actual code`
        };
        return examples[language] || examples.general;
    }
}
exports.DocumentationGenerator = DocumentationGenerator;
//# sourceMappingURL=index.js.map