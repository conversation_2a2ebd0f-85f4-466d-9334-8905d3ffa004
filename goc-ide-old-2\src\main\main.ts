/**
 * GOC IDE - Main Process
 * Professional AI-powered code editor
 */

import { app, BrowserWindow, Menu, ipcMain, dialog, shell } from 'electron';
import { autoUpdater } from 'electron-updater';
import * as log from 'electron-log';
import * as path from 'path';
import { createGocCore, GocCore } from '@goc-agent/core';
import { WindowManager } from './windowManager';
import { MenuManager } from './menuManager';
import { IPCManager } from './ipcManager';
import { ConfigManager } from './configManager';
import { FileSystemManager } from './fileSystemManager';

class GOCIDEMain {
  private windowManager: WindowManager;
  private menuManager: MenuManager;
  private ipcManager: IPCManager;
  private configManager: ConfigManager;
  private fileSystemManager: FileSystemManager;
  private gocCore: GocCore | null = null;

  constructor() {
    this.initializeManagers();
    this.initializeApp();
    this.setupAutoUpdater();
  }

  private initializeManagers(): void {
    this.configManager = new ConfigManager();
    this.windowManager = new WindowManager();
    this.menuManager = new MenuManager();
    this.fileSystemManager = new FileSystemManager();
    this.ipcManager = new IPCManager(this.configManager, this.fileSystemManager);
  }

  private initializeApp(): void {
    // Set app user model ID for Windows
    if (process.platform === 'win32') {
      app.setAppUserModelId('com.goc-agent.ide');
    }

    // Handle app ready
    app.whenReady().then(async () => {
      await this.onAppReady();
    });

    // Handle all windows closed
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // Handle app activation (macOS)
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.windowManager.createMainWindow();
      }
    });

    // Handle before quit
    app.on('before-quit', async () => {
      await this.cleanup();
    });

    // Security: Prevent new window creation
    app.on('web-contents-created', (event, contents) => {
      contents.on('new-window', (event, navigationUrl) => {
        event.preventDefault();
        shell.openExternal(navigationUrl);
      });
    });
  }

  private async onAppReady(): Promise<void> {
    try {
      // Initialize GOC Core
      await this.initializeGocCore();

      // Create main window
      const mainWindow = this.windowManager.createMainWindow();

      // Setup menu
      this.menuManager.setupMenu(mainWindow);

      // Setup IPC handlers
      this.ipcManager.setupHandlers(this.gocCore);

      // Setup file system watchers
      this.fileSystemManager.initialize();

      log.info('GOC IDE initialized successfully');

    } catch (error) {
      log.error('Failed to initialize GOC IDE:', error);
      dialog.showErrorBox('Initialization Error', 
        `Failed to start GOC IDE: ${error}`);
    }
  }

  private async initializeGocCore(): Promise<void> {
    try {
      const config = await this.configManager.getConfig();
      
      const options = {
        ai: {
          defaultProvider: config.defaultAiProvider,
          defaultModel: config.defaultAiModel
        },
        global: {
          logLevel: config.logLevel,
          cacheDirectory: path.join(app.getPath('userData'), 'cache'),
          tempDirectory: path.join(app.getPath('temp'), 'goc-ide')
        }
      };

      this.gocCore = await createGocCore(options);
      log.info('GOC Core initialized successfully');

    } catch (error) {
      log.error('Failed to initialize GOC Core:', error);
      // Continue without GOC Core functionality
    }
  }

  private setupAutoUpdater(): void {
    // Configure auto-updater
    autoUpdater.logger = log;
    autoUpdater.checkForUpdatesAndNotify();

    // Auto-updater events
    autoUpdater.on('checking-for-update', () => {
      log.info('Checking for update...');
    });

    autoUpdater.on('update-available', (info) => {
      log.info('Update available:', info);
    });

    autoUpdater.on('update-not-available', (info) => {
      log.info('Update not available:', info);
    });

    autoUpdater.on('error', (err) => {
      log.error('Error in auto-updater:', err);
    });

    autoUpdater.on('download-progress', (progressObj) => {
      let logMessage = `Download speed: ${progressObj.bytesPerSecond}`;
      logMessage += ` - Downloaded ${progressObj.percent}%`;
      logMessage += ` (${progressObj.transferred}/${progressObj.total})`;
      log.info(logMessage);
    });

    autoUpdater.on('update-downloaded', (info) => {
      log.info('Update downloaded:', info);
      autoUpdater.quitAndInstall();
    });
  }

  private async cleanup(): Promise<void> {
    try {
      if (this.gocCore) {
        await this.gocCore.dispose();
      }
      
      this.fileSystemManager.dispose();
      this.ipcManager.dispose();
      
      log.info('GOC IDE cleanup completed');
    } catch (error) {
      log.error('Error during cleanup:', error);
    }
  }
}

// Initialize the application
new GOCIDEMain();
