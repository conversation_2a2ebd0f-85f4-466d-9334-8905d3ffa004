/**
 * Learning Engine
 *
 * Core learning system that orchestrates pattern recognition, knowledge storage,
 * and context enhancement across all AI providers
 */
import { LearningPattern, UserPreference, LearningMetrics, PatternType, AIMessage, AIResponse, GocConfig } from '../types';
export interface LearningEngineOptions {
    config: GocConfig;
    userId?: string;
    projectId?: string;
    enableAutoLearning?: boolean;
}
export declare class LearningEngine {
    private config;
    private userId?;
    private projectId?;
    private enableAutoLearning;
    private patternRecognizer;
    private knowledgeBase;
    private contextEnhancer;
    private learningMonitor;
    private webLearner;
    private initialized;
    constructor(options: LearningEngineOptions);
    initialize(): Promise<void>;
    /**
     * Learn from user interaction with AI response
     */
    learnFromInteraction(messages: AIMessage[], response: AIResponse, userFeedback?: {
        accepted: boolean;
        modifications?: string;
        rating?: number;
    }): Promise<void>;
    /**
     * Enhance AI messages with learned context and patterns
     */
    enhanceMessages(messages: AIMessage[]): Promise<AIMessage[]>;
    /**
     * Learn from web research results
     */
    learnFromWebResearch(query: string, results: any[], relevantContent: string[]): Promise<void>;
    /**
     * Get learning metrics and analytics
     */
    getMetrics(): Promise<LearningMetrics>;
    /**
     * Get user preferences
     */
    getUserPreferences(): Promise<UserPreference[]>;
    /**
     * Update user preference
     */
    updateUserPreference(preference: Partial<UserPreference>): Promise<void>;
    /**
     * Search for relevant patterns
     */
    searchPatterns(query: string, options?: {
        type?: PatternType;
        limit?: number;
        minConfidence?: number;
    }): Promise<LearningPattern[]>;
    /**
     * Record a learning event
     */
    private recordLearningEvent;
    /**
     * Generate unique ID
     */
    private generateId;
    /**
     * Dispose of resources
     */
    dispose(): Promise<void>;
}
//# sourceMappingURL=learning-engine.d.ts.map