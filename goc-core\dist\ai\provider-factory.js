"use strict";
/**
 * AI Provider Factory
 *
 * Creates and manages AI provider instances
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIProviderFactory = void 0;
const types_1 = require("../types");
const ollama_provider_1 = require("./providers/ollama-provider");
// Other providers hidden for now
// import { GroqProvider } from './providers/groq-provider';
// import { OpenAIProvider } from './providers/openai-provider';
// import { ClaudeProvider } from './providers/claude-provider';
// import { GeminiProvider } from './providers/gemini-provider';
class AIProviderFactory {
    constructor() {
        this.providers = new Map();
        this.registerDefaultProviders();
    }
    static getInstance() {
        if (!AIProviderFactory.instance) {
            AIProviderFactory.instance = new AIProviderFactory();
        }
        return AIProviderFactory.instance;
    }
    createProvider(config) {
        const validation = this.validateConfig(config);
        if (!validation.success) {
            throw new types_1.AIProviderError(`Invalid provider configuration: ${validation.error}`, config.name);
        }
        const ProviderClass = this.providers.get(config.name);
        if (!ProviderClass) {
            throw new types_1.AIProviderError(`Unsupported provider: ${config.name}`, config.name);
        }
        try {
            return new ProviderClass(config.apiKey || '', config.baseUrl, config.defaultModel, config.timeout || 30000);
        }
        catch (error) {
            throw new types_1.AIProviderError(`Failed to create provider ${config.name}: ${error instanceof Error ? error.message : 'Unknown error'}`, config.name, { originalError: error });
        }
    }
    getSupportedProviders() {
        return Array.from(this.providers.keys());
    }
    isProviderSupported(provider) {
        return this.providers.has(provider);
    }
    validateConfig(config) {
        const errors = [];
        // Basic validation
        if (!config) {
            errors.push('Configuration is required');
        }
        else {
            if (!config.name) {
                errors.push('Provider name is required');
            }
            else if (!this.isProviderSupported(config.name)) {
                errors.push(`Unsupported provider: ${config.name}`);
            }
            if (!config.baseUrl) {
                errors.push('Base URL is required');
            }
            else if (!this.isValidUrl(config.baseUrl)) {
                errors.push('Invalid base URL format');
            }
            if (!config.defaultModel) {
                errors.push('Default model is required');
            }
            if (config.timeout !== undefined) {
                if (typeof config.timeout !== 'number' || config.timeout < 1000) {
                    errors.push('Timeout must be a number >= 1000ms');
                }
            }
            if (config.temperature !== undefined) {
                if (typeof config.temperature !== 'number' || config.temperature < 0 || config.temperature > 2) {
                    errors.push('Temperature must be a number between 0 and 2');
                }
            }
            if (config.maxTokens !== undefined) {
                if (typeof config.maxTokens !== 'number' || config.maxTokens < 1) {
                    errors.push('Max tokens must be a positive number');
                }
            }
            // Validate models array
            if (config.models) {
                if (!Array.isArray(config.models)) {
                    errors.push('Models must be an array');
                }
                else {
                    for (let i = 0; i < config.models.length; i++) {
                        const model = config.models[i];
                        if (!model.id) {
                            errors.push(`Model ${i}: ID is required`);
                        }
                        if (!model.name) {
                            errors.push(`Model ${i}: Name is required`);
                        }
                        if (typeof model.contextLength !== 'number' || model.contextLength < 1) {
                            errors.push(`Model ${i}: Context length must be a positive number`);
                        }
                        if (!Array.isArray(model.capabilities)) {
                            errors.push(`Model ${i}: Capabilities must be an array`);
                        }
                    }
                }
            }
        }
        return {
            success: errors.length === 0,
            error: errors.length > 0 ? errors.join(', ') : undefined,
            timestamp: new Date()
        };
    }
    registerProvider(name, providerClass) {
        this.providers.set(name, providerClass);
    }
    unregisterProvider(name) {
        this.providers.delete(name);
    }
    registerDefaultProviders() {
        // Only Ollama is available for now
        this.providers.set('ollama', ollama_provider_1.OllamaProvider);
        // Other providers hidden - will be enabled later
        // this.providers.set('groq', GroqProvider);
        // this.providers.set('openai', OpenAIProvider);
        // this.providers.set('claude', ClaudeProvider);
        // this.providers.set('gemini', GeminiProvider);
    }
    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        }
        catch {
            return false;
        }
    }
    // Utility methods for provider management
    async testProvider(config) {
        try {
            const provider = this.createProvider(config);
            const isValid = await provider.validateApiKey();
            return {
                success: isValid,
                error: isValid ? undefined : 'API key validation failed',
                timestamp: new Date()
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date()
            };
        }
    }
    async getProviderModels(config) {
        try {
            const provider = this.createProvider(config);
            const models = await provider.getModels();
            return {
                success: true,
                data: models,
                timestamp: new Date()
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date()
            };
        }
    }
}
exports.AIProviderFactory = AIProviderFactory;
//# sourceMappingURL=provider-factory.js.map