/**
 * Web Learner
 *
 * Automatically learns from web research results and integrates knowledge
 * into the learning system
 */
import { LearningPattern, GocConfig } from '../types';
export interface WebLearningContext {
    userId?: string;
    projectId?: string;
    language?: string;
    framework?: string;
    domain?: string;
}
export interface WebLearningResult {
    patternsLearned: number;
    knowledgeExtracted: string[];
    confidence: number;
    sources: string[];
}
export declare class WebLearner {
    private config;
    private knowledgeBase;
    private initialized;
    private readonly PATTERN_KEYWORDS;
    constructor(config: GocConfig);
    initialize(): Promise<void>;
    /**
     * Learn from web research results
     */
    learnFromResults(query: string, results: any[], relevantContent: string[], context?: WebLearningContext): Promise<WebLearningResult>;
    /**
     * Learn from documentation content
     */
    learnFromDocumentation(documentation: string, source: string, context?: WebLearningContext): Promise<LearningPattern[]>;
    /**
     * Learn from code repositories
     */
    learnFromRepository(repoContent: string[], repoUrl: string, context?: WebLearningContext): Promise<LearningPattern[]>;
    private extractPatternsFromContent;
    private extractKnowledge;
    private extractApiPatterns;
    private extractConfigurationPatterns;
    private extractUsageExamples;
    private extractProjectStructure;
    private extractCodingPatterns;
    private extractDependencyPatterns;
    private calculateKeywordRelevance;
    private calculateLearningConfidence;
    private isInformativeSentence;
    private createWebPattern;
    private createPattern;
    private extractRelevantContent;
    private extractCodeBlocks;
    private looksLikeConfiguration;
    private looksLikeUsageExample;
    private detectConfigType;
    private detectLanguage;
    private estimateComplexity;
    private containsCodingPattern;
    private identifyCodingPatternType;
    private getEcosystem;
    private assessContentQuality;
    private assessQueryRelevance;
    private generatePatternId;
    dispose(): Promise<void>;
}
//# sourceMappingURL=web-learner.d.ts.map