/**
 * Window Manager - Handles window controls and events
 */

export class WindowManager {
  private isMaximized = false;

  public initialize(): void {
    console.log('Initializing Window Manager...');

    this.setupWindowControls();
    this.setupWindowEvents();

    console.log('Window Manager initialized');
  }

  private setupWindowControls(): void {
    // Minimize button
    const minimizeBtn = document.getElementById('minimize-btn');
    if (minimizeBtn) {
      minimizeBtn.addEventListener('click', () => {
        window.electronAPI.window.minimize();
      });
    }

    // Maximize/Restore button
    const maximizeBtn = document.getElementById('maximize-btn');
    if (maximizeBtn) {
      maximizeBtn.addEventListener('click', () => {
        window.electronAPI.window.maximize();
      });
    }

    // Close button
    const closeBtn = document.getElementById('close-btn');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        window.electronAPI.window.close();
      });
    }
  }

  private setupWindowEvents(): void {
    // Listen for window state changes
    window.electronAPI.on('window-maximized', (maximized: boolean) => {
      this.isMaximized = maximized;
      this.updateMaximizeButton();
    });

    // Listen for theme changes
    window.electronAPI.on('theme-changed', (themeInfo: any) => {
      this.handleThemeChange(themeInfo);
    });

    // Handle double-click on title bar
    const titlebar = document.getElementById('titlebar');
    if (titlebar) {
      titlebar.addEventListener('dblclick', () => {
        window.electronAPI.window.maximize();
      });
    }
  }

  private updateMaximizeButton(): void {
    const maximizeBtn = document.getElementById('maximize-btn');
    if (maximizeBtn) {
      const svg = maximizeBtn.querySelector('svg');
      if (svg) {
        if (this.isMaximized) {
          // Show restore icon
          svg.innerHTML = `
            <rect x="2" y="2" width="6" height="6" stroke="currentColor" stroke-width="1" fill="none"/>
            <rect x="4" y="4" width="6" height="6" stroke="currentColor" stroke-width="1" fill="none"/>
          `;
          maximizeBtn.title = 'Restore';
        } else {
          // Show maximize icon
          svg.innerHTML = `
            <rect x="2" y="2" width="8" height="8" stroke="currentColor" stroke-width="1" fill="none"/>
          `;
          maximizeBtn.title = 'Maximize';
        }
      }
    }
  }

  private handleThemeChange(themeInfo: any): void {
    console.log('Theme changed:', themeInfo);
    
    // Update CSS variables or classes based on theme
    if (themeInfo.shouldUseDarkColors) {
      document.body.classList.add('dark-theme');
      document.body.classList.remove('light-theme');
    } else {
      document.body.classList.add('light-theme');
      document.body.classList.remove('dark-theme');
    }

    if (themeInfo.shouldUseHighContrastColors) {
      document.body.classList.add('high-contrast');
    } else {
      document.body.classList.remove('high-contrast');
    }
  }

  public getIsMaximized(): boolean {
    return this.isMaximized;
  }
}
