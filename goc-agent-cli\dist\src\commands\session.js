"use strict";
/**
 * Session Management Command
 *
 * Handle chat sessions with GOC Agent backend
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionCommand = void 0;
const chalk_1 = __importDefault(require("chalk"));
const inquirer_1 = __importDefault(require("inquirer"));
class SessionCommand {
    constructor(apiClient) {
        this.apiClient = apiClient;
    }
    register(program) {
        const sessionCmd = program
            .command('session')
            .alias('s')
            .description('Session management commands');
        sessionCmd
            .command('list')
            .alias('ls')
            .description('List all sessions')
            .action(async () => {
            await this.listSessions();
        });
        sessionCmd
            .command('create')
            .alias('new')
            .description('Create a new session')
            .option('-t, --title <title>', 'Session title')
            .action(async (options) => {
            await this.createSession(options);
        });
        sessionCmd
            .command('show <sessionId>')
            .description('Show session details')
            .action(async (sessionId) => {
            await this.showSession(sessionId);
        });
        sessionCmd
            .command('delete <sessionId>')
            .alias('rm')
            .description('Delete a session')
            .option('-f, --force', 'Force delete without confirmation')
            .action(async (sessionId, options) => {
            await this.deleteSession(sessionId, options);
        });
        sessionCmd
            .command('rename <sessionId>')
            .description('Rename a session')
            .option('-t, --title <title>', 'New session title')
            .action(async (sessionId, options) => {
            await this.renameSession(sessionId, options);
        });
    }
    async listSessions() {
        try {
            if (!this.apiClient.isAuthenticated()) {
                console.log(chalk_1.default.red('❌ Not authenticated'));
                console.log(chalk_1.default.dim('Use "goc auth login" to authenticate'));
                return;
            }
            console.log(chalk_1.default.blue('📋 Your Sessions'));
            console.log(chalk_1.default.dim('Fetching sessions...'));
            const sessions = await this.apiClient.getSessions();
            if (sessions.length === 0) {
                console.log(chalk_1.default.yellow('No sessions found.'));
                console.log(chalk_1.default.dim('Use "goc session create" to create a new session.'));
                return;
            }
            console.log(chalk_1.default.dim('─'.repeat(80)));
            console.log(chalk_1.default.bold('ID'.padEnd(8)) + chalk_1.default.bold('Title'.padEnd(30)) + chalk_1.default.bold('Status'.padEnd(12)) + chalk_1.default.bold('Created'));
            console.log(chalk_1.default.dim('─'.repeat(80)));
            sessions.forEach(session => {
                const statusColor = session.status === 'active' ? chalk_1.default.green : chalk_1.default.yellow;
                const createdDate = new Date(session.created_at).toLocaleDateString();
                console.log(session.id.substring(0, 8).padEnd(8) +
                    session.title.substring(0, 28).padEnd(30) +
                    statusColor(session.status.padEnd(12)) +
                    chalk_1.default.dim(createdDate));
            });
            console.log(chalk_1.default.dim('─'.repeat(80)));
            console.log(chalk_1.default.dim(`Total: ${sessions.length} sessions`));
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Failed to list sessions:'), error);
        }
    }
    async createSession(options) {
        try {
            if (!this.apiClient.isAuthenticated()) {
                console.log(chalk_1.default.red('❌ Not authenticated'));
                console.log(chalk_1.default.dim('Use "goc auth login" to authenticate'));
                return;
            }
            let title = options.title;
            if (!title) {
                const answers = await inquirer_1.default.prompt([
                    {
                        type: 'input',
                        name: 'title',
                        message: 'Session title:',
                        default: `Session ${new Date().toLocaleString()}`,
                        validate: (input) => input.trim().length > 0 || 'Title cannot be empty'
                    }
                ]);
                title = answers.title;
            }
            console.log(chalk_1.default.dim('Creating session...'));
            const session = await this.apiClient.createSession(title);
            console.log(chalk_1.default.green('✅ Session created successfully!'));
            console.log(chalk_1.default.dim(`ID: ${session.id}`));
            console.log(chalk_1.default.dim(`Title: ${session.title}`));
            console.log(chalk_1.default.dim(`Status: ${session.status}`));
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Failed to create session:'), error);
        }
    }
    async showSession(sessionId) {
        try {
            if (!this.apiClient.isAuthenticated()) {
                console.log(chalk_1.default.red('❌ Not authenticated'));
                console.log(chalk_1.default.dim('Use "goc auth login" to authenticate'));
                return;
            }
            console.log(chalk_1.default.dim('Fetching session details...'));
            const session = await this.apiClient.getSession(sessionId);
            console.log(chalk_1.default.blue('📄 Session Details'));
            console.log(chalk_1.default.dim('─'.repeat(40)));
            console.log(`ID: ${session.id}`);
            console.log(`Title: ${session.title}`);
            console.log(`Status: ${session.status}`);
            if (session.provider) {
                console.log(`Provider: ${session.provider}`);
            }
            if (session.model) {
                console.log(`Model: ${session.model}`);
            }
            console.log(`Created: ${new Date(session.created_at).toLocaleString()}`);
            console.log(`Updated: ${new Date(session.updated_at).toLocaleString()}`);
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Failed to get session:'), error);
        }
    }
    async deleteSession(sessionId, options) {
        try {
            if (!this.apiClient.isAuthenticated()) {
                console.log(chalk_1.default.red('❌ Not authenticated'));
                console.log(chalk_1.default.dim('Use "goc auth login" to authenticate'));
                return;
            }
            if (!options.force) {
                const { confirm } = await inquirer_1.default.prompt([
                    {
                        type: 'confirm',
                        name: 'confirm',
                        message: `Are you sure you want to delete session ${sessionId}?`,
                        default: false
                    }
                ]);
                if (!confirm) {
                    console.log(chalk_1.default.yellow('Delete cancelled.'));
                    return;
                }
            }
            console.log(chalk_1.default.dim('Deleting session...'));
            await this.apiClient.deleteSession(sessionId);
            console.log(chalk_1.default.green('✅ Session deleted successfully!'));
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Failed to delete session:'), error);
        }
    }
    async renameSession(sessionId, options) {
        try {
            if (!this.apiClient.isAuthenticated()) {
                console.log(chalk_1.default.red('❌ Not authenticated'));
                console.log(chalk_1.default.dim('Use "goc auth login" to authenticate'));
                return;
            }
            let title = options.title;
            if (!title) {
                const answers = await inquirer_1.default.prompt([
                    {
                        type: 'input',
                        name: 'title',
                        message: 'New session title:',
                        validate: (input) => input.trim().length > 0 || 'Title cannot be empty'
                    }
                ]);
                title = answers.title;
            }
            console.log(chalk_1.default.dim('Updating session...'));
            const session = await this.apiClient.updateSession(sessionId, { title });
            console.log(chalk_1.default.green('✅ Session renamed successfully!'));
            console.log(chalk_1.default.dim(`New title: ${session.title}`));
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Failed to rename session:'), error);
        }
    }
}
exports.SessionCommand = SessionCommand;
//# sourceMappingURL=session.js.map