/**
 * Core Types for GOC Agent
 *
 * Shared type definitions used across the entire GOC Agent ecosystem
 */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';
export type AIProvider = 'ollama' | 'goc';
export type AnalysisDepth = 'shallow' | 'medium' | 'deep';
export type ModelTask = 'code-generation' | 'code-explanation' | 'code-review' | 'debugging' | 'refactoring' | 'documentation' | 'testing' | 'general-chat';
export interface BaseResult {
    success: boolean;
    error?: string;
    data?: any;
    timestamp: Date;
}
export interface AIMessage {
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp?: Date;
    metadata?: Record<string, any>;
}
export interface AIResponse {
    content: string;
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
    model?: string;
    provider?: string;
    timestamp: Date;
}
export interface ChatOptions {
    temperature?: number;
    maxTokens?: number;
    model?: string;
    provider?: string;
    stream?: boolean;
}
export interface AITool {
    name: string;
    description: string;
    parameters: {
        type: 'object';
        properties: Record<string, any>;
        required?: string[];
    };
}
export type ModelTier = 'free' | 'paid' | 'freemium';
export type AuthenticationStatus = 'none' | 'required' | 'authenticated';
export interface ModelPricing {
    inputCostPer1k?: number;
    outputCostPer1k?: number;
    requestCost?: number;
    currency: string;
}
export interface ModelInfo {
    id: string;
    name: string;
    provider: string;
    tier: ModelTier;
    contextLength: number;
    capabilities: string[];
    isAvailable: boolean;
    lastChecked: Date;
    pricing?: ModelPricing;
    description?: string;
    requiresAuth: boolean;
    isLocal: boolean;
    installCommand?: string;
    size?: string;
}
export interface AIModel {
    id: string;
    name: string;
    tier: ModelTier;
    contextLength: number;
    capabilities: string[];
    pricing?: ModelPricing;
    description?: string;
    requiresAuth: boolean;
    isLocal: boolean;
    installCommand?: string;
    size?: string;
}
export interface AIProviderConfig {
    name: AIProvider;
    displayName: string;
    tier: ModelTier;
    apiKey?: string;
    baseUrl: string;
    models: AIModel[];
    defaultModel: string;
    maxTokens: number;
    temperature: number;
    timeout: number;
    requiresAuth: boolean;
    isLocal: boolean;
    authenticationUrl?: string;
    setupInstructions?: string;
}
export interface LearningPattern {
    id: string;
    type: PatternType;
    content: string;
    context: string;
    frequency: number;
    confidence: number;
    tags: string[];
    metadata: Record<string, any>;
    createdAt: Date;
    lastUsed: Date;
    userId?: string;
    projectId?: string;
}
export interface UserPreference {
    id: string;
    userId: string;
    category: PreferenceCategory;
    key: string;
    value: any;
    confidence: number;
    learnedFrom: string[];
    createdAt: Date;
    updatedAt: Date;
}
export interface LearningEvent {
    id: string;
    type: LearningEventType;
    userId?: string;
    projectId?: string;
    data: Record<string, any>;
    patterns: string[];
    confidence: number;
    timestamp: Date;
}
export interface LearningMetrics {
    totalPatterns: number;
    activePatterns: number;
    userPreferences: number;
    learningEvents: number;
    averageConfidence: number;
    patternsByType: Record<PatternType, number>;
    recentActivity: LearningEvent[];
    topPatterns: LearningPattern[];
}
export type PatternType = 'coding-style' | 'naming-convention' | 'architecture-pattern' | 'code-snippet' | 'project-structure' | 'dependency-usage' | 'error-solution' | 'best-practice' | 'user-workflow';
export type PreferenceCategory = 'coding' | 'formatting' | 'architecture' | 'tools' | 'workflow' | 'ui' | 'ai-behavior';
export type LearningEventType = 'code-generated' | 'code-accepted' | 'code-modified' | 'code-rejected' | 'pattern-applied' | 'preference-learned' | 'web-research' | 'context-built' | 'error-resolved';
export interface GocConfig {
    version: string;
    ai: {
        defaultProvider: AIProvider;
        defaultModel: string;
        providers: Record<string, AIProviderConfig>;
        temperature: number;
        maxTokens: number;
        timeout: number;
    };
    context: {
        maxContextLines: number;
        enableSemanticSearch: boolean;
        cacheEnabled: boolean;
        cacheSize: number;
        analysisDepth: AnalysisDepth;
    };
    files: {
        maxFileSize: number;
        allowedExtensions: string[];
        backupEnabled: boolean;
        backupDirectory: string;
        autoSave: boolean;
    };
    web: {
        enabled: boolean;
        searchEngine: string;
        maxResults: number;
        timeout: number;
        userAgent: string;
        enableCaching: boolean;
    };
    learning: {
        enabled: boolean;
        autoLearn: boolean;
        maxPatterns: number;
        confidenceThreshold: number;
        retentionDays: number;
        enableWebLearning: boolean;
        enablePatternSharing: boolean;
        storageDirectory: string;
    };
    global: {
        logLevel: LogLevel;
        cacheDirectory: string;
        tempDirectory: string;
        enableTelemetry: boolean;
        autoUpdate: boolean;
    };
}
export interface ContextItem {
    id: string;
    type: 'file' | 'function' | 'class' | 'variable' | 'import';
    name: string;
    content: string;
    filePath: string;
    lineStart: number;
    lineEnd: number;
    language: string;
    relevanceScore?: number;
}
export interface AnalysisResult {
    summary: string;
    issues: Issue[];
    suggestions: Suggestion[];
    metrics: CodeMetrics;
    dependencies: string[];
}
export interface Issue {
    type: 'error' | 'warning' | 'info';
    message: string;
    line?: number;
    column?: number;
    severity: number;
}
export interface Suggestion {
    type: 'performance' | 'style' | 'security' | 'maintainability';
    message: string;
    line?: number;
    example?: string;
}
export interface CodeMetrics {
    linesOfCode: number;
    complexity: number;
    maintainabilityIndex: number;
    testCoverage?: number;
}
export interface FileInfo {
    path: string;
    name: string;
    extension: string;
    size: number;
    lastModified: Date;
    isDirectory: boolean;
    language?: string;
}
export interface FileContent {
    path: string;
    content: string;
    encoding: string;
    language: string;
    metadata?: Record<string, any>;
}
export interface SearchResult {
    title: string;
    url: string;
    snippet: string;
    relevanceScore?: number;
}
export interface WebContent {
    url: string;
    title: string;
    content: string;
    metadata: Record<string, any>;
    timestamp: Date;
}
export interface GocCoreOptions {
    ai?: Partial<GocConfig['ai']>;
    context?: Partial<GocConfig['context']>;
    files?: Partial<GocConfig['files']>;
    web?: Partial<GocConfig['web']>;
    learning?: Partial<GocConfig['learning']>;
    global?: Partial<GocConfig['global']>;
}
export interface GocEvent {
    type: string;
    data: any;
    timestamp: Date;
}
export type EventHandler = (event: GocEvent) => void;
export declare class GocError extends Error {
    code: string;
    details?: any;
    constructor(message: string, code: string, details?: any);
}
export declare class AIProviderError extends GocError {
    provider: string;
    constructor(message: string, provider: string, details?: any);
}
export declare class ConfigurationError extends GocError {
    constructor(message: string, details?: any);
}
export declare class FileOperationError extends GocError {
    filePath: string;
    constructor(message: string, filePath: string, details?: any);
}
//# sourceMappingURL=index.d.ts.map