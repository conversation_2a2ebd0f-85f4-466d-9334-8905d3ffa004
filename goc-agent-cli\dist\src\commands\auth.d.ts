/**
 * Authentication Command
 *
 * Handle user authentication with GOC Agent backend
 */
import { BackendAPIClient } from '../../services/BackendAPIClient';
export declare class AuthCommand {
    private apiClient;
    constructor(apiClient: BackendAPIClient);
    login(options: any): Promise<void>;
    logout(): Promise<void>;
    status(): Promise<void>;
    profile(): Promise<void>;
}
//# sourceMappingURL=auth.d.ts.map