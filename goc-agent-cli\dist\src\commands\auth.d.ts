/**
 * Authentication Command
 *
 * Handle user authentication with GOC Agent backend
 */
import { Command } from 'commander';
import { BackendAPIClient } from '../../services/BackendAPIClient';
export declare class AuthCommand {
    private apiClient;
    constructor(apiClient: BackendAPIClient);
    registerCommands(program: Command): void;
    login(options: any): Promise<void>;
    register(options: any): Promise<void>;
    logout(): Promise<void>;
    status(): Promise<void>;
    profile(): Promise<void>;
}
//# sourceMappingURL=auth.d.ts.map