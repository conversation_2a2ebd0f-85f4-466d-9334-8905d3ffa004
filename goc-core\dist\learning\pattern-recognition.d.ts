/**
 * Pattern Recognition Engine
 *
 * Extracts and analyzes patterns from user interactions, code, and behavior
 */
import { LearningPattern, AIMessage, AIResponse, GocConfig } from '../types';
export interface PatternExtractionContext {
    messages: AIMessage[];
    response: AIResponse;
    userFeedback?: {
        accepted: boolean;
        modifications?: string;
        rating?: number;
    };
    userId?: string;
    projectId?: string;
}
export declare class PatternRecognizer {
    private config;
    private codePatterns;
    private namingPatterns;
    private architecturePatterns;
    constructor(config: GocConfig);
    initialize(): Promise<void>;
    /**
     * Extract patterns from user interaction
     */
    extractPatterns(context: PatternExtractionContext): Promise<LearningPattern[]>;
    /**
     * Extract coding style patterns
     */
    private extractCodingStylePatterns;
    /**
     * Extract naming convention patterns
     */
    private extractNamingConventionPatterns;
    /**
     * Extract architecture patterns
     */
    private extractArchitecturePatterns;
    /**
     * Extract code snippet patterns
     */
    private extractCodeSnippetPatterns;
    /**
     * Extract workflow patterns
     */
    private extractWorkflowPatterns;
    private createPattern;
    private generatePatternId;
    private extractCodeBlocks;
    private detectIndentationStyle;
    private detectBracketStyle;
    private detectLineLengthPreference;
    private extractVariableNames;
    private extractFunctionNames;
    private analyzeNamingStyle;
    private extractReusableSnippets;
    private extractBlock;
    private categorizeMessage;
    private loadExistingPatterns;
    dispose(): Promise<void>;
}
//# sourceMappingURL=pattern-recognition.d.ts.map