/**
 * Keyboard Manager - Handles keyboard shortcuts and key bindings
 */

interface KeyBinding {
  key: string;
  action: () => void;
  description?: string;
}

export class KeyboardManager {
  private bindings: Map<string, KeyBinding> = new Map();

  public async initialize(): Promise<void> {
    console.log('Initializing Keyboard Manager...');
    
    this.setupGlobalKeyListener();
    
    console.log('Keyboard Manager initialized');
  }

  private setupGlobalKeyListener(): void {
    document.addEventListener('keydown', (event) => {
      const key = this.getKeyString(event);
      const binding = this.bindings.get(key);
      
      if (binding) {
        event.preventDefault();
        event.stopPropagation();
        binding.action();
      }
    });
  }

  private getKeyString(event: KeyboardEvent): string {
    const parts: string[] = [];
    
    if (event.ctrlKey) parts.push('Ctrl');
    if (event.altKey) parts.push('Alt');
    if (event.shiftKey) parts.push('Shift');
    if (event.metaKey) parts.push('Meta');
    
    // Handle special keys
    let key = event.key;
    if (key === ' ') key = 'Space';
    if (key === '`') key = '`';
    
    parts.push(key);
    
    return parts.join('+');
  }

  public register(keyString: string, action: () => void, description?: string): void {
    this.bindings.set(keyString, { key: keyString, action, description });
    console.log(`Registered keyboard shortcut: ${keyString}`);
  }

  public unregister(keyString: string): void {
    this.bindings.delete(keyString);
    console.log(`Unregistered keyboard shortcut: ${keyString}`);
  }

  public getBindings(): KeyBinding[] {
    return Array.from(this.bindings.values());
  }

  public dispose(): void {
    this.bindings.clear();
    console.log('Keyboard Manager disposed');
  }
}
