/**
 * Model Selection Service
 *
 * Unified service for handling model selection across all GOC Agent products
 * Manages both Ollama (local) and GOC Agent (hosted) models
 */
import { AIProvider, BaseResult } from '../types';
export interface ModelSelectionOptions {
    includeLocal?: boolean;
    includeHosted?: boolean;
    requiresAuth?: boolean;
    userTier?: 'free' | 'paid';
}
export interface ModelSelectionResult {
    providers: ProviderInfo[];
    recommendations: ModelRecommendations;
    userStatus?: UserStatus;
}
export interface ProviderInfo {
    name: AIProvider;
    displayName: string;
    tier: 'free' | 'freemium' | 'paid';
    isLocal: boolean;
    requiresAuth: boolean;
    isAvailable: boolean;
    models: ModelInfo[];
    setupInstructions?: string;
    authenticationUrl?: string;
}
export interface ModelInfo {
    id: string;
    name: string;
    tier: 'free' | 'paid';
    description: string;
    capabilities: string[];
    contextLength: number;
    isAvailable: boolean;
    requiresAuth: boolean;
    isLocal: boolean;
    size?: string;
    installCommand?: string;
    pricing?: {
        requestCost: number;
        currency: string;
    };
    limits?: {
        monthlyRequests: number;
        dailyRequests: number;
    };
}
export interface ModelRecommendations {
    local: string;
    cloudFree: string;
    cloudPaid: string;
}
export interface UserStatus {
    isAuthenticated: boolean;
    hasActiveSubscription: boolean;
    tier: 'free' | 'paid';
    usage?: {
        monthlyUsed: number;
        monthlyLimit: number;
        dailyUsed: number;
        dailyLimit: number;
    };
}
export declare class ModelSelectionService {
    private config;
    private ollamaAvailable;
    constructor();
    /**
     * Get available models and providers for selection
     */
    getModelSelection(options?: ModelSelectionOptions): Promise<ModelSelectionResult>;
    /**
     * Get Ollama provider information
     */
    private getOllamaProvider;
    /**
     * Get GOC Agent provider information
     */
    private getGocProvider;
    /**
     * Check if Ollama is available
     */
    checkOllamaAvailability(): Promise<boolean>;
    /**
     * Get installed Ollama models
     */
    getInstalledOllamaModels(): Promise<ModelInfo[]>;
    /**
     * Format model name for display
     */
    private formatModelName;
    /**
     * Get model description
     */
    private getModelDescription;
    /**
     * Get model capabilities
     */
    private getModelCapabilities;
    /**
     * Get model context length
     */
    private getModelContextLength;
    /**
     * Format file size
     */
    private formatSize;
    /**
     * Validate model selection
     */
    validateModelSelection(providerId: AIProvider, modelId: string): BaseResult;
}
//# sourceMappingURL=model-selection-service.d.ts.map