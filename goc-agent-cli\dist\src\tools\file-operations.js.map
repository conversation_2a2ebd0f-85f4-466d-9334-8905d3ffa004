{"version": 3, "file": "file-operations.js", "sourceRoot": "", "sources": ["../../../src/tools/file-operations.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,uCAAyB;AACzB,2CAA6B;AAC7B,uCAAyB;AAyBzB,MAAa,cAAc;IAIzB;QAFQ,kBAAa,GAAiB,EAAE,CAAC;QAGvC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;QAClE,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAEO,eAAe;QACrB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACnC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,QAAgB;QACnC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACjE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACzC,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ,IAAI,SAAS,SAAS,CAAC,CAAC;QAEhF,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACxC,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI,CAAC,QAAgB,EAAE,UAAuB,EAAE;QACpD,IAAI,CAAC;YACH,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,QAAQ,EAAE;iBACrC,CAAC;YACJ,CAAC;YAED,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAClD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAElC,IAAI,MAAM,GAAG,KAAK,CAAC;YAEnB,qBAAqB;YACrB,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;gBAClB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBACnD,MAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAChG,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YACnC,CAAC;YAED,qBAAqB;YACrB,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACxB,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBAClF,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;gBAC/C,MAAM,YAAY,GAAa,EAAE,CAAC;gBAClC,MAAM,cAAc,GAAa,EAAE,CAAC;gBAEpC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;oBAC7B,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;wBACrB,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAC7B,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,4BAA4B;gBAC5B,MAAM,eAAe,GAAG,IAAI,GAAG,EAAU,CAAC;gBAC1C,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBAC7B,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,YAAY,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,YAAY,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;wBAC5G,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBACzB,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACxE,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC5D,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;oBAC1B,KAAK,EAAE,MAAM,CAAC,MAAM;oBACpB,UAAU,EAAE,KAAK,CAAC,MAAM;oBACxB,IAAI,EAAE,QAAQ;iBACf;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,QAAgB,EAAE,UAA8B;QAC/D,IAAI,CAAC;YACH,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,QAAQ,EAAE;iBACrC,CAAC;YACJ,CAAC;YAED,gBAAgB;YAChB,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAE/C,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAClD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAClC,IAAI,aAAa,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;YAC/B,IAAI,eAAe,GAAG,CAAC,CAAC;YACxB,IAAI,iBAAiB,GAAG,CAAC,CAAC;YAE1B,6DAA6D;YAC7D,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC;YAErF,KAAK,MAAM,EAAE,IAAI,SAAS,EAAE,CAAC;gBAC3B,IAAI,EAAE,CAAC,SAAS,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;oBAC/B,yBAAyB;oBACzB,MAAM,QAAQ,GAAG,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC;oBAClC,MAAM,MAAM,GAAG,EAAE,CAAC,OAAO,GAAG,CAAC,CAAC;oBAE9B,IAAI,QAAQ,GAAG,CAAC,IAAI,MAAM,IAAI,aAAa,CAAC,MAAM,IAAI,QAAQ,GAAG,MAAM,EAAE,CAAC;wBACxE,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,uBAAuB,EAAE,CAAC,SAAS,IAAI,EAAE,CAAC,OAAO,EAAE;yBAC3D,CAAC;oBACJ,CAAC;oBAED,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACxE,IAAI,UAAU,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;wBAC7B,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,6BAA6B,EAAE,CAAC,SAAS,IAAI,EAAE,CAAC,OAAO,EAAE;yBACjE,CAAC;oBACJ,CAAC;oBAED,MAAM,QAAQ,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBACvC,MAAM,YAAY,GAAG,MAAM,GAAG,QAAQ,GAAG,CAAC,CAAC;oBAC3C,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;oBAEnC,aAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,YAAY,EAAE,GAAG,QAAQ,CAAC,CAAC;oBAE1D,iBAAiB,IAAI,YAAY,CAAC;oBAClC,eAAe,IAAI,UAAU,CAAC;gBAChC,CAAC;qBAAM,CAAC;oBACN,4BAA4B;oBAC5B,MAAM,WAAW,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC7C,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;oBAE7D,IAAI,WAAW,KAAK,UAAU,EAAE,CAAC;wBAC/B,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,qBAAqB,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK;yBAC5D,CAAC;oBACJ,CAAC;oBAED,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBACvC,eAAe,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBAClE,CAAC;YACH,CAAC;YAED,yBAAyB;YACzB,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC;YAE7D,gBAAgB;YAChB,MAAM,MAAM,GAAe;gBACzB,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE,eAAe;gBAC3B,YAAY,EAAE,iBAAiB;gBAC/B,MAAM,EAAE,UAAU;aACnB,CAAC;YACF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEhC,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,UAAU,EAAE,eAAe;oBAC3B,YAAY,EAAE,iBAAiB;oBAC/B,MAAM,EAAE,UAAU;oBAClB,UAAU,EAAE,UAAU,CAAC,MAAM;iBAC9B;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,QAAgB,EAAE,OAAe,EAAE,UAAmC,EAAE;QACrF,IAAI,CAAC;YACH,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;gBAClD,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,wBAAwB,QAAQ,oCAAoC;iBAC5E,CAAC;YACJ,CAAC;YAED,0BAA0B;YAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACnC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBACxB,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YACzC,CAAC;YAED,+BAA+B;YAC/B,IAAI,UAA8B,CAAC;YACnC,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5B,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC3C,CAAC;YAED,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAE5C,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;YACzC,MAAM,MAAM,GAAe;gBACzB,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;gBACnD,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE,KAAK;gBACjB,MAAM,EAAE,UAAU;aACnB,CAAC;YACF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEhC,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;oBACd,KAAK;oBACL,MAAM,EAAE,UAAU;oBAClB,OAAO,EAAE,CAAC,UAAU;iBACrB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,SAAmB;QACnC,IAAI,CAAC;YACH,MAAM,OAAO,GAAU,EAAE,CAAC;YAC1B,MAAM,OAAO,GAAa,EAAE,CAAC;YAE7B,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC7B,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC;oBACtD,SAAS;gBACX,CAAC;gBAED,gBAAgB;gBAChB,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBAC/C,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAEzB,cAAc;gBACd,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAExB,MAAM,MAAM,GAAe;oBACzB,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,QAAQ;oBACd,MAAM,EAAE,UAAU;iBACnB,CAAC;gBACF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAEhC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;YAC1E,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,OAAO;oBACP,OAAO;oBACP,YAAY,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM;iBACjE;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,UAAkB,EAAE,UAAmB;QAC7D,IAAI,CAAC;YACH,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC/B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,qBAAqB,UAAU,EAAE;iBACzC,CAAC;YACJ,CAAC;YAED,MAAM,WAAW,GAAG,UAAU,IAAI,UAAU,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;YAC/E,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YAEzC,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,QAAQ,EAAE,WAAW;oBACrB,MAAM,EAAE,UAAU;iBACnB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAxUD,wCAwUC"}