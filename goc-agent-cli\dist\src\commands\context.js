"use strict";
/**
 * Context Command
 *
 * Intelligent context engine commands using GOC Core
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContextCommand = void 0;
const chalk_1 = __importDefault(require("chalk"));
class ContextCommand {
    constructor(core) {
        this.core = core;
    }
    register(program) {
        const contextCmd = program
            .command('context')
            .description('Intelligent context engine operations');
        contextCmd
            .command('index [projectPath]')
            .description('Index a project for intelligent search')
            .option('-f, --force', 'Force re-indexing')
            .option('-l, --lightweight', 'Lightweight indexing (faster)')
            .option('-e, --exclude <patterns>', 'Exclude patterns (comma-separated)')
            .action(async (projectPath, options) => {
            await this.indexProject(projectPath || process.cwd(), options);
        });
        contextCmd
            .command('search <query>')
            .description('Search code using natural language or patterns')
            .option('-m, --max <number>', 'Maximum results', '20')
            .option('-p, --project <path>', 'Project path', process.cwd())
            .option('--semantic', 'Enable semantic search')
            .action(async (query, options) => {
            await this.searchCode(query, options);
        });
        contextCmd
            .command('analyze <filePath>')
            .description('Analyze code file')
            .option('-l, --language <lang>', 'Programming language')
            .option('--metrics', 'Include code metrics')
            .option('--suggestions', 'Include suggestions')
            .action(async (filePath, options) => {
            await this.analyzeCode(filePath, options);
        });
        contextCmd
            .command('context <filePath>')
            .description('Get intelligent context for a file')
            .option('-l, --line <number>', 'Specific line number')
            .option('-m, --max <number>', 'Maximum context lines', '50')
            .option('--related', 'Include related symbols')
            .action(async (filePath, options) => {
            await this.getFileContext(filePath, options);
        });
        contextCmd
            .command('relationships <filePath>')
            .description('Analyze code relationships and dependencies')
            .action(async (filePath) => {
            await this.analyzeRelationships(filePath);
        });
        contextCmd
            .command('stats')
            .description('Show project statistics')
            .action(async () => {
            await this.showProjectStats();
        });
        contextCmd
            .command('symbols <filePath>')
            .description('Extract and show code symbols')
            .action(async (filePath) => {
            await this.showSymbols(filePath);
        });
    }
    async indexProject(projectPath, options) {
        try {
            console.log(chalk_1.default.blue('🔍 Indexing project...'));
            console.log(chalk_1.default.dim(`Path: ${projectPath}`));
            const excludePatterns = options.exclude ? options.exclude.split(',') : [];
            const startTime = Date.now();
            const result = await this.core.getContextEngine().indexProject(projectPath, {
                force: options.force,
                lightweight: options.lightweight,
                excludePatterns
            });
            const duration = Date.now() - startTime;
            if (result.success) {
                console.log(chalk_1.default.green('✅ Project indexed successfully!'));
                console.log(chalk_1.default.dim('─'.repeat(50)));
                console.log(`Files indexed: ${result.filesIndexed}`);
                console.log(`Symbols found: ${result.symbolsFound}`);
                console.log(`Embeddings created: ${result.embeddingsCreated}`);
                console.log(`Duration: ${duration}ms`);
            }
            else {
                console.error(chalk_1.default.red('❌ Indexing failed:'), result.error);
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Indexing error:'), error);
        }
    }
    async searchCode(query, options) {
        try {
            console.log(chalk_1.default.blue(`🔍 Searching: "${query}"`));
            const results = await this.core.getContextEngine().searchContext(query, {
                maxResults: parseInt(options.max),
                projectPath: options.project,
                includeSemanticSearch: options.semantic
            });
            if (results.length === 0) {
                console.log(chalk_1.default.yellow('No results found.'));
                return;
            }
            console.log(chalk_1.default.blue(`📋 Found ${results.length} results:`));
            console.log(chalk_1.default.dim('─'.repeat(80)));
            results.forEach((result, index) => {
                const score = result.relevanceScore ? ` (${(result.relevanceScore * 100).toFixed(0)}%)` : '';
                console.log(chalk_1.default.bold(`${index + 1}. ${result.name}${score}`));
                console.log(chalk_1.default.dim(`   ${result.filePath}:${result.lineStart}`));
                console.log(`   ${result.content.substring(0, 100)}${result.content.length > 100 ? '...' : ''}`);
                console.log();
            });
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Search error:'), error);
        }
    }
    async analyzeCode(filePath, options) {
        try {
            console.log(chalk_1.default.blue(`📊 Analyzing: ${filePath}`));
            const fs = require('fs');
            const content = fs.readFileSync(filePath, 'utf8');
            const result = await this.core.getContextEngine().analyzeCode(content, {
                language: options.language,
                includeMetrics: options.metrics,
                includeSuggestions: options.suggestions,
                filePath
            });
            console.log(chalk_1.default.blue('📈 Analysis Results'));
            console.log(chalk_1.default.dim('─'.repeat(50)));
            console.log(`Summary: ${result.summary}`);
            if (result.metrics) {
                console.log(chalk_1.default.blue('\n📊 Metrics:'));
                console.log(`Lines of Code: ${result.metrics.linesOfCode}`);
                console.log(`Complexity: ${result.metrics.complexity}`);
                console.log(`Maintainability Index: ${result.metrics.maintainabilityIndex}`);
            }
            if (result.issues.length > 0) {
                console.log(chalk_1.default.blue('\n⚠️ Issues:'));
                result.issues.forEach(issue => {
                    const severityColor = issue.type === 'error' ? chalk_1.default.red :
                        issue.type === 'warning' ? chalk_1.default.yellow : chalk_1.default.blue;
                    console.log(`  ${severityColor(issue.type.toUpperCase())} ${issue.message}`);
                    if (issue.line) {
                        console.log(chalk_1.default.dim(`    Line ${issue.line}${issue.column ? `:${issue.column}` : ''}`));
                    }
                });
            }
            if (result.suggestions.length > 0) {
                console.log(chalk_1.default.blue('\n💡 Suggestions:'));
                result.suggestions.forEach(suggestion => {
                    console.log(`  • ${suggestion.message}`);
                    if (suggestion.example) {
                        console.log(chalk_1.default.dim(`    Example: ${suggestion.example}`));
                    }
                });
            }
            if (result.dependencies.length > 0) {
                console.log(chalk_1.default.blue('\n📦 Dependencies:'));
                result.dependencies.forEach(dep => {
                    console.log(`  • ${dep}`);
                });
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Analysis error:'), error);
        }
    }
    async getFileContext(filePath, options) {
        try {
            console.log(chalk_1.default.blue(`📄 Getting context: ${filePath}`));
            const contextOptions = {
                maxLines: parseInt(options.max),
                includeRelated: options.related
            };
            if (options.line) {
                contextOptions.line = parseInt(options.line);
            }
            const results = await this.core.getContextEngine().getContext(filePath, contextOptions);
            if (results.length === 0) {
                console.log(chalk_1.default.yellow('No context found.'));
                return;
            }
            console.log(chalk_1.default.blue('📋 Context Items:'));
            console.log(chalk_1.default.dim('─'.repeat(80)));
            results.forEach((item, index) => {
                console.log(chalk_1.default.bold(`${index + 1}. ${item.name} (${item.type})`));
                console.log(chalk_1.default.dim(`   Lines ${item.lineStart}-${item.lineEnd}`));
                console.log(`   ${item.content.split('\n')[0]}${item.content.split('\n').length > 1 ? '...' : ''}`);
                console.log();
            });
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Context error:'), error);
        }
    }
    async analyzeRelationships(filePath) {
        try {
            console.log(chalk_1.default.blue(`🔗 Analyzing relationships: ${filePath}`));
            const result = await this.core.getContextEngine().analyzeRelationships(filePath);
            console.log(chalk_1.default.blue('🔗 Relationship Analysis'));
            console.log(chalk_1.default.dim('─'.repeat(50)));
            if (result.imports.length > 0) {
                console.log(chalk_1.default.blue('\n📥 Imports:'));
                result.imports.forEach(imp => console.log(`  • ${imp}`));
            }
            if (result.exports.length > 0) {
                console.log(chalk_1.default.blue('\n📤 Exports:'));
                result.exports.forEach(exp => console.log(`  • ${exp}`));
            }
            if (result.dependencies.length > 0) {
                console.log(chalk_1.default.blue('\n📦 Dependencies:'));
                result.dependencies.forEach(dep => console.log(`  • ${dep}`));
            }
            if (result.dependents.length > 0) {
                console.log(chalk_1.default.blue('\n🔄 Dependents:'));
                result.dependents.forEach(dep => console.log(`  • ${dep}`));
            }
            console.log(chalk_1.default.blue('\n📊 Metrics:'));
            console.log(`Complexity: ${result.complexity}`);
            if (result.patterns.length > 0) {
                console.log(chalk_1.default.blue('\n🎨 Patterns:'));
                result.patterns.forEach(pattern => console.log(`  • ${pattern}`));
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Relationship analysis error:'), error);
        }
    }
    async showProjectStats() {
        try {
            const stats = this.core.getContextEngine().getProjectStats();
            if (!stats) {
                console.log(chalk_1.default.yellow('No project indexed. Use "goc context index" first.'));
                return;
            }
            console.log(chalk_1.default.blue('📊 Project Statistics'));
            console.log(chalk_1.default.dim('─'.repeat(50)));
            console.log(`Total Files: ${stats.totalFiles}`);
            console.log(`Total Symbols: ${stats.totalSymbols}`);
            console.log(`Total Embeddings: ${stats.totalEmbeddings}`);
            console.log(`Last Indexed: ${stats.lastIndexed.toLocaleString()}`);
            if (Object.keys(stats.languageBreakdown).length > 0) {
                console.log(chalk_1.default.blue('\n🌐 Language Breakdown:'));
                Object.entries(stats.languageBreakdown).forEach(([lang, count]) => {
                    console.log(`  ${lang}: ${count}`);
                });
            }
            if (Object.keys(stats.complexityMetrics).length > 0) {
                console.log(chalk_1.default.blue('\n📈 Complexity Metrics:'));
                Object.entries(stats.complexityMetrics).forEach(([metric, value]) => {
                    console.log(`  ${metric}: ${typeof value === 'number' ? value.toFixed(2) : value}`);
                });
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Stats error:'), error);
        }
    }
    async showSymbols(filePath) {
        try {
            console.log(chalk_1.default.blue(`🔍 Extracting symbols: ${filePath}`));
            const fs = require('fs');
            const content = fs.readFileSync(filePath, 'utf8');
            // Use the code parser from context engine
            const contextEngine = this.core.getContextEngine();
            const symbols = await contextEngine.codeParser.parseFile(filePath, content);
            if (symbols.length === 0) {
                console.log(chalk_1.default.yellow('No symbols found.'));
                return;
            }
            console.log(chalk_1.default.blue(`📋 Found ${symbols.length} symbols:`));
            console.log(chalk_1.default.dim('─'.repeat(80)));
            symbols.forEach((symbol, index) => {
                const typeColor = symbol.type === 'function' ? chalk_1.default.green :
                    symbol.type === 'class' ? chalk_1.default.blue :
                        symbol.type === 'variable' ? chalk_1.default.yellow : chalk_1.default.white;
                console.log(`${index + 1}. ${typeColor(symbol.type)} ${chalk_1.default.bold(symbol.name)}`);
                console.log(chalk_1.default.dim(`   Line ${symbol.line}:${symbol.column}`));
                if (symbol.signature) {
                    console.log(`   ${symbol.signature}`);
                }
                console.log();
            });
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Symbol extraction error:'), error);
        }
    }
}
exports.ContextCommand = ContextCommand;
//# sourceMappingURL=context.js.map