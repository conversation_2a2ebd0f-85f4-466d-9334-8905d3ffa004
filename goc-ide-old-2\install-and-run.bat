@echo off
echo ========================================
echo   GOC IDE - Installation and Setup
echo ========================================
echo.

echo [1/4] Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo [2/4] Building main process...
call npm run build:main
if %errorlevel% neq 0 (
    echo ERROR: Failed to build main process
    pause
    exit /b 1
)

echo.
echo [3/4] Building renderer process...
call npm run build:renderer
if %errorlevel% neq 0 (
    echo ERROR: Failed to build renderer process
    pause
    exit /b 1
)

echo.
echo [4/4] Starting GOC IDE...
call npm start

echo.
echo GOC IDE setup complete!
pause
