/**
 * Authentication Command
 * 
 * Handle user authentication with GOC Agent backend
 */

import chalk from 'chalk';
import inquirer from 'inquirer';
import { Command } from 'commander';
import { BackendAPIClient } from '../../services/BackendAPIClient';

export class AuthCommand {
  constructor(private apiClient: BackendAPIClient) {}

  registerCommands(program: Command): void {
    const authCmd = program
      .command('auth')
      .description('Authentication commands');

    authCmd
      .command('login')
      .description('Login to GOC Agent backend')
      .option('-e, --email <email>', 'Email address')
      .option('-p, --password <password>', 'Password')
      .action(async (options) => {
        await this.login(options);
      });

    authCmd
      .command('register')
      .description('Register a new account')
      .option('-n, --name <name>', 'Full name')
      .option('-e, --email <email>', 'Email address')
      .option('-p, --password <password>', 'Password')
      .action(async (options) => {
        await this.register(options);
      });

    authCmd
      .command('logout')
      .description('Logout from GOC Agent backend')
      .action(async () => {
        await this.logout();
      });

    authCmd
      .command('status')
      .description('Check authentication status')
      .action(async () => {
        await this.status();
      });

    authCmd
      .command('profile')
      .description('View user profile')
      .action(async () => {
        await this.profile();
      });
  }

  async login(options: any): Promise<void> {
    console.log(chalk.blue('🔐 Login to GOC Agent'));

    try {
      let email = options.email;
      let password = options.password;

      // Prompt for missing credentials
      if (!email || !password) {
        const answers = await inquirer.prompt([
          {
            type: 'input',
            name: 'email',
            message: 'Email:',
            when: !email,
            validate: (input) => {
              const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
              return emailRegex.test(input) || 'Please enter a valid email address';
            }
          },
          {
            type: 'password',
            name: 'password',
            message: 'Password:',
            when: !password,
            validate: (input) => input.length >= 6 || 'Password must be at least 6 characters'
          }
        ]);

        email = email || answers.email;
        password = password || answers.password;
      }

      console.log(chalk.dim('Authenticating...'));

      const response = await this.apiClient.login(email, password);

      if (response.success) {
        console.log(chalk.green('✅ Login successful!'));
        if (response.user) {
          console.log(chalk.dim(`Welcome back, ${response.user.name}!`));
        }
      } else {
        console.log(chalk.red('❌ Login failed:'), response.message);
      }
    } catch (error) {
      console.error(chalk.red('❌ Login error:'), error);
    }
  }

  async register(options: any): Promise<void> {
    console.log(chalk.blue('📝 Register for GOC Agent'));

    try {
      let name = options.name;
      let email = options.email;
      let password = options.password;

      // Prompt for missing information
      if (!name || !email || !password) {
        const answers = await inquirer.prompt([
          {
            type: 'input',
            name: 'name',
            message: 'Full Name:',
            when: !name,
            validate: (input) => input.trim().length >= 2 || 'Name must be at least 2 characters'
          },
          {
            type: 'input',
            name: 'email',
            message: 'Email:',
            when: !email,
            validate: (input) => {
              const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
              return emailRegex.test(input) || 'Please enter a valid email address';
            }
          },
          {
            type: 'password',
            name: 'password',
            message: 'Password:',
            when: !password,
            validate: (input) => input.length >= 6 || 'Password must be at least 6 characters'
          },
          {
            type: 'password',
            name: 'confirmPassword',
            message: 'Confirm Password:',
            when: !password,
            validate: (input, answers) => {
              const pwd = password || answers?.password;
              return input === pwd || 'Passwords do not match';
            }
          }
        ]);

        name = name || answers.name;
        email = email || answers.email;
        password = password || answers.password;
      }

      console.log(chalk.dim('Creating account...'));

      const response = await this.apiClient.register(name, email, password);

      if (response.success) {
        console.log(chalk.green('✅ Registration successful!'));
        console.log(chalk.dim(`Welcome to GOC Agent, ${response.user?.name || name}!`));
        console.log(chalk.dim('You are now logged in and ready to use GOC Agent.'));
      } else {
        console.log(chalk.red('❌ Registration failed:'), response.message);
      }
    } catch (error) {
      console.error(chalk.red('❌ Registration error:'), error);
    }
  }

  async logout(): Promise<void> {
    try {
      if (!this.apiClient.isAuthenticated()) {
        console.log(chalk.yellow('You are not logged in.'));
        return;
      }

      console.log(chalk.dim('Logging out...'));
      await this.apiClient.logout();
      console.log(chalk.green('✅ Logged out successfully!'));
    } catch (error) {
      console.error(chalk.red('❌ Logout error:'), error);
    }
  }

  async status(): Promise<void> {
    try {
      if (this.apiClient.isAuthenticated()) {
        console.log(chalk.green('✅ Authenticated'));
        
        try {
          const user = await this.apiClient.getCurrentUser();
          console.log(chalk.dim(`Logged in as: ${user.name} (${user.email})`));
          
          if (user.subscription) {
            console.log(chalk.dim(`Plan: ${user.subscription.plan} (${user.subscription.status})`));
            console.log(chalk.dim(`API Requests Remaining: ${user.subscription.api_requests_remaining}`));
          }
        } catch (error) {
          console.log(chalk.yellow('⚠️ Could not fetch user details'));
        }
      } else {
        console.log(chalk.red('❌ Not authenticated'));
        console.log(chalk.dim('Use "goc auth login" to authenticate'));
      }
    } catch (error) {
      console.error(chalk.red('❌ Status check error:'), error);
    }
  }

  async profile(): Promise<void> {
    try {
      if (!this.apiClient.isAuthenticated()) {
        console.log(chalk.red('❌ Not authenticated'));
        console.log(chalk.dim('Use "goc auth login" to authenticate'));
        return;
      }

      console.log(chalk.dim('Fetching profile...'));
      const user = await this.apiClient.getCurrentUser();

      console.log(chalk.blue('👤 User Profile'));
      console.log(chalk.dim('─'.repeat(40)));
      console.log(`Name: ${user.name}`);
      console.log(`Email: ${user.email}`);
      console.log(`ID: ${user.id}`);

      if (user.subscription) {
        console.log(chalk.blue('\n💳 Subscription'));
        console.log(chalk.dim('─'.repeat(40)));
        console.log(`Plan: ${user.subscription.plan}`);
        console.log(`Status: ${user.subscription.status}`);
        console.log(`API Requests Remaining: ${user.subscription.api_requests_remaining}`);
      }

    } catch (error) {
      console.error(chalk.red('❌ Profile error:'), error);
    }
  }
}
