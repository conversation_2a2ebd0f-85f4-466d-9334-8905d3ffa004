"use strict";
/**
 * Deployment Assistance System
 *
 * Intelligent deployment automation and infrastructure management
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeploymentAssistant = void 0;
const utils_1 = require("../utils");
/**
 * Intelligent Deployment Assistant
 */
class DeploymentAssistant {
    constructor() {
        this.templates = new Map();
        this.activeDeployments = new Map();
        this.initializeTemplates();
    }
    /**
     * Analyze project and suggest deployment options
     */
    async analyzeProject(projectPath) {
        try {
            utils_1.logger.info('Analyzing project for deployment', { projectPath });
            const analysis = await this.scanProject(projectPath);
            const suggestedTargets = this.suggestDeploymentTargets(analysis);
            return {
                projectType: analysis.type,
                framework: analysis.framework,
                dependencies: analysis.dependencies,
                suggestedTargets,
                requirements: this.getDeploymentRequirements(analysis)
            };
        }
        catch (error) {
            utils_1.logger.error('Project analysis failed', error);
            throw error;
        }
    }
    /**
     * Create deployment plan
     */
    async createDeploymentPlan(projectPath, target) {
        try {
            utils_1.logger.info('Creating deployment plan', { projectPath, target: target.name });
            const planId = `deploy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const analysis = await this.scanProject(projectPath);
            const steps = await this.generateDeploymentSteps(analysis, target);
            const requirements = this.getTargetRequirements(target);
            const warnings = this.getDeploymentWarnings(analysis, target);
            const plan = {
                id: planId,
                projectPath,
                target,
                steps,
                estimatedDuration: steps.reduce((sum, step) => sum + step.estimatedDuration, 0),
                requirements,
                warnings,
                createdAt: new Date()
            };
            this.activeDeployments.set(planId, plan);
            utils_1.logger.info('Deployment plan created', {
                planId,
                stepsCount: steps.length,
                estimatedDuration: plan.estimatedDuration
            });
            return plan;
        }
        catch (error) {
            utils_1.logger.error('Deployment plan creation failed', error);
            throw error;
        }
    }
    /**
     * Execute deployment plan
     */
    async executeDeployment(planId) {
        try {
            utils_1.logger.info('Starting deployment execution', { planId });
            const plan = this.activeDeployments.get(planId);
            if (!plan) {
                throw new Error(`Deployment plan not found: ${planId}`);
            }
            const startTime = Date.now();
            const result = {
                success: false,
                planId,
                completedSteps: [],
                logs: [],
                duration: 0
            };
            // Execute steps in order
            for (const step of plan.steps) {
                try {
                    result.logs.push({
                        step: step.id,
                        level: 'info',
                        message: `Starting step: ${step.name}`,
                        timestamp: new Date()
                    });
                    await this.executeStep(step, plan);
                    result.completedSteps.push(step.id);
                    result.logs.push({
                        step: step.id,
                        level: 'info',
                        message: `Completed step: ${step.name}`,
                        timestamp: new Date()
                    });
                }
                catch (error) {
                    result.failedStep = step.id;
                    result.error = error instanceof Error ? error.message : String(error);
                    result.logs.push({
                        step: step.id,
                        level: 'error',
                        message: `Step failed: ${result.error}`,
                        timestamp: new Date()
                    });
                    break;
                }
            }
            result.duration = Date.now() - startTime;
            result.success = result.completedSteps.length === plan.steps.length;
            if (result.success) {
                result.deploymentUrl = this.getDeploymentUrl(plan);
            }
            utils_1.logger.info('Deployment execution completed', {
                planId,
                success: result.success,
                duration: result.duration
            });
            return result;
        }
        catch (error) {
            utils_1.logger.error('Deployment execution failed', error);
            throw error;
        }
    }
    /**
     * Generate infrastructure templates
     */
    async generateInfrastructure(projectType, target, options = {}) {
        try {
            const templates = [];
            // Generate Dockerfile
            if (target.type === 'docker' || target.type === 'kubernetes') {
                templates.push(this.generateDockerfile(projectType));
            }
            // Generate docker-compose.yml
            if (target.type === 'docker') {
                templates.push(this.generateDockerCompose(projectType, options));
            }
            // Generate Kubernetes manifests
            if (target.type === 'kubernetes') {
                templates.push(...this.generateKubernetesManifests(projectType, options));
            }
            // Generate Terraform configuration
            if (target.provider === 'aws' || target.provider === 'gcp' || target.provider === 'azure') {
                templates.push(this.generateTerraformConfig(target, options));
            }
            return templates;
        }
        catch (error) {
            utils_1.logger.error('Infrastructure generation failed', error);
            throw error;
        }
    }
    /**
     * Get deployment status
     */
    getDeploymentStatus(planId) {
        const plan = this.activeDeployments.get(planId);
        return {
            plan: plan || null,
            status: plan ? 'pending' : 'completed' // Simplified status
        };
    }
    /**
     * List available deployment templates
     */
    getAvailableTemplates() {
        return Array.from(this.templates.values());
    }
    // Private helper methods
    async scanProject(projectPath) {
        const fs = await Promise.resolve().then(() => __importStar(require('fs')));
        const path = await Promise.resolve().then(() => __importStar(require('path')));
        const analysis = {
            type: 'general',
            framework: 'unknown',
            dependencies: [],
            buildTool: 'none',
            hasDatabase: false,
            hasTests: false,
            staticAssets: false
        };
        try {
            // Check for package.json (Node.js)
            if (fs.existsSync(path.join(projectPath, 'package.json'))) {
                const packageJson = JSON.parse(fs.readFileSync(path.join(projectPath, 'package.json'), 'utf-8'));
                analysis.type = 'nodejs';
                analysis.dependencies = Object.keys(packageJson.dependencies || {});
                analysis.buildTool = 'npm';
                // Detect framework
                if (analysis.dependencies.includes('react'))
                    analysis.framework = 'react';
                else if (analysis.dependencies.includes('vue'))
                    analysis.framework = 'vue';
                else if (analysis.dependencies.includes('angular'))
                    analysis.framework = 'angular';
                else if (analysis.dependencies.includes('express'))
                    analysis.framework = 'express';
                else if (analysis.dependencies.includes('next'))
                    analysis.framework = 'nextjs';
            }
            // Check for composer.json (PHP)
            if (fs.existsSync(path.join(projectPath, 'composer.json'))) {
                analysis.type = 'php';
                analysis.buildTool = 'composer';
                // Check for Laravel
                if (fs.existsSync(path.join(projectPath, 'artisan'))) {
                    analysis.framework = 'laravel';
                }
            }
            // Check for requirements.txt (Python)
            if (fs.existsSync(path.join(projectPath, 'requirements.txt'))) {
                analysis.type = 'python';
                analysis.buildTool = 'pip';
            }
            // Check for Dockerfile
            if (fs.existsSync(path.join(projectPath, 'Dockerfile'))) {
                analysis.hasDocker = true;
            }
            // Check for database files
            const dbFiles = ['database.sqlite', 'db.sqlite3', '.env'];
            analysis.hasDatabase = dbFiles.some(file => fs.existsSync(path.join(projectPath, file)));
            // Check for test directories
            const testDirs = ['test', 'tests', '__tests__', 'spec'];
            analysis.hasTests = testDirs.some(dir => fs.existsSync(path.join(projectPath, dir)));
            // Check for static assets
            const staticDirs = ['public', 'static', 'assets', 'dist', 'build'];
            analysis.staticAssets = staticDirs.some(dir => fs.existsSync(path.join(projectPath, dir)));
        }
        catch (error) {
            utils_1.logger.warn('Project scanning partial failure', error);
        }
        return analysis;
    }
    suggestDeploymentTargets(analysis) {
        const targets = [];
        // Static site hosting for frontend projects
        if (analysis.framework === 'react' || analysis.framework === 'vue' || analysis.staticAssets) {
            targets.push({
                name: 'Vercel',
                type: 'static',
                provider: 'vercel',
                configuration: { buildCommand: 'npm run build', outputDirectory: 'dist' },
                environment: 'production'
            });
            targets.push({
                name: 'Netlify',
                type: 'static',
                provider: 'netlify',
                configuration: { buildCommand: 'npm run build', publishDirectory: 'dist' },
                environment: 'production'
            });
        }
        // Container deployment for backend projects
        if (analysis.type === 'nodejs' || analysis.type === 'php' || analysis.type === 'python') {
            targets.push({
                name: 'Docker',
                type: 'docker',
                provider: 'local',
                configuration: { port: 3000 },
                environment: 'development'
            });
            targets.push({
                name: 'AWS ECS',
                type: 'docker',
                provider: 'aws',
                configuration: { region: 'us-east-1', cluster: 'default' },
                environment: 'production'
            });
        }
        // PaaS options
        if (analysis.type === 'nodejs') {
            targets.push({
                name: 'Heroku',
                type: 'paas',
                provider: 'heroku',
                configuration: { buildpacks: ['heroku/nodejs'] },
                environment: 'production'
            });
        }
        return targets;
    }
    getDeploymentRequirements(analysis) {
        const requirements = [];
        if (analysis.type === 'nodejs') {
            requirements.push('Node.js runtime');
            requirements.push('npm or yarn package manager');
        }
        if (analysis.type === 'php') {
            requirements.push('PHP runtime');
            requirements.push('Composer package manager');
        }
        if (analysis.type === 'python') {
            requirements.push('Python runtime');
            requirements.push('pip package manager');
        }
        if (analysis.hasDatabase) {
            requirements.push('Database server');
        }
        if (analysis.hasDocker) {
            requirements.push('Docker runtime');
        }
        return requirements;
    }
    getTargetRequirements(target) {
        const requirements = [];
        switch (target.provider) {
            case 'aws':
                requirements.push('AWS CLI configured');
                requirements.push('AWS credentials');
                break;
            case 'gcp':
                requirements.push('Google Cloud SDK');
                requirements.push('GCP credentials');
                break;
            case 'azure':
                requirements.push('Azure CLI');
                requirements.push('Azure credentials');
                break;
            case 'heroku':
                requirements.push('Heroku CLI');
                requirements.push('Heroku account');
                break;
        }
        if (target.type === 'docker') {
            requirements.push('Docker installed');
        }
        if (target.type === 'kubernetes') {
            requirements.push('kubectl configured');
            requirements.push('Kubernetes cluster access');
        }
        return requirements;
    }
    getDeploymentWarnings(analysis, target) {
        const warnings = [];
        if (!analysis.hasTests && target.environment === 'production') {
            warnings.push('No tests detected - consider adding tests before production deployment');
        }
        if (analysis.hasDatabase && target.type === 'static') {
            warnings.push('Database detected but deploying to static hosting - database will not be available');
        }
        if (target.environment === 'production' && target.provider === 'local') {
            warnings.push('Local deployment not recommended for production environment');
        }
        return warnings;
    }
    async generateDeploymentSteps(analysis, target) {
        const steps = [];
        let stepCounter = 0;
        // Build step
        if (analysis.buildTool !== 'none') {
            steps.push({
                id: `step_${++stepCounter}`,
                name: 'Build Project',
                description: 'Build the project for deployment',
                type: 'build',
                command: this.getBuildCommand(analysis),
                dependencies: [],
                estimatedDuration: 60,
                required: true
            });
        }
        // Test step
        if (analysis.hasTests) {
            steps.push({
                id: `step_${++stepCounter}`,
                name: 'Run Tests',
                description: 'Execute test suite',
                type: 'test',
                command: this.getTestCommand(analysis),
                dependencies: steps.length > 0 ? [steps[steps.length - 1].id] : [],
                estimatedDuration: 30,
                required: false
            });
        }
        // Package step
        if (target.type === 'docker') {
            steps.push({
                id: `step_${++stepCounter}`,
                name: 'Build Docker Image',
                description: 'Build Docker container image',
                type: 'package',
                command: 'docker build -t app .',
                dependencies: steps.length > 0 ? [steps[steps.length - 1].id] : [],
                estimatedDuration: 120,
                required: true
            });
        }
        // Deploy step
        steps.push({
            id: `step_${++stepCounter}`,
            name: 'Deploy Application',
            description: 'Deploy to target environment',
            type: 'deploy',
            command: this.getDeployCommand(target),
            dependencies: steps.length > 0 ? [steps[steps.length - 1].id] : [],
            estimatedDuration: 90,
            required: true
        });
        // Verify step
        steps.push({
            id: `step_${++stepCounter}`,
            name: 'Verify Deployment',
            description: 'Verify deployment is successful',
            type: 'verify',
            command: 'curl -f $DEPLOYMENT_URL/health || echo "Health check failed"',
            dependencies: [steps[steps.length - 1].id],
            estimatedDuration: 15,
            required: false
        });
        return steps;
    }
    async executeStep(step, plan) {
        // Simulate step execution - in a real implementation, this would execute actual commands
        utils_1.logger.info('Executing deployment step', { stepId: step.id, name: step.name });
        // Simulate execution time
        await new Promise(resolve => setTimeout(resolve, 1000));
        // Simulate potential failure for testing
        if (step.name.includes('Test') && Math.random() < 0.1) {
            throw new Error('Test execution failed');
        }
    }
    getDeploymentUrl(plan) {
        const { target } = plan;
        switch (target.provider) {
            case 'vercel':
                return 'https://app-vercel.app';
            case 'netlify':
                return 'https://app-netlify.app';
            case 'heroku':
                return 'https://app-heroku.herokuapp.com';
            default:
                return 'http://localhost:3000';
        }
    }
    getBuildCommand(analysis) {
        switch (analysis.buildTool) {
            case 'npm':
                return 'npm run build';
            case 'composer':
                return 'composer install --no-dev';
            case 'pip':
                return 'pip install -r requirements.txt';
            default:
                return 'echo "No build command configured"';
        }
    }
    getTestCommand(analysis) {
        switch (analysis.type) {
            case 'nodejs':
                return 'npm test';
            case 'php':
                return 'vendor/bin/phpunit';
            case 'python':
                return 'pytest';
            default:
                return 'echo "No test command configured"';
        }
    }
    getDeployCommand(target) {
        switch (target.provider) {
            case 'heroku':
                return 'git push heroku main';
            case 'vercel':
                return 'vercel --prod';
            case 'netlify':
                return 'netlify deploy --prod';
            case 'aws':
                return 'aws ecs update-service --cluster default --service app';
            default:
                return 'echo "Deployment command not configured"';
        }
    }
    generateDockerfile(projectType) {
        const dockerfiles = {
            nodejs: `FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]`,
            php: `FROM php:8.2-apache
COPY . /var/www/html/
RUN docker-php-ext-install pdo pdo_mysql
EXPOSE 80`,
            python: `FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["python", "app.py"]`
        };
        return {
            name: 'Dockerfile',
            description: `Dockerfile for ${projectType} application`,
            type: 'docker',
            template: dockerfiles[projectType] || dockerfiles.nodejs,
            variables: [
                { name: 'port', description: 'Application port', type: 'number', required: false, default: 3000 }
            ],
            dependencies: ['Docker']
        };
    }
    generateDockerCompose(projectType, options) {
        let compose = `version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production`;
        if (options.includeDatabase) {
            compose += `
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: app
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - db_data:/var/lib/postgresql/data

volumes:
  db_data:`;
        }
        return {
            name: 'docker-compose.yml',
            description: 'Docker Compose configuration',
            type: 'compose',
            template: compose,
            variables: [
                { name: 'app_port', description: 'Application port', type: 'number', required: false, default: 3000 }
            ],
            dependencies: ['Docker', 'Docker Compose']
        };
    }
    generateKubernetesManifests(projectType, options) {
        const deployment = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: app
  template:
    metadata:
      labels:
        app: app
    spec:
      containers:
      - name: app
        image: app:latest
        ports:
        - containerPort: 3000`;
        const service = `apiVersion: v1
kind: Service
metadata:
  name: app-service
spec:
  selector:
    app: app
  ports:
  - port: 80
    targetPort: 3000
  type: LoadBalancer`;
        return [
            {
                name: 'deployment.yaml',
                description: 'Kubernetes Deployment manifest',
                type: 'kubernetes',
                template: deployment,
                variables: [],
                dependencies: ['Kubernetes']
            },
            {
                name: 'service.yaml',
                description: 'Kubernetes Service manifest',
                type: 'kubernetes',
                template: service,
                variables: [],
                dependencies: ['Kubernetes']
            }
        ];
    }
    generateTerraformConfig(target, options) {
        const terraform = `terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

resource "aws_ecs_cluster" "main" {
  name = "app-cluster"
}

resource "aws_ecs_service" "app" {
  name            = "app-service"
  cluster         = aws_ecs_cluster.main.id
  task_definition = aws_ecs_task_definition.app.arn
  desired_count   = 2
}`;
        return {
            name: 'main.tf',
            description: 'Terraform infrastructure configuration',
            type: 'terraform',
            template: terraform,
            variables: [
                { name: 'aws_region', description: 'AWS region', type: 'string', required: true, default: 'us-east-1' }
            ],
            dependencies: ['Terraform', 'AWS CLI']
        };
    }
    initializeTemplates() {
        // Initialize with basic templates
        utils_1.logger.info('Initializing deployment templates');
    }
}
exports.DeploymentAssistant = DeploymentAssistant;
//# sourceMappingURL=index.js.map