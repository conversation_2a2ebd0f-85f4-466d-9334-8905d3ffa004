{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../src/commands/auth.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;AAEH,kDAA0B;AAC1B,wDAAgC;AAIhC,MAAa,WAAW;IACtB,YAAoB,SAA2B;QAA3B,cAAS,GAAT,SAAS,CAAkB;IAAG,CAAC;IAEnD,QAAQ,CAAC,OAAgB;QACvB,MAAM,OAAO,GAAG,OAAO;aACpB,OAAO,CAAC,MAAM,CAAC;aACf,WAAW,CAAC,yBAAyB,CAAC,CAAC;QAE1C,OAAO;aACJ,OAAO,CAAC,OAAO,CAAC;aAChB,WAAW,CAAC,4BAA4B,CAAC;aACzC,MAAM,CAAC,qBAAqB,EAAE,eAAe,CAAC;aAC9C,MAAM,CAAC,2BAA2B,EAAE,UAAU,CAAC;aAC/C,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACxB,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEL,OAAO;aACJ,OAAO,CAAC,UAAU,CAAC;aACnB,WAAW,CAAC,wBAAwB,CAAC;aACrC,MAAM,CAAC,mBAAmB,EAAE,WAAW,CAAC;aACxC,MAAM,CAAC,qBAAqB,EAAE,eAAe,CAAC;aAC9C,MAAM,CAAC,2BAA2B,EAAE,UAAU,CAAC;aAC/C,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACxB,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEL,OAAO;aACJ,OAAO,CAAC,QAAQ,CAAC;aACjB,WAAW,CAAC,+BAA+B,CAAC;aAC5C,MAAM,CAAC,KAAK,IAAI,EAAE;YACjB,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;QAEL,OAAO;aACJ,OAAO,CAAC,QAAQ,CAAC;aACjB,WAAW,CAAC,6BAA6B,CAAC;aAC1C,MAAM,CAAC,KAAK,IAAI,EAAE;YACjB,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;QAEL,OAAO;aACJ,OAAO,CAAC,SAAS,CAAC;aAClB,WAAW,CAAC,mBAAmB,CAAC;aAChC,MAAM,CAAC,KAAK,IAAI,EAAE;YACjB,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,OAAY;QACtB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC;QAEjD,IAAI,CAAC;YACH,IAAI,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;YAC1B,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YAEhC,iCAAiC;YACjC,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACxB,MAAM,OAAO,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;oBACpC;wBACE,IAAI,EAAE,OAAO;wBACb,IAAI,EAAE,OAAO;wBACb,OAAO,EAAE,QAAQ;wBACjB,IAAI,EAAE,CAAC,KAAK;wBACZ,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE;4BAClB,MAAM,UAAU,GAAG,4BAA4B,CAAC;4BAChD,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,oCAAoC,CAAC;wBACxE,CAAC;qBACF;oBACD;wBACE,IAAI,EAAE,UAAU;wBAChB,IAAI,EAAE,UAAU;wBAChB,OAAO,EAAE,WAAW;wBACpB,IAAI,EAAE,CAAC,QAAQ;wBACf,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,wCAAwC;qBACnF;iBACF,CAAC,CAAC;gBAEH,KAAK,GAAG,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC;gBAC/B,QAAQ,GAAG,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC;YAC1C,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC,CAAC;YAE5C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YAE7D,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC;gBAChD,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;oBAClB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,iBAAiB,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;gBACjE,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAY;QACzB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAC;QAErD,IAAI,CAAC;YACH,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;YACxB,IAAI,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;YAC1B,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YAEhC,iCAAiC;YACjC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACjC,MAAM,OAAO,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;oBACpC;wBACE,IAAI,EAAE,OAAO;wBACb,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,YAAY;wBACrB,IAAI,EAAE,CAAC,IAAI;wBACX,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,IAAI,CAAC,IAAI,oCAAoC;qBACtF;oBACD;wBACE,IAAI,EAAE,OAAO;wBACb,IAAI,EAAE,OAAO;wBACb,OAAO,EAAE,QAAQ;wBACjB,IAAI,EAAE,CAAC,KAAK;wBACZ,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE;4BAClB,MAAM,UAAU,GAAG,4BAA4B,CAAC;4BAChD,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,oCAAoC,CAAC;wBACxE,CAAC;qBACF;oBACD;wBACE,IAAI,EAAE,UAAU;wBAChB,IAAI,EAAE,UAAU;wBAChB,OAAO,EAAE,WAAW;wBACpB,IAAI,EAAE,CAAC,QAAQ;wBACf,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,wCAAwC;qBACnF;oBACD;wBACE,IAAI,EAAE,UAAU;wBAChB,IAAI,EAAE,iBAAiB;wBACvB,OAAO,EAAE,mBAAmB;wBAC5B,IAAI,EAAE,CAAC,QAAQ;wBACf,QAAQ,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;4BAC3B,MAAM,GAAG,GAAG,QAAQ,IAAI,OAAO,EAAE,QAAQ,CAAC;4BAC1C,OAAO,KAAK,KAAK,GAAG,IAAI,wBAAwB,CAAC;wBACnD,CAAC;qBACF;iBACF,CAAC,CAAC;gBAEH,IAAI,GAAG,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;gBAC5B,KAAK,GAAG,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC;gBAC/B,QAAQ,GAAG,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC;YAC1C,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,CAAC;YAE9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YAEtE,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC,CAAC;gBACvD,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,yBAAyB,QAAQ,CAAC,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC;gBAChF,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC,CAAC;YAC9E,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,wBAAwB,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM;QACV,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,EAAE,CAAC;gBACtC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC,CAAC;gBACpD,OAAO;YACT,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC;YACzC,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM;QACV,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,EAAE,CAAC;gBACrC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;gBAE5C,IAAI,CAAC;oBACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;oBACnD,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;oBAErE,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;wBACtB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;wBACxF,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,2BAA2B,IAAI,CAAC,YAAY,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC;oBAChG,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,iCAAiC,CAAC,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,CAAC;gBAC9C,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,EAAE,CAAC;gBACtC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,CAAC;gBAC9C,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC,CAAC;gBAC/D,OAAO;YACT,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,CAAC;YAC9C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;YAEnD,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAC3C,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACvC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YAE9B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;gBAC7C,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACvC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC/C,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;gBACnD,OAAO,CAAC,GAAG,CAAC,2BAA2B,IAAI,CAAC,YAAY,CAAC,sBAAsB,EAAE,CAAC,CAAC;YACrF,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;CACF;AA5OD,kCA4OC"}