{"version": 3, "file": "process-manager.js", "sourceRoot": "", "sources": ["../../../src/tools/process-manager.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,iDAAoD;AACpD,mCAAsC;AAsBtC,MAAa,cAAe,SAAQ,qBAAY;IAAhD;;QACU,cAAS,GAA6B,IAAI,GAAG,EAAE,CAAC;QAChD,mBAAc,GAA8B,IAAI,GAAG,EAAE,CAAC;QACtD,mBAAc,GAAG,CAAC,CAAC;IA8V7B,CAAC;IA5VC;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,OAAe,EAAE,OAAiB,EAAE,EAAE,UAAyB,EAAE;QACnF,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,QAAQ,EAAE,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;YAChE,MAAM,EACJ,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,EACnB,OAAO,GAAG,KAAK,EACf,GAAG,GAAG,OAAO,CAAC,GAAG,EACjB,KAAK,EACL,IAAI,GAAG,KAAK,EACZ,cAAc,GAAG,GAAG,EACpB,YAAY,GAAG,KAAK,EACrB,GAAG,OAAO,CAAC;YAEZ,MAAM,WAAW,GAAgB;gBAC/B,EAAE,EAAE,SAAS;gBACb,OAAO;gBACP,IAAI;gBACJ,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,EAAE;gBACV,WAAW,EAAE,EAAE;aAChB,CAAC;YAEF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;YAE3C,MAAM,YAAY,GAAG,IAAA,qBAAK,EAAC,OAAO,EAAE,IAAI,EAAE;gBACxC,GAAG;gBACH,GAAG,EAAE,GAAwB;gBAC7B,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;aAChC,CAAC,CAAC;YAEH,WAAW,CAAC,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC;YACnC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YAEjD,gBAAgB;YAChB,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBACvC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC9B,WAAW,CAAC,MAAM,IAAI,KAAK,CAAC;gBAE5B,IAAI,YAAY,EAAE,CAAC;oBACjB,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC9B,CAAC;gBAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;YAEH,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBACvC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC9B,WAAW,CAAC,WAAW,IAAI,KAAK,CAAC;gBAEjC,IAAI,YAAY,EAAE,CAAC;oBACjB,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC9B,CAAC;gBAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;YAEH,4BAA4B;YAC5B,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;gBAChC,WAAW,CAAC,MAAM,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC;gBACzD,WAAW,CAAC,QAAQ,GAAG,IAAI,IAAI,CAAC,CAAC;gBACjC,WAAW,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;gBACjC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBACtC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;YAEH,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBACjC,WAAW,CAAC,MAAM,GAAG,QAAQ,CAAC;gBAC9B,WAAW,CAAC,WAAW,IAAI,KAAK,CAAC,OAAO,CAAC;gBACzC,WAAW,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;gBACjC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBACtC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;YAEH,yBAAyB;YACzB,IAAI,KAAK,IAAI,YAAY,CAAC,KAAK,EAAE,CAAC;gBAChC,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAChC,YAAY,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;YAC3B,CAAC;YAED,IAAI,IAAI,EAAE,CAAC;gBACT,+BAA+B;gBAC/B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,cAAc,GAAG,IAAI,CAAC,CAAC;gBAC3E,OAAO;oBACL,OAAO,EAAE,MAAM,CAAC,QAAQ,KAAK,CAAC;oBAC9B,IAAI,EAAE;wBACJ,SAAS;wBACT,GAAG,MAAM;qBACV;iBACF,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,qBAAqB;gBACrB,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE;wBACJ,SAAS;wBACT,GAAG,EAAE,WAAW,CAAC,GAAG;wBACpB,MAAM,EAAE,SAAS;qBAClB;iBACF,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,SAAiB,EAAE,UAAuD,EAAE;QAC5F,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAClD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,sBAAsB,SAAS,EAAE;iBACzC,CAAC;YACJ,CAAC;YAED,IAAI,OAAO,CAAC,IAAI,IAAI,WAAW,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACrD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,cAAc,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;gBAC3F,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE,MAAM;iBACb,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,SAAS;oBACT,MAAM,EAAE,WAAW,CAAC,MAAM;oBAC1B,MAAM,EAAE,WAAW,CAAC,MAAM;oBAC1B,WAAW,EAAE,WAAW,CAAC,WAAW;oBACpC,QAAQ,EAAE,WAAW,CAAC,QAAQ;oBAC9B,GAAG,EAAE,WAAW,CAAC,GAAG;iBACrB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,SAAiB,EAAE,KAAa;QACjD,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACxD,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,qCAAqC,SAAS,EAAE;iBACxD,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;gBACxB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,gCAAgC,SAAS,EAAE;iBACnD,CAAC;YACJ,CAAC;YAED,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAEhC,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,SAAS;oBACT,YAAY,EAAE,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC;iBAC/C;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,SAAiB,EAAE,SAAiB,SAAS;QAC7D,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAClD,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAExD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,sBAAsB,SAAS,EAAE;iBACzC,CAAC;YACJ,CAAC;YAED,IAAI,WAAW,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACrC,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE;wBACJ,SAAS;wBACT,MAAM,EAAE,WAAW,CAAC,MAAM;wBAC1B,OAAO,EAAE,4BAA4B;qBACtC;iBACF,CAAC;YACJ,CAAC;YAED,IAAI,YAAY,EAAE,CAAC;gBACjB,YAAY,CAAC,IAAI,CAAC,MAAwB,CAAC,CAAC;gBAC5C,WAAW,CAAC,MAAM,GAAG,QAAQ,CAAC;gBAC9B,WAAW,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;gBACjC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACxC,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,SAAS;oBACT,MAAM;oBACN,MAAM,EAAE,QAAQ;iBACjB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa;QACX,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjE,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC,CAAC;QAEJ,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,SAAS;gBACT,OAAO,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM;gBAC7D,KAAK,EAAE,SAAS,CAAC,MAAM;aACxB;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,SAAiB;QAC9B,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAClD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,sBAAsB,SAAS,EAAE;aACzC,CAAC;QACJ,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,WAAW;SAClB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,SAAiB,EAAE,SAAiB;QACzD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAClD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,MAAM,CAAC,IAAI,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC,CAAC;gBACrD,OAAO;YACT,CAAC;YAED,IAAI,WAAW,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACrC,OAAO,CAAC;oBACN,MAAM,EAAE,WAAW,CAAC,MAAM;oBAC1B,MAAM,EAAE,WAAW,CAAC,WAAW;oBAC/B,QAAQ,EAAE,WAAW,CAAC,QAAQ,IAAI,CAAC;oBACnC,GAAG,EAAE,WAAW,CAAC,GAAG;iBACrB,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC9B,MAAM,CAAC,IAAI,KAAK,CAAC,yBAAyB,SAAS,IAAI,CAAC,CAAC,CAAC;YAC5D,CAAC,EAAE,SAAS,CAAC,CAAC;YAEd,MAAM,UAAU,GAAG,CAAC,kBAA0B,EAAE,QAAgB,EAAE,EAAE;gBAClE,IAAI,kBAAkB,KAAK,SAAS,EAAE,CAAC;oBACrC,YAAY,CAAC,OAAO,CAAC,CAAC;oBACtB,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;oBACnD,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;oBAE7C,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;oBAC5C,OAAO,CAAC;wBACN,MAAM,EAAE,IAAI,CAAC,MAAM;wBACnB,MAAM,EAAE,IAAI,CAAC,WAAW;wBACxB,QAAQ;wBACR,GAAG,EAAE,IAAI,CAAC,GAAG;qBACd,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC;YAEF,MAAM,OAAO,GAAG,CAAC,cAAsB,EAAE,KAAY,EAAE,EAAE;gBACvD,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;oBACjC,YAAY,CAAC,OAAO,CAAC,CAAC;oBACtB,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;oBACnD,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;oBAC7C,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChB,CAAC;YACH,CAAC,CAAC;YAEF,IAAI,CAAC,EAAE,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;YACvC,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,OAAO;QACL,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,eAAe;QAE1E,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC;YAClD,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM,EAAE,CAAC;gBACvE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAjWD,wCAiWC"}