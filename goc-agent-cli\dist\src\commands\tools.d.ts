/**
 * Tools Command
 *
 * Expose the enhanced tool system through CLI commands
 */
import { Command } from 'commander';
export declare class ToolsCommand {
    private registry;
    constructor();
    register(program: Command): void;
    private viewFile;
    private editFile;
    private saveFile;
    private removeFiles;
    private runProcess;
    private listProcesses;
    private killProcess;
    private checkFiles;
    private manageTerminal;
    private managePackages;
    private analyzeWorkspace;
    private listTools;
}
//# sourceMappingURL=tools.d.ts.map