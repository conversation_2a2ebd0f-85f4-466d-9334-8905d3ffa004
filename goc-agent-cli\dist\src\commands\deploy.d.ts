/**
 * Deployment Command
 *
 * Intelligent deployment automation and infrastructure management
 */
import { Command } from 'commander';
import { Goc<PERSON>ore } from '@goc-agent/core';
export declare class DeployCommand {
    private core;
    constructor(core: GocCore);
    register(program: Command): void;
    private analyzeProject;
    private createDeploymentPlan;
    private executeDeployment;
    private checkDeploymentStatus;
    private generateInfrastructure;
    private listTemplates;
    private interactiveDeployment;
    private createDeploymentAssistant;
    private detectProjectType;
    private getSuggestedTargets;
    private getRequirements;
    private getTargetRequirements;
    private generateSteps;
    private getDeployCommand;
    private generateTemplates;
    private getDockerfileTemplate;
    private getDefaultTemplates;
    private simulateDeploymentExecution;
    private getProviderColor;
    private getStepIcon;
    private getStatusColor;
}
//# sourceMappingURL=deploy.d.ts.map