{"version": 3, "file": "terminal.js", "sourceRoot": "", "sources": ["../../../src/tools/terminal.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,iDAAoD;AAsBpD,MAAa,eAAe;IAA5B;QACU,cAAS,GAAiC,IAAI,GAAG,EAAE,CAAC;QACpD,oBAAe,GAAG,CAAC,CAAC;IAmW9B,CAAC;IAjWC;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,UAA2B,EAAE;QAChD,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,QAAQ,EAAE,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;YAClE,MAAM,EACJ,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,EAC9B,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,EACnB,GAAG,GAAG,OAAO,CAAC,GAAG,EACjB,IAAI,GAAG,EAAE,EACT,IAAI,GAAG,EAAE,EACV,GAAG,OAAO,CAAC;YAEZ,MAAM,eAAe,GAAG,IAAA,qBAAK,EAAC,KAAK,EAAE,EAAE,EAAE;gBACvC,GAAG;gBACH,GAAG,EAAE,GAAwB;gBAC7B,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;aAChC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAoB;gBAC/B,EAAE,EAAE,UAAU;gBACd,KAAK;gBACL,GAAG;gBACH,OAAO,EAAE,eAAe;gBACxB,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,YAAY,EAAE,IAAI,IAAI,EAAE;aACzB,CAAC;YAEF,gBAAgB;YAChB,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC1C,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClC,OAAO,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;YACpC,CAAC,CAAC,CAAC;YAEH,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC1C,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClC,OAAO,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;YACpC,CAAC,CAAC,CAAC;YAEH,eAAe,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;gBAC/B,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;YAC3B,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAExC,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,UAAU;oBACV,KAAK;oBACL,GAAG;oBACH,GAAG,EAAE,eAAe,CAAC,GAAG;iBACzB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,UAAmB,EAAE,UAAuD,EAAE;QAC/F,IAAI,CAAC;YACH,IAAI,UAAU,EAAE,CAAC;gBACf,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBAC/C,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,uBAAuB,UAAU,EAAE;qBAC3C,CAAC;gBACJ,CAAC;gBAED,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBAC9B,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;oBAClB,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC;gBACtB,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE;wBACJ,UAAU;wBACV,MAAM;wBACN,QAAQ,EAAE,OAAO,CAAC,QAAQ;wBAC1B,YAAY,EAAE,OAAO,CAAC,YAAY;qBACnC;iBACF,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,wCAAwC;gBACxC,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;qBACvD,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;qBACvB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC;gBAEvE,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAChC,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,2BAA2B;qBACnC,CAAC;gBACJ,CAAC;gBAED,MAAM,OAAO,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;gBAClC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBAC9B,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;oBAClB,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC;gBACtB,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE;wBACJ,UAAU,EAAE,OAAO,CAAC,EAAE;wBACtB,MAAM;wBACN,QAAQ,EAAE,OAAO,CAAC,QAAQ;wBAC1B,YAAY,EAAE,OAAO,CAAC,YAAY;qBACnC;iBACF,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,UAAkB,EAAE,OAAe;QACrD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC/C,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,uBAAuB,UAAU,EAAE;iBAC3C,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACtB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,2BAA2B,UAAU,EAAE;iBAC/C,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;gBAC3B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,iCAAiC,UAAU,EAAE;iBACrD,CAAC;YACJ,CAAC;YAED,6BAA6B;YAC7B,MAAM,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC;YACxE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAC3C,OAAO,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;YAElC,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,UAAU;oBACV,OAAO,EAAE,aAAa,CAAC,IAAI,EAAE;oBAC7B,SAAS,EAAE,OAAO,CAAC,YAAY;iBAChC;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,UAAkB,EAAE,OAAe,EAAE,YAAoB,KAAK;QACjF,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC/C,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,uBAAuB,UAAU,EAAE;iBAC3C,CAAC;YACJ,CAAC;YAED,MAAM,mBAAmB,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC;YAElD,eAAe;YACf,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAClE,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;gBACzB,OAAO,WAAW,CAAC;YACrB,CAAC;YAED,kBAAkB;YAClB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7B,MAAM,WAAW,GAAG,GAAG,EAAE;oBACvB,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBAC/B,IAAI,WAAW,GAAG,SAAS,GAAG,SAAS,EAAE,CAAC;wBACxC,OAAO,CAAC;4BACN,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,yBAAyB,SAAS,IAAI;yBAC9C,CAAC,CAAC;wBACH,OAAO;oBACT,CAAC;oBAED,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;oBAEhE,4EAA4E;oBAC5E,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;wBAClF,OAAO,CAAC;4BACN,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE;gCACJ,UAAU;gCACV,OAAO;gCACP,MAAM,EAAE,SAAS;gCACjB,aAAa,EAAE,WAAW,GAAG,SAAS;6BACvC;yBACF,CAAC,CAAC;wBACH,OAAO;oBACT,CAAC;oBAED,uBAAuB;oBACvB,UAAU,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;gBAC/B,CAAC,CAAC;gBAEF,WAAW,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,UAAkB;QACpC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC/C,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,uBAAuB,UAAU,EAAE;iBAC3C,CAAC;YACJ,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACrB,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAChC,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;YAC3B,CAAC;YAED,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAElC,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,UAAU;oBACV,MAAM,EAAE,IAAI;iBACb;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa;QACX,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACpE,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,YAAY,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM;SACpC,CAAC,CAAC,CAAC;QAEJ,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,SAAS;gBACT,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM;gBAChD,KAAK,EAAE,SAAS,CAAC,MAAM;aACxB;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,UAAkB;QAChC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,uBAAuB,UAAU,EAAE;aAC3C,CAAC;QACJ,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,YAAY,EAAE,OAAO,CAAC,YAAY;gBAClC,YAAY,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM;gBACnC,GAAG,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG;aACzB;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAElC,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YACzB,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,SAAS,CAAC;QAC1C,CAAC;aAAM,CAAC;YACN,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,WAAW,CAAC;QAC1C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,OAAO;QACL,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,aAAa;QAEnE,KAAK,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC;YACrD,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,YAAY,GAAG,MAAM,EAAE,CAAC;gBACvD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;IACH,CAAC;CACF;AArWD,0CAqWC"}