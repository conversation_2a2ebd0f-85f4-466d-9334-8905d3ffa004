/**
 * IPC Manager - Handles communication between main and renderer processes
 */

import { ipcMain, dialog, BrowserWindow } from 'electron';
import { GocCore } from '@goc-agent/core';
import { ConfigManager } from './configManager';
import { FileSystemManager } from './fileSystemManager';
import { TerminalManager } from './terminalManager';

export class IPCManager {
  private terminalManager: TerminalManager;

  constructor(
    private configManager: ConfigManager,
    private fileSystemManager: FileSystemManager
  ) {
    this.terminalManager = new TerminalManager();
  }

  public setupHandlers(gocCore: GocCore | null): void {
    console.log('Setting up IPC handlers...');

    // Window controls
    this.setupWindowHandlers();

    // File system handlers
    this.setupFileSystemHandlers();

    // GOC Agent handlers
    this.setupGocHandlers(gocCore);

    // Terminal handlers
    this.setupTerminalHandlers();

    // Dialog handlers
    this.setupDialogHandlers();

    console.log('IPC handlers setup complete');
  }

  private setupWindowHandlers(): void {
    ipcMain.handle('window-minimize', () => {
      const window = BrowserWindow.getFocusedWindow();
      if (window) window.minimize();
    });

    ipcMain.handle('window-maximize', () => {
      const window = BrowserWindow.getFocusedWindow();
      if (window) {
        if (window.isMaximized()) {
          window.unmaximize();
        } else {
          window.maximize();
        }
      }
    });

    ipcMain.handle('window-close', () => {
      const window = BrowserWindow.getFocusedWindow();
      if (window) window.close();
    });

    ipcMain.handle('window-is-maximized', () => {
      const window = BrowserWindow.getFocusedWindow();
      return window ? window.isMaximized() : false;
    });
  }

  private setupFileSystemHandlers(): void {
    ipcMain.handle('fs-read-file', async (event, filePath: string) => {
      return await this.fileSystemManager.readFile(filePath);
    });

    ipcMain.handle('fs-write-file', async (event, filePath: string, content: string) => {
      return await this.fileSystemManager.writeFile(filePath, content);
    });

    ipcMain.handle('fs-read-directory', async (event, dirPath: string) => {
      return await this.fileSystemManager.readDirectory(dirPath);
    });

    ipcMain.handle('fs-create-file', async (event, filePath: string) => {
      return await this.fileSystemManager.createFile(filePath);
    });

    ipcMain.handle('fs-create-directory', async (event, dirPath: string) => {
      return await this.fileSystemManager.createDirectory(dirPath);
    });

    ipcMain.handle('fs-delete-file', async (event, filePath: string) => {
      return await this.fileSystemManager.deleteFile(filePath);
    });

    ipcMain.handle('fs-delete-directory', async (event, dirPath: string) => {
      return await this.fileSystemManager.deleteDirectory(dirPath);
    });

    ipcMain.handle('fs-watch-directory', async (event, dirPath: string) => {
      return await this.fileSystemManager.watchDirectory(dirPath);
    });

    ipcMain.handle('fs-unwatch-directory', async (event, dirPath: string) => {
      return await this.fileSystemManager.unwatchDirectory(dirPath);
    });
  }

  private setupGocHandlers(gocCore: GocCore | null): void {
    ipcMain.handle('goc-chat', async (event, message: string, context?: any) => {
      if (!gocCore) {
        return { error: 'GOC Core not initialized' };
      }

      try {
        const response = await gocCore.chat([
          { role: 'user', content: message, timestamp: new Date() }
        ], { context });

        return {
          content: response.content,
          model: response.model,
          provider: response.provider,
          usage: response.usage
        };
      } catch (error) {
        console.error('GOC chat error:', error);
        return { error: error.message };
      }
    });

    ipcMain.handle('goc-explain-code', async (event, code: string, language: string) => {
      if (!gocCore) {
        return 'GOC Core not initialized';
      }

      try {
        const prompt = `Explain this ${language} code:\n\n\`\`\`${language}\n${code}\n\`\`\``;
        const response = await gocCore.chat([
          { role: 'user', content: prompt, timestamp: new Date() }
        ]);

        return response.content;
      } catch (error) {
        console.error('GOC explain code error:', error);
        return `Error explaining code: ${error.message}`;
      }
    });

    ipcMain.handle('goc-generate-code', async (event, prompt: string, language: string) => {
      if (!gocCore) {
        return 'GOC Core not initialized';
      }

      try {
        const fullPrompt = `Generate ${language} code for: ${prompt}`;
        const response = await gocCore.chat([
          { role: 'user', content: fullPrompt, timestamp: new Date() }
        ]);

        return response.content;
      } catch (error) {
        console.error('GOC generate code error:', error);
        return `Error generating code: ${error.message}`;
      }
    });

    ipcMain.handle('goc-refactor-code', async (event, code: string, instructions: string) => {
      if (!gocCore) {
        return 'GOC Core not initialized';
      }

      try {
        const prompt = `Refactor this code according to these instructions: ${instructions}\n\nCode:\n\`\`\`\n${code}\n\`\`\``;
        const response = await gocCore.chat([
          { role: 'user', content: prompt, timestamp: new Date() }
        ]);

        return response.content;
      } catch (error) {
        console.error('GOC refactor code error:', error);
        return `Error refactoring code: ${error.message}`;
      }
    });

    ipcMain.handle('goc-fix-code', async (event, code: string, error: string) => {
      if (!gocCore) {
        return 'GOC Core not initialized';
      }

      try {
        const prompt = `Fix this code that has the following error: ${error}\n\nCode:\n\`\`\`\n${code}\n\`\`\``;
        const response = await gocCore.chat([
          { role: 'user', content: prompt, timestamp: new Date() }
        ]);

        return response.content;
      } catch (error) {
        console.error('GOC fix code error:', error);
        return `Error fixing code: ${error.message}`;
      }
    });

    ipcMain.handle('goc-get-status', async () => {
      if (!gocCore) {
        return { status: 'not-initialized' };
      }

      try {
        return await gocCore.getStatus();
      } catch (error) {
        console.error('GOC get status error:', error);
        return { status: 'error', error: error.message };
      }
    });

    ipcMain.handle('goc-set-provider', async (event, provider: string, model: string) => {
      if (!gocCore) {
        return { error: 'GOC Core not initialized' };
      }

      try {
        // Update configuration
        await this.configManager.updateConfig({
          defaultAiProvider: provider,
          defaultAiModel: model
        });

        // Reinitialize GOC Core with new settings
        // This would typically require restarting the core
        return { success: true };
      } catch (error) {
        console.error('GOC set provider error:', error);
        return { error: error.message };
      }
    });
  }

  private setupTerminalHandlers(): void {
    ipcMain.handle('terminal-create', async (event, cwd?: string) => {
      return await this.terminalManager.createTerminal(cwd);
    });

    ipcMain.handle('terminal-write', async (event, terminalId: string, data: string) => {
      return await this.terminalManager.writeToTerminal(terminalId, data);
    });

    ipcMain.handle('terminal-resize', async (event, terminalId: string, cols: number, rows: number) => {
      return await this.terminalManager.resizeTerminal(terminalId, cols, rows);
    });

    ipcMain.handle('terminal-kill', async (event, terminalId: string) => {
      return await this.terminalManager.killTerminal(terminalId);
    });
  }

  private setupDialogHandlers(): void {
    ipcMain.handle('dialog-show-open', async (event, options: any) => {
      const window = BrowserWindow.getFocusedWindow();
      if (window) {
        return await dialog.showOpenDialog(window, options);
      }
      return { canceled: true };
    });

    ipcMain.handle('dialog-show-save', async (event, options: any) => {
      const window = BrowserWindow.getFocusedWindow();
      if (window) {
        return await dialog.showSaveDialog(window, options);
      }
      return { canceled: true };
    });

    ipcMain.handle('dialog-show-message', async (event, options: any) => {
      const window = BrowserWindow.getFocusedWindow();
      if (window) {
        return await dialog.showMessageBox(window, options);
      }
      return { response: 0 };
    });
  }

  public dispose(): void {
    // Remove all IPC handlers
    ipcMain.removeAllListeners('window-minimize');
    ipcMain.removeAllListeners('window-maximize');
    ipcMain.removeAllListeners('window-close');
    ipcMain.removeAllListeners('window-is-maximized');
    
    // File system handlers
    ipcMain.removeAllListeners('fs-read-file');
    ipcMain.removeAllListeners('fs-write-file');
    ipcMain.removeAllListeners('fs-read-directory');
    ipcMain.removeAllListeners('fs-create-file');
    ipcMain.removeAllListeners('fs-create-directory');
    ipcMain.removeAllListeners('fs-delete-file');
    ipcMain.removeAllListeners('fs-delete-directory');
    ipcMain.removeAllListeners('fs-watch-directory');
    ipcMain.removeAllListeners('fs-unwatch-directory');
    
    // GOC handlers
    ipcMain.removeAllListeners('goc-chat');
    ipcMain.removeAllListeners('goc-explain-code');
    ipcMain.removeAllListeners('goc-generate-code');
    ipcMain.removeAllListeners('goc-refactor-code');
    ipcMain.removeAllListeners('goc-fix-code');
    ipcMain.removeAllListeners('goc-get-status');
    ipcMain.removeAllListeners('goc-set-provider');
    
    // Terminal handlers
    ipcMain.removeAllListeners('terminal-create');
    ipcMain.removeAllListeners('terminal-write');
    ipcMain.removeAllListeners('terminal-resize');
    ipcMain.removeAllListeners('terminal-kill');
    
    // Dialog handlers
    ipcMain.removeAllListeners('dialog-show-open');
    ipcMain.removeAllListeners('dialog-show-save');
    ipcMain.removeAllListeners('dialog-show-message');

    // Dispose terminal manager
    this.terminalManager.dispose();

    console.log('IPC Manager disposed');
  }
}
