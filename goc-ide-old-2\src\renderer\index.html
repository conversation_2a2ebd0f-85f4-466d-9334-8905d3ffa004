<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;">
    <title>GOC IDE</title>
    <link rel="stylesheet" href="./styles/main.css">
</head>
<body>
    <!-- Title Bar -->
    <div id="titlebar" class="titlebar">
        <div class="titlebar-drag-region">
            <div class="titlebar-icon">
                <img src="../../assets/icon.png" alt="GOC IDE" width="16" height="16">
            </div>
            <div class="titlebar-title">GOC IDE</div>
        </div>
        <div class="titlebar-controls">
            <button id="minimize-btn" class="titlebar-button" title="Minimize">
                <svg width="12" height="12" viewBox="0 0 12 12">
                    <rect x="2" y="6" width="8" height="1" fill="currentColor"/>
                </svg>
            </button>
            <button id="maximize-btn" class="titlebar-button" title="Maximize">
                <svg width="12" height="12" viewBox="0 0 12 12">
                    <rect x="2" y="2" width="8" height="8" stroke="currentColor" stroke-width="1" fill="none"/>
                </svg>
            </button>
            <button id="close-btn" class="titlebar-button close" title="Close">
                <svg width="12" height="12" viewBox="0 0 12 12">
                    <path d="M2 2 L10 10 M10 2 L2 10" stroke="currentColor" stroke-width="1"/>
                </svg>
            </button>
        </div>
    </div>

    <!-- Main Container -->
    <div id="main-container" class="main-container">
        <!-- Activity Bar -->
        <div id="activity-bar" class="activity-bar">
            <div class="activity-bar-items">
                <button class="activity-item active" data-panel="explorer" title="Explorer">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z"/>
                    </svg>
                </button>
                <button class="activity-item" data-panel="search" title="Search">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                </button>
                <button class="activity-item" data-panel="git" title="Source Control">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M21.007 8.222A3.738 3.738 0 0 0 15.045 5.2a3.737 3.737 0 0 0 1.156 6.583 2.988 2.988 0 0 1-2.668 1.67h-2.99a4.456 4.456 0 0 0-2.989 1.165V7.4a3.737 3.737 0 1 0-1.494 0v9.117a3.776 3.776 0 1 0 1.816.099 2.99 2.99 0 0 1 2.668-1.667h2.99a4.484 4.484 0 0 0 4.223-3.039 3.736 3.736 0 0 0 3.25-3.687z"/>
                    </svg>
                </button>
                <button class="activity-item" data-panel="goc-agent" title="GOC Agent">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                </button>
                <button class="activity-item" data-panel="extensions" title="Extensions">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20.5 11H19V7c0-1.1-.9-2-2-2h-4V3.5C13 2.12 11.88 1 10.5 1S8 2.12 8 3.5V5H4c-1.1 0-1.99.9-1.99 2v3.8H3.5c1.49 0 2.7 1.21 2.7 2.7s-1.21 2.7-2.7 2.7H2V20c0 1.1.9 2 2 2h3.8v-1.5c0-1.49 1.21-2.7 2.7-2.7 1.49 0 2.7 1.21 2.7 2.7V22H17c1.1 0 2-.9 2-2v-4h1.5c1.38 0 2.5-1.12 2.5-2.5S21.88 11 20.5 11z"/>
                    </svg>
                </button>
            </div>
            <div class="activity-bar-bottom">
                <button class="activity-item" data-panel="settings" title="Settings">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Sidebar -->
        <div id="sidebar" class="sidebar">
            <div id="sidebar-content" class="sidebar-content">
                <!-- Dynamic content based on active panel -->
            </div>
        </div>

        <!-- Editor Area -->
        <div id="editor-area" class="editor-area">
            <!-- Tab Bar -->
            <div id="tab-bar" class="tab-bar">
                <!-- Dynamic tabs -->
            </div>
            
            <!-- Editor Container -->
            <div id="editor-container" class="editor-container">
                <div id="welcome-screen" class="welcome-screen">
                    <div class="welcome-content">
                        <h1>Welcome to GOC IDE</h1>
                        <p>Professional AI-powered code editor</p>
                        <div class="welcome-actions">
                            <button id="open-folder-btn" class="welcome-button">Open Folder</button>
                            <button id="new-file-btn" class="welcome-button">New File</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Panel Area -->
        <div id="panel-area" class="panel-area">
            <div id="panel-tabs" class="panel-tabs">
                <button class="panel-tab active" data-panel="terminal">Terminal</button>
                <button class="panel-tab" data-panel="output">Output</button>
                <button class="panel-tab" data-panel="problems">Problems</button>
                <button class="panel-tab" data-panel="debug">Debug Console</button>
            </div>
            <div id="panel-content" class="panel-content">
                <!-- Dynamic panel content -->
            </div>
        </div>
    </div>

    <!-- Status Bar -->
    <div id="status-bar" class="status-bar">
        <div class="status-left">
            <span id="git-branch" class="status-item">main</span>
            <span id="file-encoding" class="status-item">UTF-8</span>
            <span id="line-ending" class="status-item">LF</span>
        </div>
        <div class="status-right">
            <span id="cursor-position" class="status-item">Ln 1, Col 1</span>
            <span id="language-mode" class="status-item">Plain Text</span>
            <span id="goc-status" class="status-item">GOC Agent Ready</span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="./renderer.js"></script>
</body>
</html>
