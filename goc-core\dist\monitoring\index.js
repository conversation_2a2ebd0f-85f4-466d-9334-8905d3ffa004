"use strict";
/**
 * Monitoring and Analytics System
 *
 * Performance monitoring, usage analytics, and system health tracking
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonitoringSystem = void 0;
const utils_1 = require("../utils");
/**
 * Comprehensive Monitoring System
 */
class MonitoringSystem {
    constructor(options = {}) {
        this.metrics = [];
        this.performanceData = [];
        this.usageData = [];
        this.healthChecks = new Map();
        this.startTime = new Date();
        this.options = {
            enablePerformanceTracking: true,
            enableUsageAnalytics: true,
            enableErrorTracking: true,
            retentionDays: 30,
            alertThresholds: {
                responseTime: 5000, // 5 seconds
                errorRate: 0.05, // 5%
                memoryUsage: 0.8 // 80%
            },
            ...options
        };
        this.initializeHealthChecks();
        this.startPerformanceMonitoring();
    }
    /**
     * Record a metric event
     */
    recordMetric(event) {
        if (!this.shouldRecordMetric(event.type))
            return;
        const metricEvent = {
            id: `metric_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date(),
            ...event
        };
        this.metrics.push(metricEvent);
        this.cleanupOldMetrics();
        utils_1.logger.debug('Metric recorded', {
            type: event.type,
            name: event.name,
            value: event.value
        });
    }
    /**
     * Record performance metrics
     */
    recordPerformance(metrics) {
        if (!this.options.enablePerformanceTracking)
            return;
        this.performanceData.push({
            ...metrics,
            timestamp: new Date()
        });
        // Check for performance alerts
        this.checkPerformanceAlerts(metrics);
        // Keep only recent data
        const cutoff = new Date(Date.now() - (this.options.retentionDays * 24 * 60 * 60 * 1000));
        this.performanceData = this.performanceData.filter((data) => data.timestamp > cutoff);
    }
    /**
     * Record usage analytics
     */
    recordUsage(event) {
        if (!this.options.enableUsageAnalytics)
            return;
        this.recordMetric({
            type: 'usage',
            name: `${event.feature}.${event.action}`,
            value: event.duration || 1,
            unit: event.duration ? 'ms' : 'count',
            tags: {
                feature: event.feature,
                action: event.action,
                userId: event.userId || 'anonymous'
            },
            metadata: event.metadata
        });
    }
    /**
     * Record error event
     */
    recordError(error, context) {
        if (!this.options.enableErrorTracking)
            return;
        this.recordMetric({
            type: 'error',
            name: error.name,
            value: 1,
            unit: 'count',
            tags: {
                errorType: error.name,
                feature: context?.feature || 'unknown',
                action: context?.action || 'unknown',
                userId: context?.userId || 'anonymous'
            },
            metadata: {
                message: error.message,
                stack: error.stack,
                ...context?.metadata
            }
        });
        utils_1.logger.error('Error recorded in monitoring', error);
    }
    /**
     * Get current system health
     */
    async getSystemHealth() {
        const components = {};
        const alerts = [];
        // Check all registered health checks
        for (const [name, healthCheck] of this.healthChecks) {
            try {
                const startTime = Date.now();
                const isHealthy = await healthCheck();
                const responseTime = Date.now() - startTime;
                components[name] = {
                    status: isHealthy ? 'up' : 'down',
                    responseTime,
                    lastCheck: new Date()
                };
                if (!isHealthy) {
                    alerts.push({
                        level: 'error',
                        message: `Component ${name} is down`,
                        timestamp: new Date(),
                        component: name
                    });
                }
                else if (responseTime > 1000) {
                    alerts.push({
                        level: 'warning',
                        message: `Component ${name} is responding slowly (${responseTime}ms)`,
                        timestamp: new Date(),
                        component: name
                    });
                }
            }
            catch (error) {
                components[name] = {
                    status: 'down',
                    lastCheck: new Date(),
                    error: error instanceof Error ? error.message : String(error)
                };
                alerts.push({
                    level: 'critical',
                    message: `Component ${name} health check failed: ${error}`,
                    timestamp: new Date(),
                    component: name
                });
            }
        }
        // Determine overall system status
        const componentStatuses = Object.values(components).map((c) => c.status);
        let status = 'healthy';
        if (componentStatuses.includes('down')) {
            status = 'unhealthy';
        }
        else if (componentStatuses.includes('degraded')) {
            status = 'degraded';
        }
        return {
            status,
            uptime: Date.now() - this.startTime.getTime(),
            version: '1.0.0', // Would be loaded from package.json
            components,
            alerts
        };
    }
    /**
     * Get performance analytics
     */
    getPerformanceAnalytics(timeRange) {
        let data = this.performanceData;
        if (timeRange) {
            data = data.filter((d) => d.timestamp >= timeRange.start && d.timestamp <= timeRange.end);
        }
        if (data.length === 0) {
            return {
                averageResponseTime: 0,
                throughput: 0,
                errorRate: 0,
                trends: []
            };
        }
        const averageResponseTime = data.reduce((sum, d) => sum + d.responseTime, 0) / data.length;
        const throughput = data.reduce((sum, d) => sum + d.throughput, 0) / data.length;
        const errorRate = data.reduce((sum, d) => sum + d.errorRate, 0) / data.length;
        const trends = data.map((d) => ({
            timestamp: d.timestamp,
            responseTime: d.responseTime,
            throughput: d.throughput,
            errorRate: d.errorRate
        }));
        return {
            averageResponseTime,
            throughput,
            errorRate,
            trends
        };
    }
    /**
     * Get usage analytics
     */
    getUsageAnalytics(timeRange) {
        let metrics = this.metrics.filter(m => m.type === 'usage');
        if (timeRange) {
            metrics = metrics.filter(m => m.timestamp >= timeRange.start && m.timestamp <= timeRange.end);
        }
        const totalRequests = metrics.length;
        const uniqueUsers = new Set(metrics.map(m => m.tags.userId)).size;
        // Calculate features used
        const featuresUsed = {};
        metrics.forEach(m => {
            const feature = m.tags.feature;
            featuresUsed[feature] = (featuresUsed[feature] || 0) + 1;
        });
        // Calculate top queries (simplified)
        const queries = metrics
            .filter(m => m.name.includes('search') || m.name.includes('query'))
            .map(m => m.metadata?.query || 'unknown')
            .filter(q => q !== 'unknown');
        const queryCount = {};
        queries.forEach(q => {
            queryCount[q] = (queryCount[q] || 0) + 1;
        });
        const topQueries = Object.entries(queryCount)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 10)
            .map(([query, count]) => ({ query, count }));
        // Calculate top errors
        const errorMetrics = this.metrics.filter(m => m.type === 'error');
        const errorCount = {};
        errorMetrics.forEach(m => {
            const error = m.metadata?.message || m.name;
            errorCount[error] = (errorCount[error] || 0) + 1;
        });
        const topErrors = Object.entries(errorCount)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 10)
            .map(([error, count]) => ({ error, count }));
        return {
            totalRequests,
            uniqueUsers,
            featuresUsed,
            averageSessionDuration: 0, // Would calculate from session data
            topQueries,
            topErrors
        };
    }
    /**
     * Register a health check
     */
    registerHealthCheck(name, check) {
        this.healthChecks.set(name, check);
        utils_1.logger.info('Health check registered', { name });
    }
    /**
     * Get metrics by type and name
     */
    getMetrics(type, name, timeRange) {
        let filtered = this.metrics;
        if (type) {
            filtered = filtered.filter(m => m.type === type);
        }
        if (name) {
            filtered = filtered.filter(m => m.name === name);
        }
        if (timeRange) {
            filtered = filtered.filter(m => m.timestamp >= timeRange.start && m.timestamp <= timeRange.end);
        }
        return filtered;
    }
    /**
     * Export metrics for external analysis
     */
    exportMetrics(format = 'json') {
        if (format === 'csv') {
            const headers = ['id', 'type', 'name', 'value', 'unit', 'timestamp', 'tags'];
            const rows = this.metrics.map(m => [
                m.id,
                m.type,
                m.name,
                m.value,
                m.unit,
                m.timestamp.toISOString(),
                JSON.stringify(m.tags)
            ]);
            return [headers, ...rows].map(row => row.join(',')).join('\n');
        }
        return JSON.stringify(this.metrics, null, 2);
    }
    // Private methods
    shouldRecordMetric(type) {
        switch (type) {
            case 'performance':
                return this.options.enablePerformanceTracking;
            case 'usage':
                return this.options.enableUsageAnalytics;
            case 'error':
                return this.options.enableErrorTracking;
            default:
                return true;
        }
    }
    cleanupOldMetrics() {
        const cutoff = new Date(Date.now() - (this.options.retentionDays * 24 * 60 * 60 * 1000));
        this.metrics = this.metrics.filter(m => m.timestamp > cutoff);
    }
    checkPerformanceAlerts(metrics) {
        const thresholds = this.options.alertThresholds;
        if (metrics.responseTime > thresholds.responseTime) {
            utils_1.logger.warn('High response time detected', {
                responseTime: metrics.responseTime,
                threshold: thresholds.responseTime
            });
        }
        if (metrics.errorRate > thresholds.errorRate) {
            utils_1.logger.warn('High error rate detected', {
                errorRate: metrics.errorRate,
                threshold: thresholds.errorRate
            });
        }
        if (metrics.memoryUsage > thresholds.memoryUsage) {
            utils_1.logger.warn('High memory usage detected', {
                memoryUsage: metrics.memoryUsage,
                threshold: thresholds.memoryUsage
            });
        }
    }
    initializeHealthChecks() {
        // Register basic health checks
        this.registerHealthCheck('memory', async () => {
            const usage = process.memoryUsage();
            const totalMemory = usage.heapTotal + usage.external;
            const usageRatio = totalMemory / (1024 * 1024 * 1024); // Convert to GB
            return usageRatio < 2; // Less than 2GB
        });
        this.registerHealthCheck('uptime', async () => {
            return process.uptime() > 0;
        });
    }
    startPerformanceMonitoring() {
        if (!this.options.enablePerformanceTracking)
            return;
        // Start periodic performance collection
        setInterval(() => {
            const usage = process.memoryUsage();
            const cpuUsage = process.cpuUsage();
            this.recordPerformance({
                responseTime: 0, // Would be calculated from actual requests
                throughput: 0, // Would be calculated from actual requests
                errorRate: 0, // Would be calculated from actual errors
                memoryUsage: usage.heapUsed / usage.heapTotal,
                cpuUsage: (cpuUsage.user + cpuUsage.system) / 1000000, // Convert to seconds
                activeConnections: 0 // Would be tracked from actual connections
            });
        }, 60000); // Every minute
    }
}
exports.MonitoringSystem = MonitoringSystem;
//# sourceMappingURL=index.js.map