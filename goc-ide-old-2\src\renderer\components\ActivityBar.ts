/**
 * Activity Bar Component - Left sidebar navigation
 */

import { EventEmitter } from 'events';

export class ActivityBar extends EventEmitter {
  private activePanel: string = 'explorer';
  private activityItems: NodeListOf<Element> | null = null;

  constructor() {
    super();
  }

  public async initialize(): Promise<void> {
    console.log('Initializing Activity Bar...');

    this.activityItems = document.querySelectorAll('.activity-item');
    this.setupEventHandlers();

    // Set initial active panel
    this.selectPanel('explorer');

    console.log('Activity Bar initialized');
  }

  private setupEventHandlers(): void {
    if (!this.activityItems) return;

    this.activityItems.forEach(item => {
      item.addEventListener('click', (event) => {
        const target = event.currentTarget as HTMLElement;
        const panelId = target.dataset.panel;
        
        if (panelId) {
          this.selectPanel(panelId);
        }
      });
    });
  }

  public selectPanel(panelId: string): void {
    if (this.activePanel === panelId) return;

    // Update active state
    this.activePanel = panelId;

    // Update UI
    this.updateActiveState();

    // Emit panel change event
    this.emit('panel-changed', panelId);

    console.log(`Activity Bar: Selected panel ${panelId}`);
  }

  private updateActiveState(): void {
    if (!this.activityItems) return;

    this.activityItems.forEach(item => {
      const element = item as HTMLElement;
      const panelId = element.dataset.panel;
      
      if (panelId === this.activePanel) {
        element.classList.add('active');
      } else {
        element.classList.remove('active');
      }
    });
  }

  public getActivePanel(): string {
    return this.activePanel;
  }
}
