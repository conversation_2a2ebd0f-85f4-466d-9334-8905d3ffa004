"use strict";
/**
 * Pattern Recognition Engine
 *
 * Extracts and analyzes patterns from user interactions, code, and behavior
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatternRecognizer = void 0;
const utils_1 = require("../utils");
class PatternRecognizer {
    constructor(config) {
        this.codePatterns = new Map();
        this.namingPatterns = new Map();
        this.architecturePatterns = new Map();
        this.config = config;
    }
    async initialize() {
        utils_1.logger.info('Initializing Pattern Recognizer', 'PatternRecognizer');
        // Load existing patterns from storage
        await this.loadExistingPatterns();
    }
    /**
     * Extract patterns from user interaction
     */
    async extractPatterns(context) {
        const patterns = [];
        try {
            // Extract different types of patterns
            patterns.push(...await this.extractCodingStylePatterns(context));
            patterns.push(...await this.extractNamingConventionPatterns(context));
            patterns.push(...await this.extractArchitecturePatterns(context));
            patterns.push(...await this.extractCodeSnippetPatterns(context));
            patterns.push(...await this.extractWorkflowPatterns(context));
            // Filter patterns by confidence threshold
            return patterns.filter(p => p.confidence >= this.config.learning.confidenceThreshold);
        }
        catch (error) {
            utils_1.logger.error('Failed to extract patterns', 'PatternRecognizer', { error });
            return [];
        }
    }
    /**
     * Extract coding style patterns
     */
    async extractCodingStylePatterns(context) {
        const patterns = [];
        const { response, userFeedback } = context;
        // Analyze code formatting preferences
        const codeBlocks = this.extractCodeBlocks(response.content);
        for (const code of codeBlocks) {
            // Detect indentation style
            const indentationPattern = this.detectIndentationStyle(code);
            if (indentationPattern) {
                patterns.push(this.createPattern({
                    type: 'coding-style',
                    content: indentationPattern.style,
                    context: 'indentation',
                    confidence: indentationPattern.confidence,
                    tags: ['formatting', 'indentation'],
                    metadata: {
                        language: indentationPattern.language,
                        spaces: indentationPattern.spaces,
                        tabs: indentationPattern.tabs
                    }
                }, context));
            }
            // Detect bracket style
            const bracketPattern = this.detectBracketStyle(code);
            if (bracketPattern) {
                patterns.push(this.createPattern({
                    type: 'coding-style',
                    content: bracketPattern.style,
                    context: 'brackets',
                    confidence: bracketPattern.confidence,
                    tags: ['formatting', 'brackets'],
                    metadata: {
                        language: bracketPattern.language,
                        style: bracketPattern.style
                    }
                }, context));
            }
            // Detect line length preferences
            const lineLengthPattern = this.detectLineLengthPreference(code);
            if (lineLengthPattern) {
                patterns.push(this.createPattern({
                    type: 'coding-style',
                    content: `max-line-length-${lineLengthPattern.maxLength}`,
                    context: 'line-length',
                    confidence: lineLengthPattern.confidence,
                    tags: ['formatting', 'line-length'],
                    metadata: {
                        maxLength: lineLengthPattern.maxLength,
                        averageLength: lineLengthPattern.averageLength
                    }
                }, context));
            }
        }
        return patterns;
    }
    /**
     * Extract naming convention patterns
     */
    async extractNamingConventionPatterns(context) {
        const patterns = [];
        const { response } = context;
        const codeBlocks = this.extractCodeBlocks(response.content);
        for (const code of codeBlocks) {
            // Detect variable naming patterns
            const variableNames = this.extractVariableNames(code);
            const namingStyle = this.analyzeNamingStyle(variableNames);
            if (namingStyle.confidence > 0.7) {
                patterns.push(this.createPattern({
                    type: 'naming-convention',
                    content: namingStyle.style,
                    context: 'variables',
                    confidence: namingStyle.confidence,
                    tags: ['naming', 'variables'],
                    metadata: {
                        style: namingStyle.style,
                        examples: namingStyle.examples
                    }
                }, context));
            }
            // Detect function naming patterns
            const functionNames = this.extractFunctionNames(code);
            const functionNamingStyle = this.analyzeNamingStyle(functionNames);
            if (functionNamingStyle.confidence > 0.7) {
                patterns.push(this.createPattern({
                    type: 'naming-convention',
                    content: functionNamingStyle.style,
                    context: 'functions',
                    confidence: functionNamingStyle.confidence,
                    tags: ['naming', 'functions'],
                    metadata: {
                        style: functionNamingStyle.style,
                        examples: functionNamingStyle.examples
                    }
                }, context));
            }
        }
        return patterns;
    }
    /**
     * Extract architecture patterns
     */
    async extractArchitecturePatterns(context) {
        const patterns = [];
        const { messages, response } = context;
        // Analyze architectural decisions from conversation
        const architecturalKeywords = [
            'mvc', 'mvvm', 'repository', 'service', 'factory', 'singleton',
            'observer', 'strategy', 'decorator', 'adapter', 'facade'
        ];
        const conversationText = messages.map(m => m.content).join(' ') + ' ' + response.content;
        for (const keyword of architecturalKeywords) {
            const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
            const matches = conversationText.match(regex);
            if (matches && matches.length > 1) {
                patterns.push(this.createPattern({
                    type: 'architecture-pattern',
                    content: keyword,
                    context: 'architectural-preference',
                    confidence: Math.min(matches.length * 0.2, 1.0),
                    tags: ['architecture', keyword],
                    metadata: {
                        frequency: matches.length,
                        pattern: keyword
                    }
                }, context));
            }
        }
        return patterns;
    }
    /**
     * Extract code snippet patterns
     */
    async extractCodeSnippetPatterns(context) {
        const patterns = [];
        const { response, userFeedback } = context;
        // Only learn from accepted code
        if (userFeedback?.accepted) {
            const codeBlocks = this.extractCodeBlocks(response.content);
            for (const code of codeBlocks) {
                // Extract reusable code snippets
                const snippets = this.extractReusableSnippets(code);
                for (const snippet of snippets) {
                    patterns.push(this.createPattern({
                        type: 'code-snippet',
                        content: snippet.code,
                        context: snippet.context,
                        confidence: 0.8,
                        tags: ['snippet', snippet.language, ...snippet.tags],
                        metadata: {
                            language: snippet.language,
                            purpose: snippet.purpose,
                            complexity: snippet.complexity
                        }
                    }, context));
                }
            }
        }
        return patterns;
    }
    /**
     * Extract workflow patterns
     */
    async extractWorkflowPatterns(context) {
        const patterns = [];
        const { messages } = context;
        // Analyze user's workflow patterns from message sequence
        const workflowSteps = messages.map(m => this.categorizeMessage(m.content));
        if (workflowSteps.length >= 3) {
            const workflowPattern = workflowSteps.join(' -> ');
            patterns.push(this.createPattern({
                type: 'user-workflow',
                content: workflowPattern,
                context: 'conversation-flow',
                confidence: 0.6,
                tags: ['workflow', 'conversation'],
                metadata: {
                    steps: workflowSteps,
                    length: workflowSteps.length
                }
            }, context));
        }
        return patterns;
    }
    // Helper methods
    createPattern(data, context) {
        return {
            id: this.generatePatternId(),
            frequency: 1,
            createdAt: new Date(),
            lastUsed: new Date(),
            userId: context.userId,
            projectId: context.projectId,
            ...data
        };
    }
    generatePatternId() {
        return `pattern_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    extractCodeBlocks(content) {
        const codeBlockRegex = /```[\s\S]*?```/g;
        const matches = content.match(codeBlockRegex) || [];
        return matches.map(block => block.replace(/```\w*\n?/g, '').replace(/```$/g, ''));
    }
    detectIndentationStyle(code) {
        const lines = code.split('\n').filter(line => line.trim().length > 0);
        let spaceIndents = 0;
        let tabIndents = 0;
        for (const line of lines) {
            if (line.startsWith('  '))
                spaceIndents++;
            if (line.startsWith('\t'))
                tabIndents++;
        }
        const total = spaceIndents + tabIndents;
        if (total === 0)
            return null;
        const spaceRatio = spaceIndents / total;
        const tabRatio = tabIndents / total;
        if (spaceRatio > 0.7) {
            return {
                style: 'spaces',
                confidence: spaceRatio,
                language: 'unknown',
                spaces: spaceIndents,
                tabs: tabIndents
            };
        }
        else if (tabRatio > 0.7) {
            return {
                style: 'tabs',
                confidence: tabRatio,
                language: 'unknown',
                spaces: spaceIndents,
                tabs: tabIndents
            };
        }
        return null;
    }
    detectBracketStyle(code) {
        const sameLine = (code.match(/\s*{/g) || []).length;
        const newLine = (code.match(/\n\s*{/g) || []).length;
        const total = sameLine + newLine;
        if (total === 0)
            return null;
        const sameLineRatio = sameLine / total;
        const newLineRatio = newLine / total;
        if (sameLineRatio > 0.7) {
            return { style: 'same-line', confidence: sameLineRatio, language: 'unknown' };
        }
        else if (newLineRatio > 0.7) {
            return { style: 'new-line', confidence: newLineRatio, language: 'unknown' };
        }
        return null;
    }
    detectLineLengthPreference(code) {
        const lines = code.split('\n').filter(line => line.trim().length > 0);
        if (lines.length === 0)
            return null;
        const lengths = lines.map(line => line.length);
        const maxLength = Math.max(...lengths);
        const averageLength = lengths.reduce((sum, len) => sum + len, 0) / lengths.length;
        // Determine preferred max length based on distribution
        const commonLengths = [80, 100, 120, 140];
        let bestMatch = 120;
        let bestScore = 0;
        for (const length of commonLengths) {
            const withinRange = lengths.filter(l => l <= length).length;
            const score = withinRange / lengths.length;
            if (score > bestScore && score > 0.8) {
                bestScore = score;
                bestMatch = length;
            }
        }
        return {
            maxLength: bestMatch,
            averageLength: Math.round(averageLength),
            confidence: bestScore
        };
    }
    extractVariableNames(code) {
        // Simple regex to extract variable names (can be improved for specific languages)
        const variableRegex = /(?:let|const|var|=)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g;
        const matches = [];
        let match;
        while ((match = variableRegex.exec(code)) !== null) {
            matches.push(match[1]);
        }
        return matches;
    }
    extractFunctionNames(code) {
        // Simple regex to extract function names
        const functionRegex = /function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)|([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(/g;
        const matches = [];
        let match;
        while ((match = functionRegex.exec(code)) !== null) {
            matches.push(match[1] || match[2]);
        }
        return matches;
    }
    analyzeNamingStyle(names) {
        if (names.length === 0)
            return { style: 'unknown', confidence: 0, examples: [] };
        let camelCase = 0;
        let snakeCase = 0;
        let pascalCase = 0;
        for (const name of names) {
            if (/^[a-z][a-zA-Z0-9]*$/.test(name))
                camelCase++;
            else if (/^[a-z][a-z0-9_]*$/.test(name))
                snakeCase++;
            else if (/^[A-Z][a-zA-Z0-9]*$/.test(name))
                pascalCase++;
        }
        const total = names.length;
        const camelRatio = camelCase / total;
        const snakeRatio = snakeCase / total;
        const pascalRatio = pascalCase / total;
        if (camelRatio > 0.6)
            return { style: 'camelCase', confidence: camelRatio, examples: names.slice(0, 3) };
        if (snakeRatio > 0.6)
            return { style: 'snake_case', confidence: snakeRatio, examples: names.slice(0, 3) };
        if (pascalRatio > 0.6)
            return { style: 'PascalCase', confidence: pascalRatio, examples: names.slice(0, 3) };
        return { style: 'mixed', confidence: 0.3, examples: names.slice(0, 3) };
    }
    extractReusableSnippets(code) {
        // This is a simplified implementation - can be enhanced with AST parsing
        const snippets = [];
        const lines = code.split('\n');
        // Look for common patterns like function definitions, class definitions, etc.
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (line.includes('function') || line.includes('class') || line.includes('interface')) {
                const snippet = this.extractBlock(lines, i);
                if (snippet.length > 2 && snippet.length < 20) {
                    snippets.push({
                        code: snippet.join('\n'),
                        context: 'definition',
                        language: 'javascript', // Can be detected
                        tags: ['reusable'],
                        purpose: 'code-structure',
                        complexity: snippet.length
                    });
                }
            }
        }
        return snippets;
    }
    extractBlock(lines, startIndex) {
        const block = [lines[startIndex]];
        let braceCount = 0;
        let inBlock = false;
        for (let i = startIndex; i < lines.length; i++) {
            const line = lines[i];
            for (const char of line) {
                if (char === '{') {
                    braceCount++;
                    inBlock = true;
                }
                else if (char === '}') {
                    braceCount--;
                }
            }
            if (i > startIndex)
                block.push(line);
            if (inBlock && braceCount === 0)
                break;
            if (block.length > 50)
                break; // Prevent overly long blocks
        }
        return block;
    }
    categorizeMessage(content) {
        const lowerContent = content.toLowerCase();
        if (lowerContent.includes('create') || lowerContent.includes('generate'))
            return 'create';
        if (lowerContent.includes('fix') || lowerContent.includes('debug'))
            return 'debug';
        if (lowerContent.includes('explain') || lowerContent.includes('how'))
            return 'explain';
        if (lowerContent.includes('refactor') || lowerContent.includes('improve'))
            return 'refactor';
        if (lowerContent.includes('test') || lowerContent.includes('testing'))
            return 'test';
        return 'general';
    }
    async loadExistingPatterns() {
        // Load patterns from storage - implementation depends on storage mechanism
        // This would typically load from the knowledge base
    }
    async dispose() {
        // Cleanup resources
        this.codePatterns.clear();
        this.namingPatterns.clear();
        this.architecturePatterns.clear();
    }
}
exports.PatternRecognizer = PatternRecognizer;
//# sourceMappingURL=pattern-recognition.js.map