/**
 * Advanced File Operations Tool
 *
 * Implements str-replace-editor, view, save-file, remove-files with validation and backup
 */
import { ToolResult, FileRange } from './index';
export interface ViewOptions {
    range?: FileRange;
    searchRegex?: string;
    contextLines?: number;
    caseSensitive?: boolean;
}
export interface ReplaceOperation {
    oldStr: string;
    newStr: string;
    startLine?: number;
    endLine?: number;
}
export interface FileChange {
    type: 'create' | 'modify' | 'delete';
    path: string;
    linesAdded?: number;
    linesDeleted?: number;
    backup?: string;
}
export declare class FileOperations {
    private backupDir;
    private changeHistory;
    constructor();
    private ensureBackupDir;
    private createBackup;
    /**
     * View file content with optional range and search
     */
    view(filePath: string, options?: ViewOptions): Promise<ToolResult>;
    /**
     * String replace editor with backup and validation
     */
    strReplace(filePath: string, operations: ReplaceOperation[]): Promise<ToolResult>;
    /**
     * Save new file with validation
     */
    saveFile(filePath: string, content: string, options?: {
        overwrite?: boolean;
    }): Promise<ToolResult>;
    /**
     * Remove files with undo capability
     */
    removeFiles(filePaths: string[]): Promise<ToolResult>;
    /**
     * Get change history
     */
    getChangeHistory(): FileChange[];
    /**
     * Restore file from backup
     */
    restoreFromBackup(backupPath: string, targetPath?: string): Promise<ToolResult>;
}
//# sourceMappingURL=file-operations.d.ts.map