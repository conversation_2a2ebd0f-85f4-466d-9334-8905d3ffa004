"use strict";
/**
 * Workspace Detector Tool
 *
 * Workspace detection, configuration, and project structure analysis
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkspaceDetector = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class WorkspaceDetector {
    constructor() {
        this.projectIndicators = {
            'node-js': {
                files: ['package.json', 'package-lock.json', 'yarn.lock', 'pnpm-lock.yaml'],
                directories: ['node_modules'],
                patterns: [/\.js$/, /\.ts$/, /\.jsx$/, /\.tsx$/],
                language: 'JavaScript/TypeScript',
                packageManager: 'npm'
            },
            'react': {
                files: ['package.json'],
                directories: ['src', 'public'],
                patterns: [/\.jsx$/, /\.tsx$/, /react/i],
                language: 'JavaScript/TypeScript',
                framework: 'React'
            },
            'vue': {
                files: ['package.json', 'vue.config.js'],
                directories: ['src'],
                patterns: [/\.vue$/, /vue/i],
                language: 'JavaScript/TypeScript',
                framework: 'Vue.js'
            },
            'angular': {
                files: ['angular.json', 'package.json'],
                directories: ['src/app'],
                patterns: [/\.component\.ts$/, /angular/i],
                language: 'TypeScript',
                framework: 'Angular'
            },
            'laravel': {
                files: ['composer.json', 'artisan', '.env.example'],
                directories: ['app', 'config', 'database', 'resources'],
                patterns: [/\.php$/, /laravel/i],
                language: 'PHP',
                framework: 'Laravel',
                packageManager: 'composer'
            },
            'python': {
                files: ['requirements.txt', 'setup.py', 'pyproject.toml', 'Pipfile'],
                directories: ['venv', '__pycache__'],
                patterns: [/\.py$/, /\.pyw$/],
                language: 'Python',
                packageManager: 'pip'
            },
            'django': {
                files: ['manage.py', 'requirements.txt'],
                directories: ['templates', 'static'],
                patterns: [/\.py$/, /django/i],
                language: 'Python',
                framework: 'Django'
            },
            'rust': {
                files: ['Cargo.toml', 'Cargo.lock'],
                directories: ['src', 'target'],
                patterns: [/\.rs$/],
                language: 'Rust',
                packageManager: 'cargo'
            },
            'go': {
                files: ['go.mod', 'go.sum'],
                directories: [],
                patterns: [/\.go$/],
                language: 'Go',
                packageManager: 'go'
            },
            'java': {
                files: ['pom.xml', 'build.gradle', 'build.gradle.kts'],
                directories: ['src/main/java', 'target', 'build'],
                patterns: [/\.java$/],
                language: 'Java'
            },
            'csharp': {
                files: ['*.csproj', '*.sln'],
                directories: ['bin', 'obj'],
                patterns: [/\.cs$/],
                language: 'C#'
            },
            'flutter': {
                files: ['pubspec.yaml', 'pubspec.lock'],
                directories: ['lib', 'android', 'ios'],
                patterns: [/\.dart$/],
                language: 'Dart',
                framework: 'Flutter'
            }
        };
    }
    /**
     * Detect workspace and analyze project structure
     */
    async detectWorkspace(projectPath = process.cwd()) {
        try {
            if (!fs.existsSync(projectPath)) {
                return {
                    success: false,
                    error: `Path does not exist: ${projectPath}`
                };
            }
            const workspaceInfo = {
                rootPath: projectPath,
                projectTypes: [],
                primaryType: {},
                structure: {},
                packageManagers: [],
                configFiles: [],
                totalFiles: 0,
                totalDirectories: 0
            };
            // Detect project types
            workspaceInfo.projectTypes = await this.detectProjectTypes(projectPath);
            workspaceInfo.primaryType = this.getPrimaryProjectType(workspaceInfo.projectTypes);
            // Analyze directory structure
            workspaceInfo.structure = await this.analyzeStructure(projectPath, 0, 3); // Max depth 3
            const counts = this.countFilesAndDirectories(workspaceInfo.structure);
            workspaceInfo.totalFiles = counts.files;
            workspaceInfo.totalDirectories = counts.directories;
            // Detect Git repository
            workspaceInfo.gitRepository = await this.detectGitRepository(projectPath);
            // Find package managers
            workspaceInfo.packageManagers = this.detectPackageManagers(projectPath);
            // Find configuration files
            workspaceInfo.configFiles = this.findConfigFiles(projectPath);
            return {
                success: true,
                data: workspaceInfo
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Get project configuration
     */
    async getProjectConfig(projectPath = process.cwd()) {
        try {
            const configs = {};
            // Read common config files
            const configFiles = [
                'package.json',
                'composer.json',
                'pyproject.toml',
                'Cargo.toml',
                'go.mod',
                'pom.xml',
                'build.gradle',
                'pubspec.yaml',
                '.env',
                '.env.example',
                'tsconfig.json',
                'webpack.config.js',
                'vite.config.js',
                'vue.config.js',
                'angular.json'
            ];
            for (const configFile of configFiles) {
                const configPath = path.join(projectPath, configFile);
                if (fs.existsSync(configPath)) {
                    try {
                        const content = fs.readFileSync(configPath, 'utf8');
                        if (configFile.endsWith('.json')) {
                            configs[configFile] = JSON.parse(content);
                        }
                        else if (configFile.endsWith('.toml')) {
                            // Basic TOML parsing (simplified)
                            configs[configFile] = this.parseBasicToml(content);
                        }
                        else {
                            configs[configFile] = content;
                        }
                    }
                    catch (parseError) {
                        configs[configFile] = { error: 'Failed to parse', content: fs.readFileSync(configPath, 'utf8') };
                    }
                }
            }
            return {
                success: true,
                data: {
                    projectPath,
                    configs,
                    configFiles: Object.keys(configs)
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Detect project types based on files and patterns
     */
    async detectProjectTypes(projectPath) {
        const projectTypes = [];
        for (const [typeName, indicators] of Object.entries(this.projectIndicators)) {
            let confidence = 0;
            const foundIndicators = [];
            // Check for indicator files
            for (const file of indicators.files) {
                const filePath = path.join(projectPath, file);
                if (fs.existsSync(filePath)) {
                    confidence += 20;
                    foundIndicators.push(file);
                }
            }
            // Check for indicator directories
            for (const dir of indicators.directories) {
                const dirPath = path.join(projectPath, dir);
                if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
                    confidence += 15;
                    foundIndicators.push(dir);
                }
            }
            // Check file patterns
            const files = this.getFilesRecursive(projectPath, 2); // Max depth 2 for performance
            for (const file of files) {
                for (const pattern of indicators.patterns) {
                    if (pattern.test(file)) {
                        confidence += 5;
                        break;
                    }
                }
            }
            // Check package.json content for framework indicators
            if (typeName !== 'node-js') {
                const packageJsonPath = path.join(projectPath, 'package.json');
                if (fs.existsSync(packageJsonPath)) {
                    try {
                        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
                        const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
                        for (const pattern of indicators.patterns) {
                            for (const dep of Object.keys(deps)) {
                                if (pattern.test(dep)) {
                                    confidence += 10;
                                    foundIndicators.push(`dependency: ${dep}`);
                                    break;
                                }
                            }
                        }
                    }
                    catch {
                        // Ignore JSON parse errors
                    }
                }
            }
            if (confidence > 0) {
                projectTypes.push({
                    name: typeName,
                    confidence,
                    indicators: foundIndicators,
                    configFiles: indicators.files.filter(f => fs.existsSync(path.join(projectPath, f))),
                    packageManager: indicators.packageManager,
                    framework: indicators.framework,
                    language: indicators.language
                });
            }
        }
        return projectTypes.sort((a, b) => b.confidence - a.confidence);
    }
    /**
     * Get primary project type (highest confidence)
     */
    getPrimaryProjectType(projectTypes) {
        return projectTypes[0] || {
            name: 'unknown',
            confidence: 0,
            indicators: [],
            configFiles: [],
            language: 'Unknown'
        };
    }
    /**
     * Analyze directory structure
     */
    async analyzeStructure(dirPath, currentDepth, maxDepth) {
        const stats = fs.statSync(dirPath);
        const name = path.basename(dirPath);
        const structure = {
            name,
            path: dirPath,
            type: stats.isDirectory() ? 'directory' : 'file',
            size: stats.isFile() ? stats.size : undefined,
            depth: currentDepth
        };
        if (stats.isDirectory() && currentDepth < maxDepth) {
            try {
                const entries = fs.readdirSync(dirPath);
                structure.children = [];
                for (const entry of entries) {
                    // Skip hidden files and common ignore patterns
                    if (entry.startsWith('.') ||
                        ['node_modules', 'vendor', 'target', 'build', 'dist', '__pycache__'].includes(entry)) {
                        continue;
                    }
                    const entryPath = path.join(dirPath, entry);
                    try {
                        const childStructure = await this.analyzeStructure(entryPath, currentDepth + 1, maxDepth);
                        structure.children.push(childStructure);
                    }
                    catch {
                        // Skip entries that can't be accessed
                    }
                }
                // Sort children: directories first, then files
                structure.children.sort((a, b) => {
                    if (a.type !== b.type) {
                        return a.type === 'directory' ? -1 : 1;
                    }
                    return a.name.localeCompare(b.name);
                });
            }
            catch {
                // Handle permission errors
            }
        }
        return structure;
    }
    /**
     * Count files and directories in structure
     */
    countFilesAndDirectories(structure) {
        let files = 0;
        let directories = 0;
        if (structure.type === 'file') {
            files = 1;
        }
        else {
            directories = 1;
        }
        if (structure.children) {
            for (const child of structure.children) {
                const counts = this.countFilesAndDirectories(child);
                files += counts.files;
                directories += counts.directories;
            }
        }
        return { files, directories };
    }
    /**
     * Detect Git repository
     */
    async detectGitRepository(projectPath) {
        const gitDir = path.join(projectPath, '.git');
        if (!fs.existsSync(gitDir)) {
            return { isRepository: false };
        }
        const gitInfo = { isRepository: true };
        try {
            // Try to get current branch
            const headPath = path.join(gitDir, 'HEAD');
            if (fs.existsSync(headPath)) {
                const headContent = fs.readFileSync(headPath, 'utf8').trim();
                const branchMatch = headContent.match(/ref: refs\/heads\/(.+)/);
                if (branchMatch) {
                    gitInfo.branch = branchMatch[1];
                }
            }
            // Try to get remotes
            const configPath = path.join(gitDir, 'config');
            if (fs.existsSync(configPath)) {
                const configContent = fs.readFileSync(configPath, 'utf8');
                const remoteMatches = configContent.match(/\[remote "(.+)"\]/g);
                if (remoteMatches) {
                    gitInfo.remotes = remoteMatches.map(match => match.match(/\[remote "(.+)"\]/)[1]);
                }
            }
        }
        catch {
            // Ignore errors in Git info extraction
        }
        return gitInfo;
    }
    /**
     * Detect package managers
     */
    detectPackageManagers(projectPath) {
        const managers = [];
        const managerFiles = {
            'npm': ['package.json', 'package-lock.json'],
            'yarn': ['yarn.lock'],
            'pnpm': ['pnpm-lock.yaml'],
            'composer': ['composer.json', 'composer.lock'],
            'pip': ['requirements.txt'],
            'poetry': ['pyproject.toml', 'poetry.lock'],
            'cargo': ['Cargo.toml', 'Cargo.lock'],
            'go': ['go.mod', 'go.sum']
        };
        for (const [manager, files] of Object.entries(managerFiles)) {
            if (files.some(file => fs.existsSync(path.join(projectPath, file)))) {
                managers.push(manager);
            }
        }
        return managers;
    }
    /**
     * Find configuration files
     */
    findConfigFiles(projectPath) {
        const configPatterns = [
            /\.config\.(js|ts|json)$/,
            /\.rc$/,
            /\.yml$/,
            /\.yaml$/,
            /\.toml$/,
            /\.ini$/,
            /^\.env/,
            /^\.gitignore$/,
            /^\.eslintrc/,
            /^\.prettierrc/,
            /^tsconfig\.json$/,
            /^webpack\.config/,
            /^vite\.config/,
            /^rollup\.config/
        ];
        const configFiles = [];
        try {
            const entries = fs.readdirSync(projectPath);
            for (const entry of entries) {
                if (configPatterns.some(pattern => pattern.test(entry))) {
                    configFiles.push(entry);
                }
            }
        }
        catch {
            // Handle permission errors
        }
        return configFiles.sort();
    }
    /**
     * Get files recursively with depth limit
     */
    getFilesRecursive(dirPath, maxDepth, currentDepth = 0) {
        if (currentDepth >= maxDepth)
            return [];
        const files = [];
        try {
            const entries = fs.readdirSync(dirPath);
            for (const entry of entries) {
                if (entry.startsWith('.') || ['node_modules', 'vendor'].includes(entry)) {
                    continue;
                }
                const entryPath = path.join(dirPath, entry);
                const stats = fs.statSync(entryPath);
                if (stats.isFile()) {
                    files.push(entry);
                }
                else if (stats.isDirectory()) {
                    files.push(...this.getFilesRecursive(entryPath, maxDepth, currentDepth + 1));
                }
            }
        }
        catch {
            // Handle permission errors
        }
        return files;
    }
    /**
     * Basic TOML parser (simplified)
     */
    parseBasicToml(content) {
        const result = {};
        const lines = content.split('\n');
        let currentSection = '';
        for (const line of lines) {
            const trimmed = line.trim();
            if (!trimmed || trimmed.startsWith('#'))
                continue;
            // Section header
            if (trimmed.startsWith('[') && trimmed.endsWith(']')) {
                currentSection = trimmed.slice(1, -1);
                if (!result[currentSection]) {
                    result[currentSection] = {};
                }
                continue;
            }
            // Key-value pair
            const equalIndex = trimmed.indexOf('=');
            if (equalIndex > 0) {
                const key = trimmed.substring(0, equalIndex).trim();
                const value = trimmed.substring(equalIndex + 1).trim().replace(/^["']|["']$/g, '');
                if (currentSection) {
                    result[currentSection][key] = value;
                }
                else {
                    result[key] = value;
                }
            }
        }
        return result;
    }
}
exports.WorkspaceDetector = WorkspaceDetector;
//# sourceMappingURL=workspace.js.map