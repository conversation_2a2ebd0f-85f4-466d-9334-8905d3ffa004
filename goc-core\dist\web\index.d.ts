/**
 * Enhanced Web Capabilities Module
 *
 * Advanced web research, content processing, and learning integration
 */
import { SearchResult, WebContent } from '../types';
export interface EnhancedSearchResult extends SearchResult {
    content?: string;
    relevanceScore: number;
    source: string;
    publishedDate?: Date;
    metadata?: Record<string, any>;
}
export interface WebSearchOptions {
    maxResults?: number;
    language?: string;
    region?: string;
    timeRange?: 'day' | 'week' | 'month' | 'year' | 'all';
    safeSearch?: boolean;
    includeContent?: boolean;
    filterDomains?: string[];
    excludeDomains?: string[];
}
export interface ContentExtractionResult {
    title: string;
    content: string;
    summary: string;
    codeExamples: Array<{
        language: string;
        code: string;
        description?: string;
    }>;
    links: Array<{
        url: string;
        title: string;
        type: 'internal' | 'external';
    }>;
    metadata: {
        wordCount: number;
        readingTime: number;
        language: string;
        publishedDate?: Date;
        author?: string;
        tags?: string[];
    };
}
export interface LearningEvent {
    type: 'search' | 'content_extraction' | 'code_discovery' | 'pattern_learning';
    query?: string;
    url?: string;
    content?: string;
    patterns?: string[];
    timestamp: Date;
    metadata?: Record<string, any>;
}
export interface TrendAnalysis {
    topic: string;
    trend: 'rising' | 'stable' | 'declining';
    confidence: number;
    relatedTopics: string[];
    timeframe: string;
    sources: string[];
}
/**
 * Enhanced Web Research Engine
 */
export declare class WebResearcher {
    private searchEngines;
    private learningEvents;
    constructor();
    /**
     * Enhanced search with multiple engines and learning integration
     */
    search(query: string, options?: WebSearchOptions): Promise<EnhancedSearchResult[]>;
    /**
     * Fetch and process content from URL
     */
    fetchContent(url: string): Promise<WebContent>;
    /**
     * Enhanced content extraction with code examples and metadata
     */
    extractContent(url: string, options?: {
        includeSummary?: boolean;
        extractCode?: boolean;
        analyzePatterns?: boolean;
    }): Promise<ContentExtractionResult>;
    /**
     * Enhanced content summarization
     */
    summarizeContent(content: string): Promise<string>;
    /**
     * Research a specific technology or topic
     */
    researchTopic(topic: string, options?: {
        depth?: 'basic' | 'intermediate' | 'advanced';
        includeExamples?: boolean;
        includeTrends?: boolean;
        maxSources?: number;
    }): Promise<{
        summary: string;
        keyPoints: string[];
        codeExamples: Array<{
            language: string;
            code: string;
            description: string;
            source: string;
        }>;
        trends?: TrendAnalysis[];
        sources: EnhancedSearchResult[];
        relatedTopics: string[];
    }>;
    /**
     * Get learning insights and recommendations
     */
    getLearningInsights(): Promise<{
        topSearches: Array<{
            query: string;
            count: number;
        }>;
        discoveredPatterns: Array<{
            pattern: string;
            frequency: number;
            sources: string[];
        }>;
        recommendedTopics: string[];
        trendingTechnologies: string[];
    }>;
    private initializeSearchEngines;
    private searchDuckDuckGo;
    private fallbackSearch;
    private enhanceWithContent;
    private processHtmlContent;
    private extractTitle;
    private extractTextContent;
    private extractCodeExamples;
    private extractLinks;
    private detectLanguage;
    private extractPublishedDate;
    private extractAuthor;
    private extractTags;
    private calculateWordFrequency;
    private recordLearningEvent;
    private extractSearchPatterns;
    private learnFromCodeExamples;
    private generateResearchQueries;
    private deduplicateResults;
    private generateTopicSummary;
    private extractKeyPoints;
    private analyzeTrends;
    private extractRelatedTopics;
    private generateRecommendations;
    private extractTrendingTechnologies;
}
export * from '../types';
//# sourceMappingURL=index.d.ts.map