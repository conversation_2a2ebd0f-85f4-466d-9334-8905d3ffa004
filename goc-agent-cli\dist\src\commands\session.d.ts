/**
 * Session Management Command
 *
 * Handle chat sessions with GOC Agent backend
 */
import { Command } from 'commander';
import { BackendAPIClient } from '../../services/BackendAPIClient';
export declare class SessionCommand {
    private apiClient;
    constructor(apiClient: BackendAPIClient);
    register(program: Command): void;
    listSessions(): Promise<void>;
    createSession(options: any): Promise<void>;
    showSession(sessionId: string): Promise<void>;
    deleteSession(sessionId: string, options: any): Promise<void>;
    renameSession(sessionId: string, options: any): Promise<void>;
}
//# sourceMappingURL=session.d.ts.map