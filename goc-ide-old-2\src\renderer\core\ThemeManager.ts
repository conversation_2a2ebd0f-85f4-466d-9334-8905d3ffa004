/**
 * Theme Manager - Handles IDE themes and styling
 */

export class ThemeManager {
  private currentTheme = 'dark';

  public async initialize(): Promise<void> {
    console.log('Initializing Theme Manager...');

    // Apply default theme
    this.applyTheme(this.currentTheme);

    console.log('Theme Manager initialized');
  }

  public applyTheme(themeName: string): void {
    this.currentTheme = themeName;

    // Remove existing theme classes
    document.body.classList.remove('theme-dark', 'theme-light', 'theme-high-contrast');

    // Apply new theme class
    document.body.classList.add(`theme-${themeName}`);

    // Update CSS custom properties
    this.updateThemeProperties(themeName);

    console.log(`Applied theme: ${themeName}`);
  }

  private updateThemeProperties(themeName: string): void {
    const root = document.documentElement;

    switch (themeName) {
      case 'dark':
        root.style.setProperty('--bg-primary', '#1e1e1e');
        root.style.setProperty('--bg-secondary', '#252526');
        root.style.setProperty('--bg-tertiary', '#2d2d30');
        root.style.setProperty('--text-primary', '#cccccc');
        root.style.setProperty('--text-secondary', '#858585');
        root.style.setProperty('--accent-primary', '#007acc');
        root.style.setProperty('--border-color', '#3e3e42');
        break;

      case 'light':
        root.style.setProperty('--bg-primary', '#ffffff');
        root.style.setProperty('--bg-secondary', '#f3f3f3');
        root.style.setProperty('--bg-tertiary', '#e8e8e8');
        root.style.setProperty('--text-primary', '#333333');
        root.style.setProperty('--text-secondary', '#666666');
        root.style.setProperty('--accent-primary', '#0066cc');
        root.style.setProperty('--border-color', '#d0d0d0');
        break;

      case 'high-contrast':
        root.style.setProperty('--bg-primary', '#000000');
        root.style.setProperty('--bg-secondary', '#000000');
        root.style.setProperty('--bg-tertiary', '#1a1a1a');
        root.style.setProperty('--text-primary', '#ffffff');
        root.style.setProperty('--text-secondary', '#ffffff');
        root.style.setProperty('--accent-primary', '#ffff00');
        root.style.setProperty('--border-color', '#ffffff');
        break;
    }
  }

  public getCurrentTheme(): string {
    return this.currentTheme;
  }

  public getAvailableThemes(): string[] {
    return ['dark', 'light', 'high-contrast'];
  }

  public toggleTheme(): void {
    const themes = this.getAvailableThemes();
    const currentIndex = themes.indexOf(this.currentTheme);
    const nextIndex = (currentIndex + 1) % themes.length;
    this.applyTheme(themes[nextIndex]);
  }
}
