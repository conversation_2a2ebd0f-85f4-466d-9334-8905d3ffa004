/**
 * Terminal Manager - Handles terminal integration
 */

export class TerminalManager {
  private terminals: Map<string, any> = new Map();
  private activeTerminalId: string | null = null;

  public async initialize(): Promise<void> {
    console.log('Initializing Terminal Manager...');
    console.log('Terminal Manager initialized');
  }

  public async createTerminal(cwd?: string): Promise<string> {
    try {
      const terminalId = await window.electronAPI.terminal.create(cwd);
      console.log('Terminal created:', terminalId);
      return terminalId;
    } catch (error) {
      console.error('Failed to create terminal:', error);
      throw error;
    }
  }

  public async toggle(): void {
    const panelArea = document.getElementById('panel-area');
    if (panelArea) {
      const isVisible = panelArea.style.display !== 'none';
      panelArea.style.display = isVisible ? 'none' : 'flex';
      
      if (!isVisible && !this.activeTerminalId) {
        // Create a terminal if none exists
        await this.createTerminal();
      }
    }
  }

  public dispose(): void {
    this.terminals.clear();
    console.log('Terminal Manager disposed');
  }
}
