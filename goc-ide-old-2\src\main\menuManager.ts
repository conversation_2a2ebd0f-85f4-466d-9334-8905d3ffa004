/**
 * Menu Manager - Handles application menu creation and management
 */

import { Menu, MenuItem, BrowserWindow, app } from 'electron';

export class MenuManager {
  public setupMenu(mainWindow: BrowserWindow): void {
    const template = this.createMenuTemplate(mainWindow);
    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  private createMenuTemplate(mainWindow: BrowserWindow): any[] {
    const isMac = process.platform === 'darwin';

    return [
      // App Menu (macOS only)
      ...(isMac ? [{
        label: app.getName(),
        submenu: [
          { role: 'about' },
          { type: 'separator' },
          { role: 'services' },
          { type: 'separator' },
          { role: 'hide' },
          { role: 'hideothers' },
          { role: 'unhide' },
          { type: 'separator' },
          { role: 'quit' }
        ]
      }] : []),

      // File Menu
      {
        label: 'File',
        submenu: [
          {
            label: 'New File',
            accelerator: 'CmdOrCtrl+N',
            click: () => mainWindow.webContents.send('menu-new-file')
          },
          {
            label: 'Open File...',
            accelerator: 'CmdOrCtrl+O',
            click: () => mainWindow.webContents.send('menu-open-file')
          },
          {
            label: 'Open Folder...',
            accelerator: 'CmdOrCtrl+Shift+O',
            click: () => mainWindow.webContents.send('menu-open-folder')
          },
          { type: 'separator' },
          {
            label: 'Save',
            accelerator: 'CmdOrCtrl+S',
            click: () => mainWindow.webContents.send('menu-save')
          },
          {
            label: 'Save As...',
            accelerator: 'CmdOrCtrl+Shift+S',
            click: () => mainWindow.webContents.send('menu-save-as')
          },
          { type: 'separator' },
          ...(isMac ? [] : [{ role: 'quit' }])
        ]
      },

      // Edit Menu
      {
        label: 'Edit',
        submenu: [
          { role: 'undo' },
          { role: 'redo' },
          { type: 'separator' },
          { role: 'cut' },
          { role: 'copy' },
          { role: 'paste' },
          { role: 'selectall' },
          { type: 'separator' },
          {
            label: 'Find',
            accelerator: 'CmdOrCtrl+F',
            click: () => mainWindow.webContents.send('menu-find')
          },
          {
            label: 'Replace',
            accelerator: 'CmdOrCtrl+H',
            click: () => mainWindow.webContents.send('menu-replace')
          }
        ]
      },

      // View Menu
      {
        label: 'View',
        submenu: [
          {
            label: 'Command Palette',
            accelerator: 'CmdOrCtrl+Shift+P',
            click: () => mainWindow.webContents.send('menu-command-palette')
          },
          { type: 'separator' },
          {
            label: 'Explorer',
            accelerator: 'CmdOrCtrl+Shift+E',
            click: () => mainWindow.webContents.send('menu-toggle-explorer')
          },
          {
            label: 'Search',
            accelerator: 'CmdOrCtrl+Shift+F',
            click: () => mainWindow.webContents.send('menu-toggle-search')
          },
          {
            label: 'Terminal',
            accelerator: 'CmdOrCtrl+`',
            click: () => mainWindow.webContents.send('menu-toggle-terminal')
          },
          { type: 'separator' },
          { role: 'reload' },
          { role: 'forceReload' },
          { role: 'toggleDevTools' },
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' }
        ]
      },

      // GOC Agent Menu
      {
        label: 'GOC Agent',
        submenu: [
          {
            label: 'Open Chat',
            accelerator: 'CmdOrCtrl+Shift+A',
            click: () => mainWindow.webContents.send('menu-open-chat')
          },
          {
            label: 'Explain Code',
            accelerator: 'CmdOrCtrl+Shift+E',
            click: () => mainWindow.webContents.send('menu-explain-code')
          },
          {
            label: 'Generate Code',
            accelerator: 'CmdOrCtrl+Shift+G',
            click: () => mainWindow.webContents.send('menu-generate-code')
          },
          {
            label: 'Refactor Code',
            accelerator: 'CmdOrCtrl+Shift+R',
            click: () => mainWindow.webContents.send('menu-refactor-code')
          },
          { type: 'separator' },
          {
            label: 'Settings',
            click: () => mainWindow.webContents.send('menu-settings')
          }
        ]
      },

      // Window Menu
      {
        label: 'Window',
        submenu: [
          { role: 'minimize' },
          { role: 'close' },
          ...(isMac ? [
            { type: 'separator' },
            { role: 'front' },
            { type: 'separator' },
            { role: 'window' }
          ] : [])
        ]
      },

      // Help Menu
      {
        role: 'help',
        submenu: [
          {
            label: 'About GOC IDE',
            click: () => {
              mainWindow.webContents.send('menu-about');
            }
          },
          {
            label: 'Documentation',
            click: async () => {
              const { shell } = require('electron');
              await shell.openExternal('https://goc-agent.com/docs');
            }
          },
          {
            label: 'Report Issue',
            click: async () => {
              const { shell } = require('electron');
              await shell.openExternal('https://github.com/goc-agent/goc-ide/issues');
            }
          }
        ]
      }
    ];
  }
}
