"use strict";
/**
 * Diagnostics Provider Tool
 *
 * Implements IDE error detection and code analysis
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiagnosticsProvider = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class DiagnosticsProvider {
    constructor() {
        this.diagnostics = new Map();
    }
    /**
     * Get diagnostics for specific files
     */
    async getDiagnostics(filePaths) {
        try {
            const results = {};
            let totalIssues = 0;
            let errors = 0;
            let warnings = 0;
            let infos = 0;
            let hints = 0;
            for (const filePath of filePaths) {
                if (!fs.existsSync(filePath)) {
                    results[filePath] = [{
                            file: filePath,
                            line: 1,
                            column: 1,
                            severity: 'error',
                            message: 'File not found',
                            source: 'file-system'
                        }];
                    errors++;
                    totalIssues++;
                    continue;
                }
                const diagnostics = await this.analyzeFile(filePath);
                results[filePath] = diagnostics;
                diagnostics.forEach(diag => {
                    totalIssues++;
                    switch (diag.severity) {
                        case 'error':
                            errors++;
                            break;
                        case 'warning':
                            warnings++;
                            break;
                        case 'info':
                            infos++;
                            break;
                        case 'hint':
                            hints++;
                            break;
                    }
                });
            }
            const summary = {
                totalFiles: filePaths.length,
                totalIssues,
                errors,
                warnings,
                infos,
                hints,
                fileIssues: results
            };
            return {
                success: true,
                data: summary
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Analyze a single file for issues
     */
    async analyzeFile(filePath) {
        const diagnostics = [];
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            const lines = content.split('\n');
            const ext = path.extname(filePath).toLowerCase();
            // Basic syntax checks based on file type
            switch (ext) {
                case '.js':
                case '.ts':
                    diagnostics.push(...this.analyzeJavaScript(filePath, lines));
                    break;
                case '.py':
                    diagnostics.push(...this.analyzePython(filePath, lines));
                    break;
                case '.php':
                    diagnostics.push(...this.analyzePHP(filePath, lines));
                    break;
                case '.json':
                    diagnostics.push(...this.analyzeJSON(filePath, content));
                    break;
                case '.yaml':
                case '.yml':
                    diagnostics.push(...this.analyzeYAML(filePath, content));
                    break;
                default:
                    diagnostics.push(...this.analyzeGeneric(filePath, lines));
            }
            // Store diagnostics
            this.diagnostics.set(filePath, diagnostics);
        }
        catch (error) {
            diagnostics.push({
                file: filePath,
                line: 1,
                column: 1,
                severity: 'error',
                message: `Failed to analyze file: ${error}`,
                source: 'analyzer'
            });
        }
        return diagnostics;
    }
    /**
     * Analyze JavaScript/TypeScript files
     */
    analyzeJavaScript(filePath, lines) {
        const diagnostics = [];
        lines.forEach((line, index) => {
            const lineNum = index + 1;
            const trimmed = line.trim();
            // Check for common issues
            if (trimmed.includes('console.log') && !trimmed.startsWith('//')) {
                diagnostics.push({
                    file: filePath,
                    line: lineNum,
                    column: line.indexOf('console.log') + 1,
                    severity: 'warning',
                    message: 'console.log statement found - consider removing for production',
                    code: 'no-console',
                    source: 'eslint'
                });
            }
            if (trimmed.includes('debugger') && !trimmed.startsWith('//')) {
                diagnostics.push({
                    file: filePath,
                    line: lineNum,
                    column: line.indexOf('debugger') + 1,
                    severity: 'warning',
                    message: 'debugger statement found - remove before production',
                    code: 'no-debugger',
                    source: 'eslint'
                });
            }
            // Check for missing semicolons (basic check)
            if (trimmed.length > 0 &&
                !trimmed.endsWith(';') &&
                !trimmed.endsWith('{') &&
                !trimmed.endsWith('}') &&
                !trimmed.startsWith('//') &&
                !trimmed.startsWith('*') &&
                !trimmed.includes('if ') &&
                !trimmed.includes('for ') &&
                !trimmed.includes('while ') &&
                !trimmed.includes('function ')) {
                diagnostics.push({
                    file: filePath,
                    line: lineNum,
                    column: line.length,
                    severity: 'hint',
                    message: 'Missing semicolon',
                    code: 'semi',
                    source: 'eslint'
                });
            }
            // Check for unused variables (basic pattern)
            const unusedVarMatch = line.match(/(?:let|const|var)\s+(\w+)\s*=/);
            if (unusedVarMatch && !this.isVariableUsed(unusedVarMatch[1], lines, index)) {
                diagnostics.push({
                    file: filePath,
                    line: lineNum,
                    column: line.indexOf(unusedVarMatch[1]) + 1,
                    severity: 'warning',
                    message: `'${unusedVarMatch[1]}' is defined but never used`,
                    code: 'no-unused-vars',
                    source: 'eslint'
                });
            }
        });
        return diagnostics;
    }
    /**
     * Analyze Python files
     */
    analyzePython(filePath, lines) {
        const diagnostics = [];
        lines.forEach((line, index) => {
            const lineNum = index + 1;
            const trimmed = line.trim();
            // Check for print statements
            if (trimmed.includes('print(') && !trimmed.startsWith('#')) {
                diagnostics.push({
                    file: filePath,
                    line: lineNum,
                    column: line.indexOf('print(') + 1,
                    severity: 'info',
                    message: 'print statement found - consider using logging',
                    code: 'print-statement',
                    source: 'pylint'
                });
            }
            // Check for line length
            if (line.length > 79) {
                diagnostics.push({
                    file: filePath,
                    line: lineNum,
                    column: 80,
                    severity: 'warning',
                    message: 'Line too long (>79 characters)',
                    code: 'line-too-long',
                    source: 'pylint'
                });
            }
            // Check for missing imports
            if (trimmed.includes('os.') && !this.hasImport(lines, 'import os')) {
                diagnostics.push({
                    file: filePath,
                    line: lineNum,
                    column: line.indexOf('os.') + 1,
                    severity: 'error',
                    message: 'os module used but not imported',
                    code: 'import-error',
                    source: 'pylint'
                });
            }
        });
        return diagnostics;
    }
    /**
     * Analyze PHP files
     */
    analyzePHP(filePath, lines) {
        const diagnostics = [];
        lines.forEach((line, index) => {
            const lineNum = index + 1;
            const trimmed = line.trim();
            // Check for var_dump
            if (trimmed.includes('var_dump(') && !trimmed.startsWith('//')) {
                diagnostics.push({
                    file: filePath,
                    line: lineNum,
                    column: line.indexOf('var_dump(') + 1,
                    severity: 'warning',
                    message: 'var_dump found - remove before production',
                    code: 'no-var-dump',
                    source: 'phpcs'
                });
            }
            // Check for missing semicolons
            if (trimmed.length > 0 &&
                !trimmed.endsWith(';') &&
                !trimmed.endsWith('{') &&
                !trimmed.endsWith('}') &&
                !trimmed.startsWith('//') &&
                !trimmed.startsWith('<?php') &&
                !trimmed.includes('if ') &&
                !trimmed.includes('foreach ') &&
                !trimmed.includes('while ')) {
                diagnostics.push({
                    file: filePath,
                    line: lineNum,
                    column: line.length,
                    severity: 'error',
                    message: 'Missing semicolon',
                    code: 'missing-semicolon',
                    source: 'phpcs'
                });
            }
        });
        return diagnostics;
    }
    /**
     * Analyze JSON files
     */
    analyzeJSON(filePath, content) {
        const diagnostics = [];
        try {
            JSON.parse(content);
        }
        catch (error) {
            const match = error instanceof Error ? error.message.match(/position (\d+)/) : null;
            const position = match ? parseInt(match[1]) : 0;
            const lines = content.substring(0, position).split('\n');
            diagnostics.push({
                file: filePath,
                line: lines.length,
                column: lines[lines.length - 1].length + 1,
                severity: 'error',
                message: error instanceof Error ? error.message : 'JSON parse error',
                code: 'json-parse-error',
                source: 'json'
            });
        }
        return diagnostics;
    }
    /**
     * Analyze YAML files
     */
    analyzeYAML(filePath, content) {
        const diagnostics = [];
        const lines = content.split('\n');
        lines.forEach((line, index) => {
            const lineNum = index + 1;
            // Check for tabs (YAML should use spaces)
            if (line.includes('\t')) {
                diagnostics.push({
                    file: filePath,
                    line: lineNum,
                    column: line.indexOf('\t') + 1,
                    severity: 'error',
                    message: 'YAML files should use spaces, not tabs',
                    code: 'yaml-tabs',
                    source: 'yaml'
                });
            }
        });
        return diagnostics;
    }
    /**
     * Generic file analysis
     */
    analyzeGeneric(filePath, lines) {
        const diagnostics = [];
        lines.forEach((line, index) => {
            const lineNum = index + 1;
            // Check for trailing whitespace
            if (line.endsWith(' ') || line.endsWith('\t')) {
                diagnostics.push({
                    file: filePath,
                    line: lineNum,
                    column: line.length,
                    severity: 'hint',
                    message: 'Trailing whitespace',
                    code: 'trailing-whitespace',
                    source: 'generic'
                });
            }
            // Check for very long lines
            if (line.length > 120) {
                diagnostics.push({
                    file: filePath,
                    line: lineNum,
                    column: 121,
                    severity: 'info',
                    message: 'Line is very long (>120 characters)',
                    code: 'long-line',
                    source: 'generic'
                });
            }
        });
        return diagnostics;
    }
    /**
     * Helper: Check if variable is used
     */
    isVariableUsed(varName, lines, declarationIndex) {
        for (let i = declarationIndex + 1; i < lines.length; i++) {
            if (lines[i].includes(varName)) {
                return true;
            }
        }
        return false;
    }
    /**
     * Helper: Check if import exists
     */
    hasImport(lines, importStatement) {
        return lines.some(line => line.trim().includes(importStatement));
    }
    /**
     * Clear diagnostics for a file
     */
    clearDiagnostics(filePath) {
        this.diagnostics.delete(filePath);
    }
    /**
     * Get all cached diagnostics
     */
    getAllDiagnostics() {
        const result = {};
        for (const [file, diagnostics] of this.diagnostics.entries()) {
            result[file] = diagnostics;
        }
        return result;
    }
}
exports.DiagnosticsProvider = DiagnosticsProvider;
//# sourceMappingURL=diagnostics.js.map