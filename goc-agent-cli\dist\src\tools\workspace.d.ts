/**
 * Workspace Detector Tool
 *
 * Workspace detection, configuration, and project structure analysis
 */
import { ToolResult } from './index';
export interface ProjectType {
    name: string;
    confidence: number;
    indicators: string[];
    configFiles: string[];
    packageManager?: string;
    framework?: string;
    language: string;
}
export interface WorkspaceInfo {
    rootPath: string;
    projectTypes: ProjectType[];
    primaryType: ProjectType;
    structure: DirectoryStructure;
    gitRepository?: GitInfo;
    packageManagers: string[];
    configFiles: string[];
    totalFiles: number;
    totalDirectories: number;
}
export interface DirectoryStructure {
    name: string;
    path: string;
    type: 'file' | 'directory';
    size?: number;
    children?: DirectoryStructure[];
    depth: number;
}
export interface GitInfo {
    isRepository: boolean;
    branch?: string;
    remotes?: string[];
    hasUncommittedChanges?: boolean;
}
export declare class WorkspaceDetector {
    private projectIndicators;
    /**
     * Detect workspace and analyze project structure
     */
    detectWorkspace(projectPath?: string): Promise<ToolResult>;
    /**
     * Get project configuration
     */
    getProjectConfig(projectPath?: string): Promise<ToolResult>;
    /**
     * Detect project types based on files and patterns
     */
    private detectProjectTypes;
    /**
     * Get primary project type (highest confidence)
     */
    private getPrimaryProjectType;
    /**
     * Analyze directory structure
     */
    private analyzeStructure;
    /**
     * Count files and directories in structure
     */
    private countFilesAndDirectories;
    /**
     * Detect Git repository
     */
    private detectGitRepository;
    /**
     * Detect package managers
     */
    private detectPackageManagers;
    /**
     * Find configuration files
     */
    private findConfigFiles;
    /**
     * Get files recursively with depth limit
     */
    private getFilesRecursive;
    /**
     * Basic TOML parser (simplified)
     */
    private parseBasicToml;
}
//# sourceMappingURL=workspace.d.ts.map