{"version": 3, "file": "default-config.js", "sourceRoot": "", "sources": ["../../src/config/default-config.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAIH,SAAgB,gBAAgB;IAC9B,OAAO;QACL,OAAO,EAAE,OAAO;QAChB,EAAE,EAAE;YACF,eAAe,EAAE,QAAQ;YACzB,YAAY,EAAE,aAAa;YAC3B,SAAS,EAAE;gBACT,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,gBAAgB;oBAC7B,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,wBAAwB;oBACjC,MAAM,EAAE,EAAE,EAAE,oEAAoE;oBAChF,YAAY,EAAE,aAAa,EAAE,mBAAmB;oBAChD,SAAS,EAAE,IAAI;oBACf,WAAW,EAAE,GAAG;oBAChB,OAAO,EAAE,KAAK;oBACd,YAAY,EAAE,KAAK;oBACnB,OAAO,EAAE,IAAI;oBACb,iBAAiB,EAAE,mFAAmF;iBACvG;gBACD,GAAG,EAAE;oBACH,IAAI,EAAE,KAAK;oBACX,WAAW,EAAE,iBAAiB;oBAC9B,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE,8BAA8B;oBACvC,MAAM,EAAE;wBACN;4BACE,EAAE,EAAE,iBAAiB;4BACrB,IAAI,EAAE,wBAAwB;4BAC9B,IAAI,EAAE,MAAM;4BACZ,aAAa,EAAE,MAAM;4BACrB,YAAY,EAAE,CAAC,iBAAiB,EAAE,eAAe,EAAE,cAAc,CAAC;4BAClE,WAAW,EAAE,0DAA0D;4BACvE,YAAY,EAAE,IAAI;4BAClB,OAAO,EAAE,KAAK;4BACd,OAAO,EAAE;gCACP,WAAW,EAAE,CAAC;gCACd,QAAQ,EAAE,KAAK;6BAChB;yBACF;wBACD;4BACE,EAAE,EAAE,eAAe;4BACnB,IAAI,EAAE,6BAA6B;4BACnC,IAAI,EAAE,MAAM;4BACZ,aAAa,EAAE,MAAM;4BACrB,YAAY,EAAE,CAAC,iBAAiB,EAAE,eAAe,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,CAAC;4BAC5G,WAAW,EAAE,+EAA+E;4BAC5F,YAAY,EAAE,IAAI;4BAClB,OAAO,EAAE,KAAK;4BACd,OAAO,EAAE;gCACP,WAAW,EAAE,KAAK;gCAClB,QAAQ,EAAE,KAAK;6BAChB;yBACF;qBACF;oBACD,YAAY,EAAE,iBAAiB;oBAC/B,SAAS,EAAE,IAAI;oBACf,WAAW,EAAE,GAAG;oBAChB,OAAO,EAAE,KAAK;oBACd,YAAY,EAAE,IAAI;oBAClB,OAAO,EAAE,KAAK;oBACd,iBAAiB,EAAE,4BAA4B;oBAC/C,iBAAiB,EAAE,kDAAkD;iBACtE;gBACD,uCAAuC;gBACvC,iBAAiB;gBACjB,mBAAmB;gBACnB,kBAAkB;aACnB;YACD,WAAW,EAAE,GAAG;YAChB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,KAAK;SACf;QACD,OAAO,EAAE;YACP,eAAe,EAAE,IAAI;YACrB,oBAAoB,EAAE,IAAI;YAC1B,YAAY,EAAE,IAAI;YAClB,SAAS,EAAE,GAAG;YACd,aAAa,EAAE,QAAQ;SACxB;QACD,KAAK,EAAE;YACL,WAAW,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,OAAO;YACtC,iBAAiB,EAAE;gBACjB,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;gBAC5B,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;gBAC9B,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO;aACxC;YACD,aAAa,EAAE,IAAI;YACnB,eAAe,EAAE,cAAc;YAC/B,QAAQ,EAAE,KAAK;SAChB;QACD,GAAG,EAAE;YACH,OAAO,EAAE,IAAI;YACb,YAAY,EAAE,YAAY;YAC1B,UAAU,EAAE,EAAE;YACd,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,eAAe;YAC1B,aAAa,EAAE,IAAI;SACpB;QACD,QAAQ,EAAE;YACR,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,KAAK;YAClB,mBAAmB,EAAE,GAAG;YACxB,aAAa,EAAE,EAAE;YACjB,iBAAiB,EAAE,IAAI;YACvB,oBAAoB,EAAE,KAAK;YAC3B,gBAAgB,EAAE,eAAe;SAClC;QACD,MAAM,EAAE;YACN,QAAQ,EAAE,MAAM;YAChB,cAAc,EAAE,YAAY;YAC5B,aAAa,EAAE,WAAW;YAC1B,eAAe,EAAE,KAAK;YACtB,UAAU,EAAE,KAAK;SAClB;KACF,CAAC;AACJ,CAAC;AAtHD,4CAsHC;AAED,SAAgB,gBAAgB;IAC9B,OAAO;QACL,EAAE,EAAE;YACF,eAAe,EAAE,QAAQ;YACzB,YAAY,EAAE,aAAa;YAC3B,WAAW,EAAE,GAAG;YAChB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,EAAE;SACd;QACD,MAAM,EAAE;YACN,QAAQ,EAAE,MAAM;YAChB,cAAc,EAAE,YAAY;YAC5B,aAAa,EAAE,WAAW;YAC1B,eAAe,EAAE,KAAK;YACtB,UAAU,EAAE,KAAK;SAClB;KACF,CAAC;AACJ,CAAC;AAlBD,4CAkBC"}