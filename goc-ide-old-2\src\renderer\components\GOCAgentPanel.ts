/**
 * GOC Agent Panel Component - AI chat and assistance
 */

import { EventEmitter } from 'events';

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export class GOCAgentPanel extends EventEmitter {
  private messages: ChatMessage[] = [];
  private isProcessing = false;

  constructor() {
    super();
  }

  public async initialize(): Promise<void> {
    console.log('Initializing GOC Agent Panel...');
    console.log('GOC Agent Panel initialized');
  }

  public render(container: HTMLElement): void {
    container.innerHTML = `
      <div class="panel-header">
        <h3>GOC Agent</h3>
      </div>
      <div class="goc-panel">
        <div class="goc-chat">
          <div class="goc-messages" id="goc-messages">
            ${this.renderMessages()}
          </div>
          <div class="goc-input-container">
            <textarea 
              id="goc-input" 
              class="goc-input" 
              placeholder="Ask GOC Agent anything..."
              rows="2"
            ></textarea>
            <button id="goc-send" class="goc-send" ${this.isProcessing ? 'disabled' : ''}>
              ${this.isProcessing ? '...' : 'Send'}
            </button>
          </div>
        </div>
      </div>
    `;

    this.setupEventHandlers(container);
  }

  private renderMessages(): string {
    if (this.messages.length === 0) {
      return `
        <div class="goc-message assistant">
          <div class="message-content">
            👋 Hello! I'm GOC Agent, your AI coding assistant. How can I help you today?
          </div>
        </div>
      `;
    }

    return this.messages.map(message => `
      <div class="goc-message ${message.role}">
        <div class="message-content">${this.formatMessage(message.content)}</div>
        <div class="message-time">${this.formatTime(message.timestamp)}</div>
      </div>
    `).join('');
  }

  private formatMessage(content: string): string {
    // Basic markdown-like formatting
    return content
      .replace(/```([^`]+)```/g, '<pre><code>$1</code></pre>')
      .replace(/`([^`]+)`/g, '<code>$1</code>')
      .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
      .replace(/\*([^*]+)\*/g, '<em>$1</em>')
      .replace(/\n/g, '<br>');
  }

  private formatTime(timestamp: Date): string {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  private setupEventHandlers(container: HTMLElement): void {
    const input = container.querySelector('#goc-input') as HTMLTextAreaElement;
    const sendButton = container.querySelector('#goc-send') as HTMLButtonElement;

    if (input && sendButton) {
      // Send on button click
      sendButton.addEventListener('click', () => {
        this.sendMessage(input.value.trim());
      });

      // Send on Ctrl+Enter
      input.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && e.ctrlKey) {
          e.preventDefault();
          this.sendMessage(input.value.trim());
        }
      });

      // Auto-resize textarea
      input.addEventListener('input', () => {
        input.style.height = 'auto';
        input.style.height = Math.min(input.scrollHeight, 120) + 'px';
      });
    }
  }

  private async sendMessage(content: string): Promise<void> {
    if (!content || this.isProcessing) return;

    const input = document.getElementById('goc-input') as HTMLTextAreaElement;
    if (input) {
      input.value = '';
      input.style.height = 'auto';
    }

    // Add user message
    const userMessage: ChatMessage = {
      id: this.generateId(),
      role: 'user',
      content,
      timestamp: new Date()
    };

    this.messages.push(userMessage);
    this.updateMessagesDisplay();

    // Set processing state
    this.isProcessing = true;
    this.updateSendButton();

    try {
      // Send to GOC Agent
      const response = await window.electronAPI.goc.chat(content);

      if (response.error) {
        throw new Error(response.error);
      }

      // Add assistant response
      const assistantMessage: ChatMessage = {
        id: this.generateId(),
        role: 'assistant',
        content: response.content || 'Sorry, I encountered an error processing your request.',
        timestamp: new Date()
      };

      this.messages.push(assistantMessage);

    } catch (error) {
      console.error('GOC Agent error:', error);

      // Add error message
      const errorMessage: ChatMessage = {
        id: this.generateId(),
        role: 'assistant',
        content: `Sorry, I encountered an error: ${error.message}`,
        timestamp: new Date()
      };

      this.messages.push(errorMessage);
    } finally {
      this.isProcessing = false;
      this.updateMessagesDisplay();
      this.updateSendButton();
    }
  }

  private updateMessagesDisplay(): void {
    const messagesContainer = document.getElementById('goc-messages');
    if (messagesContainer) {
      messagesContainer.innerHTML = this.renderMessages();
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
  }

  private updateSendButton(): void {
    const sendButton = document.getElementById('goc-send') as HTMLButtonElement;
    if (sendButton) {
      sendButton.disabled = this.isProcessing;
      sendButton.textContent = this.isProcessing ? '...' : 'Send';
    }
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  public addMessage(role: 'user' | 'assistant', content: string): void {
    const message: ChatMessage = {
      id: this.generateId(),
      role,
      content,
      timestamp: new Date()
    };

    this.messages.push(message);
    this.updateMessagesDisplay();
  }

  public clearMessages(): void {
    this.messages = [];
    this.updateMessagesDisplay();
  }

  public getMessages(): ChatMessage[] {
    return [...this.messages];
  }
}
