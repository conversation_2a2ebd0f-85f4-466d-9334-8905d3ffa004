{"name": "goc-ide", "productName": "GOC IDE", "version": "1.0.0", "description": "Professional AI-powered code editor with GOC Agent integration", "main": "dist/main.js", "author": "GOC Agent Team", "license": "MIT", "homepage": "https://goc-agent.com", "repository": {"type": "git", "url": "https://github.com/goc-agent/goc-ide.git"}, "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "concurrently \"npm run build:watch\" \"npm run start:dev\"", "build": "npm run build:main && npm run build:renderer", "build:main": "tsc -p tsconfig.main.json", "build:renderer": "webpack --mode production", "build:watch": "concurrently \"npm run build:main:watch\" \"npm run build:renderer:watch\"", "build:main:watch": "tsc -p tsconfig.main.json --watch", "build:renderer:watch": "webpack --mode development --watch", "start": "electron .", "start:dev": "cross-env NODE_ENV=development electron . --inspect=5858", "package": "npm run build && electron-builder", "package:win": "npm run build && electron-builder --win", "package:mac": "npm run build && electron-builder --mac", "package:linux": "npm run build && electron-builder --linux", "dist": "npm run build && electron-builder --publish=never", "dist:win": "npm run build && electron-builder --win --publish=never", "dist:mac": "npm run build && electron-builder --mac --publish=never", "dist:linux": "npm run build && electron-builder --linux --publish=never", "publish": "npm run build && electron-builder --publish=always", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "clean": "<PERSON><PERSON><PERSON> dist build", "install:deps": "npm install --ignore-scripts", "install:electron": "npm install electron --ignore-scripts", "setup": "npm run install:deps && npm run install:electron"}, "build": {"appId": "com.goc-agent.ide", "productName": "GOC IDE", "directories": {"output": "build"}, "files": ["dist/**/*", "assets/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "assets", "to": "assets"}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "publisherName": "GOC Agent"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.developer-tools"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Development"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}, "publish": {"provider": "github", "owner": "goc-agent", "repo": "goc-ide"}}, "devDependencies": {"@types/node": "^20.10.0", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "electron": "^27.1.3", "electron-builder": "^24.6.4", "eslint": "^8.54.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.3", "jest": "^29.7.0", "mini-css-extract-plugin": "^2.7.6", "rimraf": "^5.0.5", "style-loader": "^3.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "typescript": "^5.3.2", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}, "dependencies": {"@goc-agent/core": "file:../goc-core", "electron-updater": "^6.1.7", "electron-log": "^5.0.1", "monaco-editor": "^0.44.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "node-pty": "^1.0.0", "chokidar": "^3.5.3", "simple-git": "^3.20.0", "fuse.js": "^7.0.0"}}