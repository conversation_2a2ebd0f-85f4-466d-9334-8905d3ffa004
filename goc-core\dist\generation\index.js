"use strict";
/**
 * Code Generation System
 *
 * Intelligent code generation with pattern recognition and best practices
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeGenerator = void 0;
const utils_1 = require("../utils");
/**
 * Intelligent Code Generator
 */
class CodeGenerator {
    constructor(contextEngine) {
        this.templates = new Map();
        this.patterns = new Map();
        this.contextEngine = contextEngine;
        this.initializeTemplates();
        this.initializePatterns();
    }
    /**
     * Generate code based on description and context
     */
    async generateCode(request) {
        try {
            utils_1.logger.info('Starting code generation', {
                type: request.type,
                language: request.language
            });
            // Analyze context and requirements
            const analysis = await this.analyzeRequirements(request);
            // Select appropriate template or pattern
            const template = await this.selectTemplate(request, analysis);
            // Generate code using template and context
            const code = await this.generateFromTemplate(template, request, analysis);
            // Generate tests if requested
            const tests = request.options?.includeTests ?
                await this.generateTests(code, request) : undefined;
            // Generate documentation if requested
            const documentation = request.options?.includeDocumentation ?
                await this.generateDocumentation(code, request) : undefined;
            // Analyze code quality
            const quality = await this.analyzeCodeQuality(code, request.language);
            // Extract patterns and suggestions
            const patterns = this.extractPatterns(code, request.language);
            const suggestions = await this.generateSuggestions(code, request, analysis);
            const result = {
                code,
                explanation: this.generateExplanation(code, request, template),
                suggestions,
                tests,
                documentation,
                dependencies: analysis.dependencies,
                patterns,
                quality
            };
            utils_1.logger.info('Code generation completed', {
                codeLength: code.length,
                qualityScore: quality.score
            });
            return result;
        }
        catch (error) {
            utils_1.logger.error('Code generation failed', error);
            throw error;
        }
    }
    /**
     * Refactor existing code with improvements
     */
    async refactorCode(code, options) {
        try {
            utils_1.logger.info('Starting code refactoring', { language: options.language });
            // Analyze existing code
            const analysis = await this.contextEngine.analyzeCode(code, {
                language: options.language,
                includeMetrics: true,
                includeSuggestions: true
            });
            // Apply refactoring patterns
            const refactoredCode = await this.applyRefactoringPatterns(code, options, analysis);
            // Generate quality comparison
            const newQuality = await this.analyzeCodeQuality(refactoredCode, options.language);
            const improvements = this.compareQuality(analysis.metrics, newQuality);
            return {
                code: refactoredCode,
                explanation: 'Code refactored with modern patterns and best practices',
                suggestions: improvements,
                dependencies: [],
                patterns: this.extractPatterns(refactoredCode, options.language),
                quality: newQuality
            };
        }
        catch (error) {
            utils_1.logger.error('Code refactoring failed', error);
            throw error;
        }
    }
    /**
     * Generate code from natural language description
     */
    async generateFromDescription(description, language, context) {
        // Parse description to understand intent
        const intent = this.parseIntent(description);
        const request = {
            type: intent.type,
            language,
            description,
            context,
            options: {
                style: 'modern',
                includeTests: true,
                includeDocumentation: true,
                followPatterns: true
            }
        };
        return await this.generateCode(request);
    }
    /**
     * Get available templates for a language
     */
    getTemplates(language) {
        const templates = Array.from(this.templates.values());
        return language ? templates.filter(t => t.language === language) : templates;
    }
    /**
     * Add custom template
     */
    addTemplate(template) {
        this.templates.set(template.id, template);
        utils_1.logger.info('Template added', { id: template.id, language: template.language });
    }
    // Private methods
    async analyzeRequirements(request) {
        const analysis = {
            complexity: 1,
            dependencies: request.context?.dependencies || [],
            patterns: [],
            constraints: []
        };
        // Analyze description for complexity indicators
        const description = request.description.toLowerCase();
        if (description.includes('complex') || description.includes('advanced')) {
            analysis.complexity = 3;
        }
        else if (description.includes('simple') || description.includes('basic')) {
            analysis.complexity = 1;
        }
        else {
            analysis.complexity = 2;
        }
        // Extract patterns from description
        if (description.includes('singleton'))
            analysis.patterns.push('singleton');
        if (description.includes('factory'))
            analysis.patterns.push('factory');
        if (description.includes('observer'))
            analysis.patterns.push('observer');
        if (description.includes('mvc'))
            analysis.patterns.push('mvc');
        // Analyze existing code context if provided
        if (request.context?.existingCode) {
            const contextAnalysis = await this.contextEngine.analyzeCode(request.context.existingCode, {
                language: request.language,
                includeMetrics: true
            });
            analysis.dependencies.push(...contextAnalysis.dependencies);
        }
        return analysis;
    }
    async selectTemplate(request, analysis) {
        // Find templates matching the request
        const candidates = Array.from(this.templates.values()).filter(template => template.language === request.language &&
            template.category === request.type);
        if (candidates.length === 0) {
            // Create a basic template if none found
            return this.createBasicTemplate(request);
        }
        // Select best template based on complexity and patterns
        return candidates.reduce((best, current) => {
            const score = this.scoreTemplate(current, request, analysis);
            const bestScore = this.scoreTemplate(best, request, analysis);
            return score > bestScore ? current : best;
        });
    }
    scoreTemplate(template, request, analysis) {
        let score = 0;
        // Match category
        if (template.category === request.type)
            score += 10;
        // Match language
        if (template.language === request.language)
            score += 10;
        // Match patterns
        analysis.patterns.forEach((pattern) => {
            if (template.description.toLowerCase().includes(pattern)) {
                score += 5;
            }
        });
        return score;
    }
    async generateFromTemplate(template, request, analysis) {
        let code = template.template;
        // Replace template variables
        const variables = this.extractVariables(request, analysis);
        template.variables.forEach(variable => {
            const value = variables[variable.name] || variable.default || '';
            const placeholder = `{{${variable.name}}}`;
            code = code.replace(new RegExp(placeholder, 'g'), value);
        });
        // Apply language-specific formatting
        code = this.formatCode(code, request.language);
        return code;
    }
    extractVariables(request, analysis) {
        const variables = {};
        // Extract from description
        const description = request.description;
        // Extract class/function name
        const nameMatch = description.match(/(?:class|function|component)\s+(\w+)/i);
        if (nameMatch) {
            variables.name = nameMatch[1];
        }
        else {
            // Generate name from description
            variables.name = this.generateNameFromDescription(description);
        }
        // Extract parameters
        const paramMatch = description.match(/with\s+parameters?\s+([^.]+)/i);
        if (paramMatch) {
            variables.parameters = paramMatch[1];
        }
        // Extract return type
        const returnMatch = description.match(/returns?\s+([^.]+)/i);
        if (returnMatch) {
            variables.returnType = returnMatch[1];
        }
        return variables;
    }
    generateNameFromDescription(description) {
        // Simple name generation from description
        const words = description.split(' ')
            .filter(word => word.length > 2)
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase());
        return words.slice(0, 3).join('');
    }
    formatCode(code, language) {
        // Basic code formatting - in production, this would use proper formatters
        switch (language) {
            case 'javascript':
            case 'typescript':
                return this.formatJavaScript(code);
            case 'python':
                return this.formatPython(code);
            case 'php':
                return this.formatPHP(code);
            default:
                return code;
        }
    }
    formatJavaScript(code) {
        // Basic JavaScript formatting
        return code
            .replace(/\s*{\s*/g, ' {\n  ')
            .replace(/;\s*/g, ';\n  ')
            .replace(/\s*}\s*/g, '\n}');
    }
    formatPython(code) {
        // Basic Python formatting
        return code
            .replace(/:\s*/g, ':\n    ')
            .replace(/\n\s*\n/g, '\n');
    }
    formatPHP(code) {
        // Basic PHP formatting
        return code
            .replace(/\s*{\s*/g, ' {\n    ')
            .replace(/;\s*/g, ';\n    ')
            .replace(/\s*}\s*/g, '\n}');
    }
    async generateTests(code, request) {
        // Generate basic test structure
        const testTemplate = this.getTestTemplate(request.language);
        const functionName = this.extractFunctionName(code);
        return testTemplate
            .replace('{{functionName}}', functionName)
            .replace('{{testCases}}', this.generateTestCases(code, request));
    }
    getTestTemplate(language) {
        const templates = {
            javascript: `
describe('{{functionName}}', () => {
  {{testCases}}
});`,
            python: `
import unittest

class Test{{functionName}}(unittest.TestCase):
    {{testCases}}

if __name__ == '__main__':
    unittest.main()`,
            php: `
<?php
use PHPUnit\\Framework\\TestCase;

class {{functionName}}Test extends TestCase {
    {{testCases}}
}`
        };
        return templates[language] || '// Tests not implemented for this language';
    }
    extractFunctionName(code) {
        // Extract function name from code
        const match = code.match(/(?:function|def|public function)\s+(\w+)/);
        return match ? match[1] : 'GeneratedFunction';
    }
    generateTestCases(code, request) {
        // Generate basic test cases
        return `
  test('should work correctly', () => {
    // TODO: Implement test
    expect(true).toBe(true);
  });

  test('should handle edge cases', () => {
    // TODO: Implement edge case tests
    expect(true).toBe(true);
  });`;
    }
    async generateDocumentation(code, request) {
        const functionName = this.extractFunctionName(code);
        return `
/**
 * ${request.description}
 * 
 * @description ${request.description}
 * @param {*} params - Function parameters
 * @returns {*} Function result
 * @example
 * // Usage example
 * const result = ${functionName}();
 */`;
    }
    async analyzeCodeQuality(code, language) {
        const analysis = await this.contextEngine.analyzeCode(code, {
            language,
            includeMetrics: true,
            includeSuggestions: true
        });
        return {
            score: analysis.metrics?.maintainabilityIndex || 85,
            issues: analysis.issues.map(issue => issue.message),
            improvements: analysis.suggestions.map(suggestion => suggestion.message)
        };
    }
    extractPatterns(code, language) {
        const patterns = [];
        // Detect common patterns
        if (code.includes('class ') && code.includes('extends ')) {
            patterns.push('inheritance');
        }
        if (code.includes('interface ') || code.includes('implements ')) {
            patterns.push('interface');
        }
        if (code.includes('async ') || code.includes('await ')) {
            patterns.push('async');
        }
        if (code.includes('try ') && code.includes('catch ')) {
            patterns.push('error-handling');
        }
        return patterns;
    }
    async generateSuggestions(code, request, analysis) {
        const suggestions = [];
        // Add suggestions based on analysis
        if (analysis.complexity > 2) {
            suggestions.push('Consider breaking down complex logic into smaller functions');
        }
        if (!code.includes('try') && request.type === 'function') {
            suggestions.push('Consider adding error handling');
        }
        if (!code.includes('/**') && request.options?.includeDocumentation) {
            suggestions.push('Add comprehensive documentation');
        }
        return suggestions;
    }
    generateExplanation(code, request, template) {
        return `Generated ${request.type} in ${request.language} based on: "${request.description}". 
The code follows ${template.name} pattern and includes modern best practices.`;
    }
    parseIntent(description) {
        const desc = description.toLowerCase();
        if (desc.includes('function') || desc.includes('method')) {
            return { type: 'function' };
        }
        else if (desc.includes('class')) {
            return { type: 'class' };
        }
        else if (desc.includes('component')) {
            return { type: 'component' };
        }
        else if (desc.includes('test')) {
            return { type: 'test' };
        }
        else {
            return { type: 'function' }; // Default
        }
    }
    createBasicTemplate(request) {
        const templates = {
            function: {
                javascript: 'function {{name}}({{parameters}}) {\n  // TODO: Implement\n  return {{returnType}};\n}',
                python: 'def {{name}}({{parameters}}):\n    """{{description}}"""\n    # TODO: Implement\n    pass',
                php: 'public function {{name}}({{parameters}}) {\n    // TODO: Implement\n    return {{returnType}};\n}'
            },
            class: {
                javascript: 'class {{name}} {\n  constructor({{parameters}}) {\n    // TODO: Implement\n  }\n}',
                python: 'class {{name}}:\n    """{{description}}"""\n    \n    def __init__(self, {{parameters}}):\n        # TODO: Implement\n        pass',
                php: 'class {{name}} {\n    public function __construct({{parameters}}) {\n        // TODO: Implement\n    }\n}'
            }
        };
        const template = templates[request.type]?.[request.language] ||
            '// Template not available for this language';
        return {
            id: `basic_${request.type}_${request.language}`,
            name: `Basic ${request.type}`,
            description: `Basic ${request.type} template for ${request.language}`,
            language: request.language,
            category: request.type,
            template,
            variables: [
                { name: 'name', type: 'string', description: 'Name', required: true },
                { name: 'parameters', type: 'string', description: 'Parameters', required: false, default: '' },
                { name: 'returnType', type: 'string', description: 'Return type', required: false, default: 'void' },
                { name: 'description', type: 'string', description: 'Description', required: false, default: '' }
            ],
            dependencies: [],
            examples: []
        };
    }
    async applyRefactoringPatterns(code, options, analysis) {
        let refactoredCode = code;
        // Apply modernization patterns
        if (options.modernize) {
            refactoredCode = this.modernizeCode(refactoredCode, options.language);
        }
        // Apply quality improvements
        if (analysis.metrics?.complexity > 10) {
            refactoredCode = this.reduceComplexity(refactoredCode, options.language);
        }
        return refactoredCode;
    }
    modernizeCode(code, language) {
        // Apply modern patterns based on language
        switch (language) {
            case 'javascript':
                return code
                    .replace(/var\s+/g, 'const ')
                    .replace(/function\s*\(/g, '() => {')
                    .replace(/\.then\(/g, 'await ');
            case 'python':
                return code
                    .replace(/print\s+/g, 'print(')
                    .replace(/%\s*\(/g, '.format(');
            default:
                return code;
        }
    }
    reduceComplexity(code, language) {
        // Basic complexity reduction - in production, this would be more sophisticated
        return code + '\n// TODO: Consider breaking down complex logic into smaller functions';
    }
    compareQuality(oldMetrics, newQuality) {
        const improvements = [];
        if (newQuality.score > (oldMetrics?.maintainabilityIndex || 0)) {
            improvements.push('Improved maintainability index');
        }
        improvements.push('Applied modern coding patterns');
        improvements.push('Enhanced code readability');
        return improvements;
    }
    initializeTemplates() {
        // Initialize with basic templates - in production, these would be loaded from files
        utils_1.logger.info('Initializing code templates');
    }
    initializePatterns() {
        // Initialize with common patterns - in production, these would be loaded from files
        utils_1.logger.info('Initializing code patterns');
    }
}
exports.CodeGenerator = CodeGenerator;
//# sourceMappingURL=index.js.map