"use strict";
/**
 * Model Selection Service
 *
 * Unified service for handling model selection across all GOC Agent products
 * Manages both Ollama (local) and GOC Agent (hosted) models
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelSelectionService = void 0;
const default_config_1 = require("../config/default-config");
class ModelSelectionService {
    constructor() {
        this.ollamaAvailable = false;
        this.config = (0, default_config_1.getDefaultConfig)();
    }
    /**
     * Get available models and providers for selection
     */
    async getModelSelection(options = {}) {
        const { includeLocal = true, includeHosted = true, requiresAuth = false, userTier = 'free' } = options;
        const providers = [];
        // Add Ollama provider if requested
        if (includeLocal) {
            const ollamaProvider = await this.getOllamaProvider();
            providers.push(ollamaProvider);
        }
        // Add GOC Agent provider if requested
        if (includeHosted) {
            const gocProvider = await this.getGocProvider(userTier);
            providers.push(gocProvider);
        }
        const recommendations = {
            local: 'llama3.2:3b',
            cloudFree: 'goc-agent-cloud',
            cloudPaid: 'goc-agent-dev'
        };
        return {
            providers,
            recommendations
        };
    }
    /**
     * Get Ollama provider information
     */
    async getOllamaProvider() {
        const ollamaConfig = this.config.ai.providers.ollama;
        const isAvailable = await this.checkOllamaAvailability();
        let models = [];
        if (isAvailable) {
            // Get installed models from Ollama
            models = await this.getInstalledOllamaModels();
        }
        else {
            // Show suggested models for installation
            models = ollamaConfig.suggestedModels.map((model) => ({
                id: model.id,
                name: model.name,
                tier: 'free',
                description: model.description,
                capabilities: model.capabilities,
                contextLength: model.contextLength,
                isAvailable: false,
                requiresAuth: false,
                isLocal: true,
                size: model.size,
                installCommand: model.installCommand
            }));
        }
        return {
            name: 'ollama',
            displayName: ollamaConfig.displayName,
            tier: 'free',
            isLocal: true,
            requiresAuth: false,
            isAvailable,
            models,
            setupInstructions: ollamaConfig.setupInstructions
        };
    }
    /**
     * Get GOC Agent provider information
     */
    async getGocProvider(userTier) {
        const gocConfig = this.config.ai.providers.goc;
        const models = gocConfig.models.map((model) => ({
            id: model.id,
            name: model.name,
            tier: model.tier,
            description: model.description,
            capabilities: model.capabilities,
            contextLength: model.contextLength,
            isAvailable: true,
            requiresAuth: true,
            isLocal: false,
            pricing: model.pricing,
            limits: model.limits
        }));
        return {
            name: 'goc',
            displayName: gocConfig.displayName,
            tier: 'freemium',
            isLocal: false,
            requiresAuth: true,
            isAvailable: true,
            models,
            setupInstructions: gocConfig.setupInstructions,
            authenticationUrl: gocConfig.authenticationUrl
        };
    }
    /**
     * Check if Ollama is available
     */
    async checkOllamaAvailability() {
        try {
            const response = await fetch('http://localhost:11434/api/tags', {
                method: 'GET',
                timeout: 5000
            });
            this.ollamaAvailable = response.ok;
            return response.ok;
        }
        catch (error) {
            this.ollamaAvailable = false;
            return false;
        }
    }
    /**
     * Get installed Ollama models
     */
    async getInstalledOllamaModels() {
        try {
            const response = await fetch('http://localhost:11434/api/tags');
            if (!response.ok)
                return [];
            const data = await response.json();
            const installedModels = data.models || [];
            // Map installed models to our format
            return installedModels.map((model) => ({
                id: model.name,
                name: this.formatModelName(model.name),
                tier: 'free',
                description: this.getModelDescription(model.name),
                capabilities: this.getModelCapabilities(model.name),
                contextLength: this.getModelContextLength(model.name),
                isAvailable: true,
                requiresAuth: false,
                isLocal: true,
                size: this.formatSize(model.size)
            }));
        }
        catch (error) {
            return [];
        }
    }
    /**
     * Format model name for display
     */
    formatModelName(modelId) {
        const nameMap = {
            'llama3.2:3b': 'Llama 3.2 3B',
            'llama3.2:1b': 'Llama 3.2 1B',
            'codellama:7b': 'Code Llama 7B',
            'codellama:13b': 'Code Llama 13B',
            'mistral:7b': 'Mistral 7B',
            'deepseek-coder:6.7b': 'DeepSeek Coder 6.7B',
            'qwen2.5-coder:7b': 'Qwen2.5 Coder 7B'
        };
        return nameMap[modelId] || modelId;
    }
    /**
     * Get model description
     */
    getModelDescription(modelId) {
        const descriptionMap = {
            'llama3.2:3b': 'Fast and efficient for most coding tasks',
            'llama3.2:1b': 'Lightweight model for basic tasks',
            'codellama:7b': 'Specialized for code generation',
            'codellama:13b': 'Larger model for complex coding tasks',
            'mistral:7b': 'General purpose model',
            'deepseek-coder:6.7b': 'Optimized for coding tasks',
            'qwen2.5-coder:7b': 'Latest coding model'
        };
        return descriptionMap[modelId] || 'Local AI model';
    }
    /**
     * Get model capabilities
     */
    getModelCapabilities(modelId) {
        if (modelId.includes('code')) {
            return ['code-generation', 'code-analysis', 'code-review'];
        }
        return ['code-generation', 'general-chat'];
    }
    /**
     * Get model context length
     */
    getModelContextLength(modelId) {
        if (modelId.includes('codellama'))
            return 16384;
        return 32768;
    }
    /**
     * Format file size
     */
    formatSize(bytes) {
        const gb = bytes / (1024 * 1024 * 1024);
        return `${gb.toFixed(1)}GB`;
    }
    /**
     * Validate model selection
     */
    validateModelSelection(providerId, modelId) {
        const provider = this.config.ai.providers[providerId];
        if (!provider) {
            return {
                success: false,
                error: `Provider ${providerId} not found`,
                timestamp: new Date()
            };
        }
        return {
            success: true,
            timestamp: new Date()
        };
    }
}
exports.ModelSelectionService = ModelSelectionService;
//# sourceMappingURL=model-selection-service.js.map