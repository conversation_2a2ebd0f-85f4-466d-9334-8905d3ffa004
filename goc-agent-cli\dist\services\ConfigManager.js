"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigManager = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const yaml = __importStar(require("yaml"));
class ConfigManager {
    constructor(configPath) {
        this.config = {};
        this.configPath = configPath || this.getDefaultConfigPath();
    }
    getDefaultConfigPath() {
        const homeDir = process.env.HOME || process.env.USERPROFILE || '';
        return path.join(homeDir, '.goc-agent', 'config.yaml');
    }
    async loadConfig() {
        try {
            if (await fs.pathExists(this.configPath)) {
                const content = await fs.readFile(this.configPath, 'utf8');
                this.config = yaml.parse(content) || {};
            }
        }
        catch (error) {
            console.warn(`Failed to load config from ${this.configPath}:`, error);
        }
        return this.config;
    }
    async saveConfig(config) {
        try {
            await fs.ensureDir(path.dirname(this.configPath));
            const content = yaml.stringify(config);
            await fs.writeFile(this.configPath, content, 'utf8');
            this.config = config;
        }
        catch (error) {
            throw new Error(`Failed to save config to ${this.configPath}: ${error}`);
        }
    }
    getConfig() {
        return { ...this.config };
    }
    updateConfig(updates) {
        this.config = { ...this.config, ...updates };
    }
    getApiKey(provider) {
        return this.config.apiKeys?.[provider];
    }
    setApiKey(provider, apiKey) {
        if (!this.config.apiKeys) {
            this.config.apiKeys = {};
        }
        this.config.apiKeys[provider] = apiKey;
    }
    getBackendConfig() {
        return this.config.backend;
    }
    setBackendConfig(url, apiKey) {
        this.config.backend = { url, apiKey };
    }
    getDefaultProvider() {
        return this.config.defaultProvider;
    }
    setDefaultProvider(provider) {
        this.config.defaultProvider = provider;
    }
    getDefaultModel() {
        return this.config.defaultModel;
    }
    setDefaultModel(model) {
        this.config.defaultModel = model;
    }
    isVerbose() {
        return this.config.preferences?.verbose || false;
    }
    setVerbose(verbose) {
        if (!this.config.preferences) {
            this.config.preferences = {};
        }
        this.config.preferences.verbose = verbose;
    }
}
exports.ConfigManager = ConfigManager;
//# sourceMappingURL=ConfigManager.js.map