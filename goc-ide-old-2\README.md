# GOC IDE

Professional AI-powered code editor with GOC Agent integration.

## Features

- **Monaco Editor** - VS Code's editor engine with syntax highlighting and IntelliSense
- **GOC Agent Integration** - Built-in AI assistance for coding tasks
- **Multi-Provider Support** - Works with Ollama, OpenAI, Groq, Gemini, and Claude
- **File Explorer** - Browse and manage your project files
- **Integrated Terminal** - Built-in terminal for command execution
- **Command Palette** - Quick access to all IDE features
- **Professional UI** - Clean, modern interface inspired by VS Code
- **Cross-Platform** - Windows, macOS, and Linux support

## Quick Start

### Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Package for distribution
npm run package
```

### Production Build

```bash
# Build and package for current platform
npm run dist

# Build for specific platforms
npm run dist:win    # Windows
npm run dist:mac    # macOS
npm run dist:linux  # Linux
```

## Architecture

- **Main Process** - Electron main process handling system integration
- **Renderer Process** - UI layer with Monaco Editor and React components
- **GOC Core** - AI engine providing intelligent code assistance
- **IPC Communication** - Secure communication between processes

## Configuration

The IDE uses GOC Core for AI functionality. Configure your AI providers in the settings panel or through the GOC Core configuration.

## Development

### Project Structure

```
src/
├── main/           # Electron main process
├── renderer/       # UI and editor logic
├── shared/         # Shared utilities
└── assets/         # Static assets
```

### Building

The project uses TypeScript and Webpack for building:

- Main process: TypeScript → JavaScript
- Renderer process: TypeScript + Webpack → Bundled JavaScript
- Assets: Copied to dist folder

### Testing

```bash
npm test
```

## Distribution

The IDE can be distributed as:

- **Installers** - NSIS (Windows), DMG (macOS), DEB/AppImage (Linux)
- **Portable** - Standalone executables
- **Auto-updater** - Automatic updates via GitHub releases

## License

MIT License - see LICENSE file for details.
