/**
 * Documentation Generation System
 *
 * Intelligent documentation generation with code analysis and best practices
 */
import { ContextEngine } from '../context';
export interface DocumentationOptions {
    format: 'markdown' | 'html' | 'pdf' | 'json';
    includeExamples: boolean;
    includeAPI: boolean;
    includeArchitecture: boolean;
    includeDeployment: boolean;
    includeTesting: boolean;
    language: string;
    outputPath?: string;
    template?: string;
}
export interface DocumentationSection {
    title: string;
    content: string;
    level: number;
    type: 'overview' | 'api' | 'example' | 'architecture' | 'deployment' | 'testing' | 'custom';
    metadata?: Record<string, any>;
}
export interface APIDocumentation {
    functions: Array<{
        name: string;
        description: string;
        parameters: Array<{
            name: string;
            type: string;
            description: string;
            required: boolean;
            default?: any;
        }>;
        returns: {
            type: string;
            description: string;
        };
        examples: string[];
        throws?: string[];
    }>;
    classes: Array<{
        name: string;
        description: string;
        constructor: {
            parameters: Array<{
                name: string;
                type: string;
                description: string;
                required: boolean;
            }>;
        };
        methods: Array<{
            name: string;
            description: string;
            parameters: Array<{
                name: string;
                type: string;
                description: string;
                required: boolean;
            }>;
            returns: {
                type: string;
                description: string;
            };
            visibility: 'public' | 'private' | 'protected';
        }>;
        properties: Array<{
            name: string;
            type: string;
            description: string;
            visibility: 'public' | 'private' | 'protected';
        }>;
    }>;
    interfaces: Array<{
        name: string;
        description: string;
        properties: Array<{
            name: string;
            type: string;
            description: string;
            required: boolean;
        }>;
    }>;
}
export interface ProjectDocumentation {
    title: string;
    description: string;
    version: string;
    sections: DocumentationSection[];
    api?: APIDocumentation;
    architecture?: {
        overview: string;
        components: Array<{
            name: string;
            description: string;
            dependencies: string[];
            type: 'service' | 'library' | 'component' | 'module';
        }>;
        patterns: string[];
    };
    deployment?: {
        requirements: string[];
        steps: string[];
        configuration: Record<string, any>;
        environments: string[];
    };
    testing?: {
        framework: string;
        coverage: number;
        testTypes: string[];
        runInstructions: string[];
    };
}
/**
 * Intelligent Documentation Generator
 */
export declare class DocumentationGenerator {
    private contextEngine;
    constructor(contextEngine: ContextEngine);
    /**
     * Generate comprehensive project documentation
     */
    generateProjectDocumentation(projectPath: string, options: DocumentationOptions): Promise<ProjectDocumentation>;
    /**
     * Generate API documentation from code
     */
    generateAPIDocumentation(projectPath: string, language: string): Promise<APIDocumentation>;
    /**
     * Generate code examples
     */
    generateCodeExamples(projectPath: string, language: string): Promise<Array<{
        title: string;
        description: string;
        code: string;
        language: string;
    }>>;
    /**
     * Export documentation in specified format
     */
    exportDocumentation(documentation: ProjectDocumentation, format: 'markdown' | 'html' | 'pdf' | 'json', outputPath?: string): Promise<string>;
    private analyzeProject;
    private scanProjectFiles;
    private generateOverviewSection;
    private generateExamplesSection;
    private generateArchitectureDocumentation;
    private generateDeploymentDocumentation;
    private generateTestingDocumentation;
    private formatAPIDocumentation;
    private formatArchitectureDocumentation;
    private formatDeploymentDocumentation;
    private formatTestingDocumentation;
    private exportToMarkdown;
    private exportToHTML;
    private exportToPDF;
    private getInstallCommand;
    private getTestFramework;
    private getTestCommand;
    private generateQuickStartGuide;
    private generateBasicUsageExample;
    private generateAdvancedUsageExample;
}
//# sourceMappingURL=index.d.ts.map