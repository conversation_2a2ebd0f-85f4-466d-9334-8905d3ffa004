/**
 * Editor Manager - <PERSON>les Monaco Editor instances and file management
 */

import * as monaco from 'monaco-editor';
import { EventEmitter } from 'events';

interface EditorTab {
  id: string;
  filePath: string;
  fileName: string;
  isDirty: boolean;
  editor: monaco.editor.IStandaloneCodeEditor;
  model: monaco.editor.ITextModel;
}

export class EditorManager extends EventEmitter {
  private editors: Map<string, EditorTab> = new Map();
  private activeEditorId: string | null = null;
  private editorContainer: HTMLElement | null = null;
  private tabBar: HTMLElement | null = null;
  private nextTabId = 1;

  constructor() {
    super();
  }

  public async initialize(): Promise<void> {
    console.log('Initializing Editor Manager...');

    // Get DOM elements
    this.editorContainer = document.getElementById('editor-container');
    this.tabBar = document.getElementById('tab-bar');

    if (!this.editorContainer || !this.tabBar) {
      throw new Error('Editor container or tab bar not found');
    }

    // Configure Monaco Editor
    this.configureMonaco();

    console.log('Editor Manager initialized');
  }

  private configureMonaco(): void {
    // Configure Monaco Editor worker
    (self as any).MonacoEnvironment = {
      getWorkerUrl: function (moduleId: string, label: string) {
        if (label === 'json') {
          return './monaco-editor/esm/vs/language/json/json.worker.js';
        }
        if (label === 'css' || label === 'scss' || label === 'less') {
          return './monaco-editor/esm/vs/language/css/css.worker.js';
        }
        if (label === 'html' || label === 'handlebars' || label === 'razor') {
          return './monaco-editor/esm/vs/language/html/html.worker.js';
        }
        if (label === 'typescript' || label === 'javascript') {
          return './monaco-editor/esm/vs/language/typescript/ts.worker.js';
        }
        return './monaco-editor/esm/vs/editor/editor.worker.js';
      }
    };

    // Configure Monaco themes
    monaco.editor.defineTheme('goc-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6A9955' },
        { token: 'keyword', foreground: '569CD6' },
        { token: 'string', foreground: 'CE9178' },
        { token: 'number', foreground: 'B5CEA8' }
      ],
      colors: {
        'editor.background': '#1e1e1e',
        'editor.foreground': '#d4d4d4',
        'editorLineNumber.foreground': '#858585',
        'editor.selectionBackground': '#264f78',
        'editor.inactiveSelectionBackground': '#3a3d41'
      }
    });

    monaco.editor.setTheme('goc-dark');
  }

  public async openFile(filePath: string): Promise<void> {
    try {
      // Check if file is already open
      const existingTab = Array.from(this.editors.values())
        .find(tab => tab.filePath === filePath);

      if (existingTab) {
        this.switchToTab(existingTab.id);
        return;
      }

      // Read file content
      const content = await window.electronAPI.fs.readFile(filePath);
      
      // Create new tab
      await this.createTab(filePath, content);

    } catch (error) {
      console.error('Failed to open file:', error);
      throw error;
    }
  }

  public async createNewFile(): Promise<void> {
    const fileName = `Untitled-${this.nextTabId}`;
    const filePath = `untitled:${fileName}`;
    
    await this.createTab(filePath, '', fileName);
  }

  private async createTab(filePath: string, content: string, customFileName?: string): Promise<void> {
    const tabId = `tab-${this.nextTabId++}`;
    const fileName = customFileName || this.getFileName(filePath);
    const language = this.getLanguageFromPath(filePath);

    // Create Monaco model
    const model = monaco.editor.createModel(content, language);

    // Create editor container
    const editorDiv = document.createElement('div');
    editorDiv.id = `editor-${tabId}`;
    editorDiv.className = 'monaco-editor-container';
    editorDiv.style.display = 'none';
    this.editorContainer!.appendChild(editorDiv);

    // Create Monaco editor
    const editor = monaco.editor.create(editorDiv, {
      model: model,
      theme: 'goc-dark',
      automaticLayout: true,
      fontSize: 14,
      lineNumbers: 'on',
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      tabSize: 2,
      insertSpaces: true
    });

    // Create tab object
    const tab: EditorTab = {
      id: tabId,
      filePath,
      fileName,
      isDirty: false,
      editor,
      model
    };

    // Store tab
    this.editors.set(tabId, tab);

    // Setup editor events
    this.setupEditorEvents(tab);

    // Create tab UI
    this.createTabUI(tab);

    // Switch to new tab
    this.switchToTab(tabId);
  }

  private setupEditorEvents(tab: EditorTab): void {
    // Content change event
    tab.model.onDidChangeContent(() => {
      if (!tab.isDirty) {
        tab.isDirty = true;
        this.updateTabUI(tab);
        this.emit('file-changed', tab.filePath, true);
      }
    });

    // Cursor position change event
    tab.editor.onDidChangeCursorPosition((e) => {
      if (tab.id === this.activeEditorId) {
        this.emit('cursor-changed', e.position.lineNumber, e.position.column);
      }
    });

    // Focus event
    tab.editor.onDidFocusEditorText(() => {
      if (tab.id !== this.activeEditorId) {
        this.switchToTab(tab.id);
      }
    });
  }

  private createTabUI(tab: EditorTab): void {
    const tabElement = document.createElement('div');
    tabElement.className = 'tab';
    tabElement.dataset.tabId = tab.id;

    tabElement.innerHTML = `
      <span class="tab-label">${tab.fileName}</span>
      <button class="tab-close" title="Close">×</button>
    `;

    // Tab click event
    tabElement.addEventListener('click', (e) => {
      if (!(e.target as HTMLElement).classList.contains('tab-close')) {
        this.switchToTab(tab.id);
      }
    });

    // Close button event
    const closeButton = tabElement.querySelector('.tab-close') as HTMLElement;
    closeButton.addEventListener('click', (e) => {
      e.stopPropagation();
      this.closeTab(tab.id);
    });

    this.tabBar!.appendChild(tabElement);
  }

  private updateTabUI(tab: EditorTab): void {
    const tabElement = this.tabBar!.querySelector(`[data-tab-id="${tab.id}"]`) as HTMLElement;
    if (tabElement) {
      const label = tabElement.querySelector('.tab-label') as HTMLElement;
      label.textContent = tab.isDirty ? `● ${tab.fileName}` : tab.fileName;
      tabElement.classList.toggle('dirty', tab.isDirty);
    }
  }

  public switchToTab(tabId: string): void {
    const tab = this.editors.get(tabId);
    if (!tab) return;

    // Hide current editor
    if (this.activeEditorId) {
      const currentTab = this.editors.get(this.activeEditorId);
      if (currentTab) {
        const currentEditor = document.getElementById(`editor-${this.activeEditorId}`);
        if (currentEditor) {
          currentEditor.style.display = 'none';
        }
      }
    }

    // Show new editor
    const editorElement = document.getElementById(`editor-${tabId}`);
    if (editorElement) {
      editorElement.style.display = 'block';
      tab.editor.layout();
      tab.editor.focus();
    }

    // Update active tab
    this.activeEditorId = tabId;

    // Update tab UI
    this.updateActiveTabUI();

    // Emit cursor position
    const position = tab.editor.getPosition();
    if (position) {
      this.emit('cursor-changed', position.lineNumber, position.column);
    }
  }

  private updateActiveTabUI(): void {
    // Remove active class from all tabs
    this.tabBar!.querySelectorAll('.tab').forEach(tab => {
      tab.classList.remove('active');
    });

    // Add active class to current tab
    if (this.activeEditorId) {
      const activeTab = this.tabBar!.querySelector(`[data-tab-id="${this.activeEditorId}"]`);
      if (activeTab) {
        activeTab.classList.add('active');
      }
    }
  }

  public async closeTab(tabId: string): Promise<void> {
    const tab = this.editors.get(tabId);
    if (!tab) return;

    // Check if file has unsaved changes
    if (tab.isDirty) {
      const result = await window.electronAPI.dialog.showMessageBox({
        type: 'warning',
        buttons: ['Save', "Don't Save", 'Cancel'],
        defaultId: 0,
        message: `Do you want to save the changes to ${tab.fileName}?`,
        detail: 'Your changes will be lost if you don\'t save them.'
      });

      if (result.response === 0) { // Save
        await this.saveTab(tab);
      } else if (result.response === 2) { // Cancel
        return;
      }
    }

    // Remove tab UI
    const tabElement = this.tabBar!.querySelector(`[data-tab-id="${tabId}"]`);
    if (tabElement) {
      tabElement.remove();
    }

    // Remove editor
    const editorElement = document.getElementById(`editor-${tabId}`);
    if (editorElement) {
      editorElement.remove();
    }

    // Dispose Monaco resources
    tab.model.dispose();
    tab.editor.dispose();

    // Remove from editors map
    this.editors.delete(tabId);

    // Switch to another tab if this was active
    if (this.activeEditorId === tabId) {
      const remainingTabs = Array.from(this.editors.keys());
      if (remainingTabs.length > 0) {
        this.switchToTab(remainingTabs[remainingTabs.length - 1]);
      } else {
        this.activeEditorId = null;
        // Show welcome screen
        const welcomeScreen = document.getElementById('welcome-screen');
        if (welcomeScreen) {
          welcomeScreen.style.display = 'flex';
        }
      }
    }
  }

  public async saveCurrentFile(): Promise<void> {
    if (!this.activeEditorId) return;

    const tab = this.editors.get(this.activeEditorId);
    if (tab) {
      await this.saveTab(tab);
    }
  }

  private async saveTab(tab: EditorTab): Promise<void> {
    try {
      let filePath = tab.filePath;

      // Handle untitled files
      if (filePath.startsWith('untitled:')) {
        const result = await window.electronAPI.dialog.showSaveDialog({
          defaultPath: tab.fileName,
          filters: [
            { name: 'All Files', extensions: ['*'] },
            { name: 'Text Files', extensions: ['txt'] },
            { name: 'JavaScript', extensions: ['js'] },
            { name: 'TypeScript', extensions: ['ts'] },
            { name: 'HTML', extensions: ['html'] },
            { name: 'CSS', extensions: ['css'] }
          ]
        });

        if (result.canceled) return;
        filePath = result.filePath!;
        
        // Update tab info
        tab.filePath = filePath;
        tab.fileName = this.getFileName(filePath);
      }

      // Save file
      const content = tab.model.getValue();
      await window.electronAPI.fs.writeFile(filePath, content);

      // Update tab state
      tab.isDirty = false;
      this.updateTabUI(tab);
      this.emit('file-changed', tab.filePath, false);

      console.log('File saved:', filePath);

    } catch (error) {
      console.error('Failed to save file:', error);
      throw error;
    }
  }

  private getFileName(filePath: string): string {
    if (filePath.startsWith('untitled:')) {
      return filePath.substring(9);
    }
    return filePath.split(/[/\\]/).pop() || 'Unknown';
  }

  private getLanguageFromPath(filePath: string): string {
    const extension = filePath.split('.').pop()?.toLowerCase();
    
    const languageMap: { [key: string]: string } = {
      'js': 'javascript',
      'ts': 'typescript',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'less': 'less',
      'json': 'json',
      'md': 'markdown',
      'py': 'python',
      'php': 'php',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'cs': 'csharp',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml'
    };

    return languageMap[extension || ''] || 'plaintext';
  }

  public getActiveEditor(): monaco.editor.IStandaloneCodeEditor | null {
    if (!this.activeEditorId) return null;
    
    const tab = this.editors.get(this.activeEditorId);
    return tab ? tab.editor : null;
  }

  public getActiveFilePath(): string | null {
    if (!this.activeEditorId) return null;
    
    const tab = this.editors.get(this.activeEditorId);
    return tab ? tab.filePath : null;
  }
}
