#!/usr/bin/env node

/**
 * Quick Start Script for GOC IDE
 * Minimal setup to get the IDE running quickly
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 GOC IDE Quick Start');
console.log('======================\n');

function runCommand(command, description) {
  console.log(`📦 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} completed\n`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    throw error;
  }
}

function createMinimalFiles() {
  console.log('📝 Creating minimal files...');
  
  // Create dist directory
  if (!fs.existsSync('dist')) {
    fs.mkdirSync('dist', { recursive: true });
  }
  
  // Create minimal main.js
  const mainJs = `
const { app, BrowserWindow } = require('electron');
const path = require('path');

function createWindow() {
  const win = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  win.loadFile(path.join(__dirname, 'renderer', 'index.html'));
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
`;

  // Create renderer directory
  if (!fs.existsSync('dist/renderer')) {
    fs.mkdirSync('dist/renderer', { recursive: true });
  }

  // Create minimal HTML
  const indexHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>GOC IDE</title>
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: #1e1e1e;
            color: #cccccc;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        h1 {
            color: #007acc;
            font-size: 48px;
            margin-bottom: 20px;
        }
        p {
            font-size: 18px;
            margin-bottom: 30px;
        }
        .status {
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .success {
            color: #4ec9b0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 GOC IDE</h1>
        <p>Professional AI-powered code editor</p>
        
        <div class="status">
            <h2 class="success">✅ IDE Successfully Launched!</h2>
            <p>Your standalone GOC IDE is now running.</p>
            <p>This is the minimal version - the full IDE with Monaco Editor and GOC Agent integration is ready to be built.</p>
        </div>
        
        <div class="status">
            <h3>🎯 What's Working:</h3>
            <ul style="text-align: left; display: inline-block;">
                <li>✅ Electron desktop application</li>
                <li>✅ Professional window management</li>
                <li>✅ Cross-platform compatibility</li>
                <li>✅ Ready for full feature integration</li>
            </ul>
        </div>
        
        <div class="status">
            <h3>🔧 Next Steps:</h3>
            <p>Run <code>npm run build</code> to build the full IDE with all features</p>
        </div>
    </div>
</body>
</html>
`;

  fs.writeFileSync('dist/main.js', mainJs);
  fs.writeFileSync('dist/renderer/index.html', indexHtml);
  
  console.log('✅ Minimal files created\n');
}

async function main() {
  try {
    // Create minimal files first
    createMinimalFiles();
    
    // Install only essential dependencies
    console.log('📦 Installing essential dependencies...');
    runCommand('npm install electron --save-dev --ignore-scripts', 'Installing Electron');
    
    // Start the IDE
    console.log('🚀 Starting GOC IDE...');
    runCommand('npx electron .', 'Launching GOC IDE');
    
  } catch (error) {
    console.error('\n❌ Quick start failed:', error.message);
    console.log('\n💡 Try running: npm install --ignore-scripts');
    process.exit(1);
  }
}

main();
