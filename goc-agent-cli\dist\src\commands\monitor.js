"use strict";
/**
 * Monitoring Command
 *
 * Performance monitoring, usage analytics, and system health tracking
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonitorCommand = void 0;
const chalk_1 = __importDefault(require("chalk"));
const fs_1 = __importDefault(require("fs"));
class MonitorCommand {
    constructor(core) {
        this.core = core;
    }
    register(program) {
        const monitorCmd = program
            .command('monitor')
            .description('System monitoring and analytics');
        monitorCmd
            .command('health')
            .description('Check system health')
            .action(async () => {
            await this.checkHealth();
        });
        monitorCmd
            .command('performance')
            .description('Show performance metrics')
            .option('--hours <hours>', 'Time range in hours', '24')
            .action(async (options) => {
            await this.showPerformance(options);
        });
        monitorCmd
            .command('usage')
            .description('Show usage analytics')
            .option('--hours <hours>', 'Time range in hours', '24')
            .action(async (options) => {
            await this.showUsage(options);
        });
        monitorCmd
            .command('metrics')
            .description('Show detailed metrics')
            .option('--type <type>', 'Metric type (performance|usage|error|feature|system)')
            .option('--name <name>', 'Metric name filter')
            .option('--hours <hours>', 'Time range in hours', '24')
            .action(async (options) => {
            await this.showMetrics(options);
        });
        monitorCmd
            .command('export')
            .description('Export metrics data')
            .option('--format <format>', 'Export format (json|csv)', 'json')
            .option('--output <file>', 'Output file path')
            .action(async (options) => {
            await this.exportMetrics(options);
        });
        monitorCmd
            .command('record <type> <name> <value>')
            .description('Record a custom metric')
            .option('--unit <unit>', 'Metric unit', 'count')
            .option('--tags <tags>', 'Tags as JSON string')
            .action(async (type, name, value, options) => {
            await this.recordMetric(type, name, value, options);
        });
        monitorCmd
            .command('status')
            .description('Show monitoring system status')
            .action(async () => {
            await this.showStatus();
        });
    }
    async checkHealth() {
        try {
            console.log(chalk_1.default.blue('🏥 System Health Check'));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            const monitoring = this.core.getMonitoringSystem();
            const health = await monitoring.getSystemHealth();
            // Overall status
            const statusColor = health.status === 'healthy' ? chalk_1.default.green :
                health.status === 'degraded' ? chalk_1.default.yellow : chalk_1.default.red;
            console.log(chalk_1.default.blue('📊 Overall Status:'));
            console.log(`Status: ${statusColor(health.status.toUpperCase())}`);
            console.log(`Uptime: ${this.formatDuration(health.uptime)}`);
            console.log(`Version: ${health.version}`);
            console.log();
            // Component status
            console.log(chalk_1.default.blue('🔧 Components:'));
            Object.entries(health.components).forEach(([name, component]) => {
                const statusIcon = component.status === 'up' ? '✅' :
                    component.status === 'degraded' ? '⚠️' : '❌';
                console.log(`${statusIcon} ${name}: ${component.status}`);
                if (component.responseTime) {
                    console.log(chalk_1.default.dim(`   Response Time: ${component.responseTime}ms`));
                }
                if (component.error) {
                    console.log(chalk_1.default.red(`   Error: ${component.error}`));
                }
                console.log(chalk_1.default.dim(`   Last Check: ${component.lastCheck.toLocaleString()}`));
            });
            console.log();
            // Alerts
            if (health.alerts.length > 0) {
                console.log(chalk_1.default.blue('🚨 Active Alerts:'));
                health.alerts.forEach((alert, index) => {
                    const levelColor = alert.level === 'critical' ? chalk_1.default.red :
                        alert.level === 'error' ? chalk_1.default.red :
                            alert.level === 'warning' ? chalk_1.default.yellow : chalk_1.default.blue;
                    console.log(`${index + 1}. ${levelColor(alert.level.toUpperCase())}: ${alert.message}`);
                    console.log(chalk_1.default.dim(`   Time: ${alert.timestamp.toLocaleString()}`));
                    if (alert.component) {
                        console.log(chalk_1.default.dim(`   Component: ${alert.component}`));
                    }
                });
            }
            else {
                console.log(chalk_1.default.green('✅ No active alerts'));
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Health check failed:'), error);
        }
    }
    async showPerformance(options) {
        try {
            console.log(chalk_1.default.blue('📈 Performance Metrics'));
            console.log(chalk_1.default.dim(`Time Range: Last ${options.hours} hours`));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            const monitoring = this.core.getMonitoringSystem();
            const timeRange = {
                start: new Date(Date.now() - parseInt(options.hours) * 60 * 60 * 1000),
                end: new Date()
            };
            const analytics = monitoring.getPerformanceAnalytics(timeRange);
            console.log(chalk_1.default.blue('📊 Summary:'));
            console.log(`Average Response Time: ${analytics.averageResponseTime.toFixed(2)}ms`);
            console.log(`Throughput: ${analytics.throughput.toFixed(2)} req/s`);
            console.log(`Error Rate: ${(analytics.errorRate * 100).toFixed(2)}%`);
            console.log();
            if (analytics.trends.length > 0) {
                console.log(chalk_1.default.blue('📈 Recent Trends:'));
                console.log('Time'.padEnd(20) + 'Response Time'.padEnd(15) + 'Throughput'.padEnd(15) + 'Error Rate');
                console.log('─'.repeat(65));
                analytics.trends.slice(-10).forEach(trend => {
                    const time = trend.timestamp.toLocaleTimeString();
                    const responseTime = `${trend.responseTime.toFixed(0)}ms`;
                    const throughput = `${trend.throughput.toFixed(1)} req/s`;
                    const errorRate = `${(trend.errorRate * 100).toFixed(1)}%`;
                    console.log(time.padEnd(20) +
                        responseTime.padEnd(15) +
                        throughput.padEnd(15) +
                        errorRate);
                });
            }
            else {
                console.log(chalk_1.default.yellow('No performance data available for the specified time range'));
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Failed to show performance metrics:'), error);
        }
    }
    async showUsage(options) {
        try {
            console.log(chalk_1.default.blue('📊 Usage Analytics'));
            console.log(chalk_1.default.dim(`Time Range: Last ${options.hours} hours`));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            const monitoring = this.core.getMonitoringSystem();
            const timeRange = {
                start: new Date(Date.now() - parseInt(options.hours) * 60 * 60 * 1000),
                end: new Date()
            };
            const usage = monitoring.getUsageAnalytics(timeRange);
            console.log(chalk_1.default.blue('📈 Summary:'));
            console.log(`Total Requests: ${usage.totalRequests}`);
            console.log(`Unique Users: ${usage.uniqueUsers}`);
            console.log(`Average Session Duration: ${usage.averageSessionDuration.toFixed(1)}s`);
            console.log();
            if (Object.keys(usage.featuresUsed).length > 0) {
                console.log(chalk_1.default.blue('🔧 Features Used:'));
                const sortedFeatures = Object.entries(usage.featuresUsed)
                    .sort(([, a], [, b]) => b - a)
                    .slice(0, 10);
                sortedFeatures.forEach(([feature, count]) => {
                    const percentage = ((count / usage.totalRequests) * 100).toFixed(1);
                    console.log(`${feature.padEnd(20)} ${count.toString().padStart(6)} (${percentage}%)`);
                });
                console.log();
            }
            if (usage.topQueries.length > 0) {
                console.log(chalk_1.default.blue('🔍 Top Queries:'));
                usage.topQueries.forEach((query, index) => {
                    console.log(`${index + 1}. "${query.query}" (${query.count} times)`);
                });
                console.log();
            }
            if (usage.topErrors.length > 0) {
                console.log(chalk_1.default.blue('❌ Top Errors:'));
                usage.topErrors.forEach((error, index) => {
                    console.log(`${index + 1}. ${error.error} (${error.count} times)`);
                });
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Failed to show usage analytics:'), error);
        }
    }
    async showMetrics(options) {
        try {
            console.log(chalk_1.default.blue('📊 Detailed Metrics'));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            const monitoring = this.core.getMonitoringSystem();
            const timeRange = options.hours ? {
                start: new Date(Date.now() - parseInt(options.hours) * 60 * 60 * 1000),
                end: new Date()
            } : undefined;
            const metrics = monitoring.getMetrics(options.type, options.name, timeRange);
            if (metrics.length === 0) {
                console.log(chalk_1.default.yellow('No metrics found matching the criteria'));
                return;
            }
            console.log(chalk_1.default.blue(`📈 Found ${metrics.length} metrics:`));
            console.log();
            // Group by type
            const groupedMetrics = metrics.reduce((groups, metric) => {
                if (!groups[metric.type])
                    groups[metric.type] = [];
                groups[metric.type].push(metric);
                return groups;
            }, {});
            Object.entries(groupedMetrics).forEach(([type, typeMetrics]) => {
                console.log(chalk_1.default.bold(`${type.toUpperCase()}:`));
                typeMetrics.slice(0, 10).forEach(metric => {
                    console.log(`  ${metric.name}: ${metric.value} ${metric.unit}`);
                    console.log(chalk_1.default.dim(`    Time: ${metric.timestamp.toLocaleString()}`));
                    if (Object.keys(metric.tags).length > 0) {
                        const tags = Object.entries(metric.tags)
                            .map(([key, value]) => `${key}=${value}`)
                            .join(', ');
                        console.log(chalk_1.default.dim(`    Tags: ${tags}`));
                    }
                });
                if (typeMetrics.length > 10) {
                    console.log(chalk_1.default.dim(`    ... and ${typeMetrics.length - 10} more`));
                }
                console.log();
            });
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Failed to show metrics:'), error);
        }
    }
    async exportMetrics(options) {
        try {
            console.log(chalk_1.default.blue('📤 Exporting Metrics'));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            const monitoring = this.core.getMonitoringSystem();
            const data = monitoring.exportMetrics(options.format);
            if (options.output) {
                fs_1.default.writeFileSync(options.output, data, 'utf-8');
                console.log(chalk_1.default.green(`✅ Metrics exported to: ${options.output}`));
            }
            else {
                const filename = `metrics_${Date.now()}.${options.format}`;
                fs_1.default.writeFileSync(filename, data, 'utf-8');
                console.log(chalk_1.default.green(`✅ Metrics exported to: ${filename}`));
            }
            console.log(chalk_1.default.dim(`Format: ${options.format.toUpperCase()}`));
            console.log(chalk_1.default.dim(`Size: ${data.length} characters`));
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Failed to export metrics:'), error);
        }
    }
    async recordMetric(type, name, value, options) {
        try {
            console.log(chalk_1.default.blue('📝 Recording Custom Metric'));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            const monitoring = this.core.getMonitoringSystem();
            let tags = {};
            if (options.tags) {
                try {
                    tags = JSON.parse(options.tags);
                }
                catch {
                    console.log(chalk_1.default.yellow('⚠️ Invalid tags JSON, using empty tags'));
                }
            }
            monitoring.recordMetric({
                type: type,
                name,
                value: parseFloat(value),
                unit: options.unit,
                tags
            });
            console.log(chalk_1.default.green('✅ Metric recorded successfully'));
            console.log(`Type: ${type}`);
            console.log(`Name: ${name}`);
            console.log(`Value: ${value} ${options.unit}`);
            if (Object.keys(tags).length > 0) {
                console.log(`Tags: ${JSON.stringify(tags)}`);
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Failed to record metric:'), error);
        }
    }
    async showStatus() {
        try {
            console.log(chalk_1.default.blue('📊 Monitoring System Status'));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            const monitoring = this.core.getMonitoringSystem();
            // Get basic metrics count
            const allMetrics = monitoring.getMetrics();
            const metricsByType = allMetrics.reduce((counts, metric) => {
                counts[metric.type] = (counts[metric.type] || 0) + 1;
                return counts;
            }, {});
            console.log(chalk_1.default.blue('📈 Metrics Overview:'));
            console.log(`Total Metrics: ${allMetrics.length}`);
            Object.entries(metricsByType).forEach(([type, count]) => {
                console.log(`${type}: ${count}`);
            });
            console.log();
            // Show recent activity
            const recentMetrics = allMetrics
                .filter(m => m.timestamp > new Date(Date.now() - 60 * 60 * 1000)) // Last hour
                .length;
            console.log(chalk_1.default.blue('⏱️ Recent Activity:'));
            console.log(`Metrics in last hour: ${recentMetrics}`);
            console.log();
            console.log(chalk_1.default.blue('💡 Available Commands:'));
            console.log('• goc monitor health - Check system health');
            console.log('• goc monitor performance - View performance metrics');
            console.log('• goc monitor usage - View usage analytics');
            console.log('• goc monitor export - Export metrics data');
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Failed to show monitoring status:'), error);
        }
    }
    formatDuration(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        if (days > 0) {
            return `${days}d ${hours % 24}h ${minutes % 60}m`;
        }
        else if (hours > 0) {
            return `${hours}h ${minutes % 60}m`;
        }
        else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        }
        else {
            return `${seconds}s`;
        }
    }
}
exports.MonitorCommand = MonitorCommand;
//# sourceMappingURL=monitor.js.map