/**
 * Process Management Tool
 *
 * Implements launch-process, read-process, write-process, kill-process with monitoring
 */
import { EventEmitter } from 'events';
import { ToolResult, ProcessOptions } from './index';
export interface ProcessInfo {
    id: string;
    command: string;
    args: string[];
    pid?: number;
    status: 'running' | 'completed' | 'failed' | 'killed';
    startTime: Date;
    endTime?: Date;
    exitCode?: number;
    output: string;
    errorOutput: string;
}
export interface LaunchOptions extends ProcessOptions {
    wait?: boolean;
    maxWaitSeconds?: number;
    streamOutput?: boolean;
}
export declare class ProcessManager extends EventEmitter {
    private processes;
    private childProcesses;
    private processCounter;
    /**
     * Launch a new process
     */
    launchProcess(command: string, args?: string[], options?: LaunchOptions): Promise<ToolResult>;
    /**
     * Read output from a process
     */
    readProcess(processId: string, options?: {
        wait?: boolean;
        maxWaitSeconds?: number;
    }): Promise<ToolResult>;
    /**
     * Write input to a process
     */
    writeProcess(processId: string, input: string): Promise<ToolResult>;
    /**
     * Kill a process
     */
    killProcess(processId: string, signal?: string): Promise<ToolResult>;
    /**
     * List all processes
     */
    listProcesses(): ToolResult;
    /**
     * Get process info
     */
    getProcessInfo(processId: string): ToolResult;
    /**
     * Wait for process to complete
     */
    private waitForProcess;
    /**
     * Cleanup completed processes
     */
    cleanup(): void;
}
//# sourceMappingURL=process-manager.d.ts.map