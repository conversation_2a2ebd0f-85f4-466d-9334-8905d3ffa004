/**
 * Context Command
 *
 * Intelligent context engine commands using GOC Core
 */
import { Command } from 'commander';
import { GocCore } from '@goc-agent/core';
export declare class ContextCommand {
    private core;
    constructor(core: GocCore);
    register(program: Command): void;
    private indexProject;
    private searchCode;
    private analyzeCode;
    private getFileContext;
    private analyzeRelationships;
    private showProjectStats;
    private showSymbols;
}
//# sourceMappingURL=context.d.ts.map