export interface CLIConfig {
    defaultProvider?: string;
    defaultModel?: string;
    apiKeys?: {
        [provider: string]: string;
    };
    backend?: {
        url: string;
        apiKey?: string;
    };
    preferences?: {
        theme?: string;
        verbose?: boolean;
        autoSave?: boolean;
    };
}
export declare class ConfigManager {
    private configPath;
    private config;
    constructor(configPath?: string);
    private getDefaultConfigPath;
    loadConfig(): Promise<CLIConfig>;
    saveConfig(config: CLIConfig): Promise<void>;
    getConfig(): CLIConfig;
    updateConfig(updates: Partial<CLIConfig>): void;
    getApiKey(provider: string): string | undefined;
    setApiKey(provider: string, apiKey: string): void;
    getBackendConfig(): {
        url: string;
        apiKey?: string;
    } | undefined;
    setBackendConfig(url: string, apiKey?: string): void;
    getDefaultProvider(): string | undefined;
    setDefaultProvider(provider: string): void;
    getDefaultModel(): string | undefined;
    setDefaultModel(model: string): void;
    isVerbose(): boolean;
    setVerbose(verbose: boolean): void;
}
//# sourceMappingURL=ConfigManager.d.ts.map