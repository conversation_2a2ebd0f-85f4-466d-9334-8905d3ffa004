/**
 * Context Enhancer
 *
 * Enhances AI prompts and messages with learned patterns and context
 * Works with all AI providers (OpenAI, Groq, Gemini, Ollama, etc.)
 */
import { AIMessage, LearningPattern, UserPreference, GocConfig } from '../types';
export interface EnhancementContext {
    userId?: string;
    projectId?: string;
    language?: string;
    framework?: string;
    currentFile?: string;
    workspaceRoot?: string;
}
export interface EnhancementResult {
    enhancedMessages: AIMessage[];
    appliedPatterns: LearningPattern[];
    appliedPreferences: UserPreference[];
    enhancementSummary: string;
}
export declare class ContextEnhancer {
    private config;
    private knowledgeBase;
    private initialized;
    constructor(config: GocConfig);
    initialize(): Promise<void>;
    /**
     * Enhance AI messages with learned context and patterns
     */
    enhanceMessages(messages: AIMessage[], context?: EnhancementContext): Promise<AIMessage[]>;
    /**
     * Get detailed enhancement information
     */
    enhanceWithDetails(messages: AIMessage[], context?: EnhancementContext): Promise<EnhancementResult>;
    /**
     * Build comprehensive enhancement
     */
    private buildEnhancement;
    /**
     * Find relevant patterns for the conversation
     */
    private findRelevantPatterns;
    /**
     * Build system message enhancement
     */
    private buildSystemEnhancement;
    /**
     * Enhance user message with context
     */
    private enhanceUserMessage;
    private extractKeywords;
    private detectConversationIntent;
    private getPatternsByIntent;
    private deduplicatePatterns;
    private buildStyleGuidelines;
    private buildArchitecturalGuidelines;
    private buildNamingGuidelines;
    private buildProjectContext;
    private buildFrequentPatternGuidelines;
    private buildSnippetContext;
    private buildWorkflowContext;
    private isCodeRequest;
    private mergeSystemMessages;
    private buildEnhancementSummary;
    dispose(): Promise<void>;
}
//# sourceMappingURL=context-enhancer.d.ts.map