/**
 * Terminal Manager - Handles terminal creation and management
 */

import * as pty from 'node-pty';
import * as os from 'os';

interface Terminal {
  id: string;
  ptyProcess: pty.IPty;
  cwd: string;
}

export class TerminalManager {
  private terminals: Map<string, Terminal> = new Map();
  private nextTerminalId = 1;

  public async createTerminal(cwd?: string): Promise<string> {
    const terminalId = `terminal-${this.nextTerminalId++}`;
    const workingDirectory = cwd || os.homedir();

    try {
      // Determine shell based on platform
      const shell = this.getDefaultShell();

      // Create PTY process
      const ptyProcess = pty.spawn(shell, [], {
        name: 'xterm-color',
        cols: 80,
        rows: 24,
        cwd: workingDirectory,
        env: process.env
      });

      // Store terminal
      const terminal: Terminal = {
        id: terminalId,
        ptyProcess,
        cwd: workingDirectory
      };

      this.terminals.set(terminalId, terminal);

      // Setup event handlers
      ptyProcess.onData((data) => {
        // Send data to renderer process
        // This would typically be sent via IPC to the renderer
        console.log(`Terminal ${terminalId} output:`, data);
      });

      ptyProcess.onExit((exitCode) => {
        console.log(`Terminal ${terminalId} exited with code:`, exitCode);
        this.terminals.delete(terminalId);
      });

      console.log(`Terminal ${terminalId} created in ${workingDirectory}`);
      return terminalId;

    } catch (error) {
      console.error('Failed to create terminal:', error);
      throw new Error(`Failed to create terminal: ${error.message}`);
    }
  }

  public async writeToTerminal(terminalId: string, data: string): Promise<void> {
    const terminal = this.terminals.get(terminalId);
    if (!terminal) {
      throw new Error(`Terminal ${terminalId} not found`);
    }

    try {
      terminal.ptyProcess.write(data);
    } catch (error) {
      console.error('Failed to write to terminal:', error);
      throw new Error(`Failed to write to terminal: ${error.message}`);
    }
  }

  public async resizeTerminal(terminalId: string, cols: number, rows: number): Promise<void> {
    const terminal = this.terminals.get(terminalId);
    if (!terminal) {
      throw new Error(`Terminal ${terminalId} not found`);
    }

    try {
      terminal.ptyProcess.resize(cols, rows);
    } catch (error) {
      console.error('Failed to resize terminal:', error);
      throw new Error(`Failed to resize terminal: ${error.message}`);
    }
  }

  public async killTerminal(terminalId: string): Promise<void> {
    const terminal = this.terminals.get(terminalId);
    if (!terminal) {
      throw new Error(`Terminal ${terminalId} not found`);
    }

    try {
      terminal.ptyProcess.kill();
      this.terminals.delete(terminalId);
      console.log(`Terminal ${terminalId} killed`);
    } catch (error) {
      console.error('Failed to kill terminal:', error);
      throw new Error(`Failed to kill terminal: ${error.message}`);
    }
  }

  private getDefaultShell(): string {
    const platform = os.platform();
    
    switch (platform) {
      case 'win32':
        return process.env.COMSPEC || 'cmd.exe';
      case 'darwin':
        return process.env.SHELL || '/bin/zsh';
      default: // Linux and others
        return process.env.SHELL || '/bin/bash';
    }
  }

  public getTerminalList(): string[] {
    return Array.from(this.terminals.keys());
  }

  public dispose(): void {
    // Kill all terminals
    this.terminals.forEach((terminal) => {
      try {
        terminal.ptyProcess.kill();
      } catch (error) {
        console.error('Error killing terminal:', error);
      }
    });

    this.terminals.clear();
    console.log('Terminal Manager disposed');
  }
}
