"use strict";
/**
 * Model Selection Commands
 *
 * Interactive commands for selecting between Ollama and GOC Agent models
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelsCommand = void 0;
const chalk_1 = __importDefault(require("chalk"));
const inquirer_1 = __importDefault(require("inquirer"));
const ConfigManager_1 = require("../services/ConfigManager");
const BackendAPIClient_1 = require("../services/BackendAPIClient");
class ModelsCommand {
    constructor() {
        this.configManager = new ConfigManager_1.ConfigManager();
        this.apiClient = new BackendAPIClient_1.BackendAPIClient();
    }
    register(program) {
        const modelsCmd = program
            .command('models')
            .description('Manage AI model selection');
        modelsCmd
            .command('select')
            .description('Select AI provider and model')
            .action(async () => {
            await this.selectModel();
        });
        modelsCmd
            .command('list')
            .description('List available models')
            .action(async () => {
            await this.listModels();
        });
        modelsCmd
            .command('status')
            .description('Show current model selection')
            .action(async () => {
            await this.showStatus();
        });
        modelsCmd
            .command('test')
            .description('Test current model connection')
            .action(async () => {
            await this.testModel();
        });
    }
    async selectModel() {
        console.log(chalk_1.default.blue('🤖 GOC Agent Model Selection\n'));
        try {
            // Step 1: Select Provider
            const provider = await this.selectProvider();
            if (!provider)
                return;
            // Step 2: Select Model based on provider
            if (provider === 'ollama') {
                await this.selectOllamaModel();
            }
            else if (provider === 'goc') {
                await this.selectGocAgentModel();
            }
            console.log(chalk_1.default.green('\n✅ Model selection completed!'));
            await this.showStatus();
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Model selection failed:'), error);
        }
    }
    async selectProvider() {
        const { provider } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'provider',
                message: 'Choose AI provider:',
                choices: [
                    {
                        name: 'Ollama (Local) - Free local AI models',
                        value: 'ollama',
                        short: 'Ollama'
                    },
                    {
                        name: 'GOC Agent Model - Hosted AI models (registration required)',
                        value: 'goc',
                        short: 'GOC Agent'
                    }
                ]
            }
        ]);
        await this.configManager.setProvider(provider);
        if (provider === 'ollama') {
            console.log(chalk_1.default.dim('Selected: Ollama (Local models)'));
        }
        else {
            console.log(chalk_1.default.dim('Selected: GOC Agent Model (Hosted models)'));
        }
        return provider;
    }
    async selectOllamaModel() {
        console.log(chalk_1.default.yellow('\n🔍 Checking Ollama installation...'));
        const isAvailable = await this.checkOllamaAvailability();
        if (!isAvailable) {
            await this.showOllamaSetup();
            return;
        }
        const models = await this.getOllamaModels();
        if (models.length === 0) {
            await this.showOllamaModelSetup();
            return;
        }
        const { model } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'model',
                message: 'Select Ollama model:',
                choices: models.map(model => ({
                    name: `${this.formatOllamaModelName(model)} - ${this.getOllamaModelDescription(model)}`,
                    value: model,
                    short: this.formatOllamaModelName(model)
                }))
            }
        ]);
        await this.configManager.setModel(model);
        console.log(chalk_1.default.green(`✅ Selected: ${this.formatOllamaModelName(model)}`));
    }
    async selectGocAgentModel() {
        console.log(chalk_1.default.yellow('\n☁️  GOC Agent Model Selection'));
        const { model } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'model',
                message: 'Select GOC Agent model tier:',
                choices: [
                    {
                        name: 'Free Tier - 50 requests/month forever (Perfect for trying GOC Agent)',
                        value: 'goc-agent-cloud',
                        short: 'Free Tier'
                    },
                    {
                        name: 'Developer Tier - $29/month for 500 requests (Full-featured)',
                        value: 'goc-agent-dev',
                        short: 'Developer Tier'
                    }
                ]
            }
        ]);
        await this.configManager.setModel(model);
        const tierName = model === 'goc-agent-cloud' ? 'Free Tier' : 'Developer Tier';
        console.log(chalk_1.default.green(`✅ Selected: GOC Agent Model (${tierName})`));
        // Check authentication
        if (!this.apiClient.isAuthenticated()) {
            await this.handleGocAgentAuth();
        }
    }
    async handleGocAgentAuth() {
        console.log(chalk_1.default.yellow('\n🔐 Authentication Required'));
        console.log('GOC Agent models require registration and authentication.');
        const { action } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'action',
                message: 'What would you like to do?',
                choices: [
                    { name: 'Register new account', value: 'register' },
                    { name: 'Login with existing account', value: 'login' },
                    { name: 'Skip for now', value: 'skip' }
                ]
            }
        ]);
        if (action === 'register') {
            console.log(chalk_1.default.blue('\n📝 Opening registration page...'));
            console.log('Visit: https://goc-agent.com/register');
            console.log('After registration, use "goc auth login" to authenticate.');
        }
        else if (action === 'login') {
            console.log(chalk_1.default.blue('\n🔑 Use "goc auth login" to authenticate.'));
        }
        else {
            console.log(chalk_1.default.dim('\n⚠️  You can authenticate later using "goc auth login"'));
        }
    }
    async checkOllamaAvailability() {
        try {
            const response = await fetch('http://localhost:11434/api/tags', {
                method: 'GET',
                signal: AbortSignal.timeout(5000)
            });
            return response.ok;
        }
        catch (error) {
            return false;
        }
    }
    async getOllamaModels() {
        try {
            const response = await fetch('http://localhost:11434/api/tags');
            if (!response.ok)
                return [];
            const data = await response.json();
            return (data.models || []).map((model) => model.name);
        }
        catch (error) {
            return [];
        }
    }
    async showOllamaSetup() {
        console.log(chalk_1.default.red('\n❌ Ollama not found'));
        console.log('To use local AI models, you need to install Ollama:');
        console.log(chalk_1.default.cyan('1. Visit: https://ollama.ai'));
        console.log(chalk_1.default.cyan('2. Download and install Ollama'));
        console.log(chalk_1.default.cyan('3. Pull a model: ollama pull llama3.2:3b'));
        console.log(chalk_1.default.cyan('4. Run: goc models select'));
    }
    async showOllamaModelSetup() {
        console.log(chalk_1.default.yellow('\n⚠️  No Ollama models found'));
        console.log('Install recommended models:');
        console.log(chalk_1.default.cyan('ollama pull llama3.2:3b      # Recommended for most tasks'));
        console.log(chalk_1.default.cyan('ollama pull codellama:7b     # Specialized for coding'));
        console.log(chalk_1.default.cyan('ollama pull mistral:7b       # General purpose'));
    }
    formatOllamaModelName(modelId) {
        const nameMap = {
            'llama3.2:3b': 'Llama 3.2 3B',
            'llama3.2:1b': 'Llama 3.2 1B',
            'codellama:7b': 'Code Llama 7B',
            'codellama:13b': 'Code Llama 13B',
            'mistral:7b': 'Mistral 7B',
            'deepseek-coder:6.7b': 'DeepSeek Coder 6.7B',
            'qwen2.5-coder:7b': 'Qwen2.5 Coder 7B'
        };
        return nameMap[modelId] || modelId;
    }
    getOllamaModelDescription(modelId) {
        const descMap = {
            'llama3.2:3b': 'Recommended for most tasks',
            'llama3.2:1b': 'Lightweight and fast',
            'codellama:7b': 'Specialized for coding',
            'codellama:13b': 'Advanced coding model',
            'mistral:7b': 'General purpose',
            'deepseek-coder:6.7b': 'Optimized for code',
            'qwen2.5-coder:7b': 'Latest coding model'
        };
        return descMap[modelId] || 'Local AI model';
    }
    async listModels() {
        console.log(chalk_1.default.blue('📋 Available Models\n'));
        // Show Ollama models
        console.log(chalk_1.default.green('🏠 Ollama (Local Models)'));
        const ollamaAvailable = await this.checkOllamaAvailability();
        if (ollamaAvailable) {
            const models = await this.getOllamaModels();
            if (models.length > 0) {
                models.forEach(model => {
                    console.log(`  • ${this.formatOllamaModelName(model)} - ${this.getOllamaModelDescription(model)}`);
                });
            }
            else {
                console.log(chalk_1.default.dim('  No models installed. Run "ollama pull <model>" to install.'));
            }
        }
        else {
            console.log(chalk_1.default.dim('  Ollama not installed. Visit https://ollama.ai'));
        }
        // Show GOC Agent models
        console.log(chalk_1.default.blue('\n☁️  GOC Agent Models'));
        console.log('  • Free Tier - 50 requests/month forever');
        console.log('  • Developer Tier - $29/month for 500 requests');
        console.log(chalk_1.default.dim('  Requires registration at https://goc-agent.com'));
    }
    async showStatus() {
        const config = await this.configManager.getConfig();
        const provider = config.defaultProvider;
        const model = config.providers[provider]?.defaultModel;
        console.log(chalk_1.default.blue('\n📊 Current Model Status'));
        console.log(`Provider: ${chalk_1.default.green(provider === 'ollama' ? 'Ollama (Local)' : 'GOC Agent Model')}`);
        if (model) {
            if (provider === 'ollama') {
                console.log(`Model: ${chalk_1.default.green(this.formatOllamaModelName(model))}`);
                // Check Ollama connection
                const isRunning = await this.checkOllamaAvailability();
                console.log(`Status: ${isRunning ? chalk_1.default.green('✅ Connected') : chalk_1.default.red('❌ Ollama not running')}`);
                if (isRunning) {
                    const models = await this.getOllamaModels();
                    const isInstalled = models.includes(model);
                    console.log(`Model Status: ${isInstalled ? chalk_1.default.green('✅ Installed') : chalk_1.default.red('❌ Not installed')}`);
                }
            }
            else {
                const tierName = model === 'goc-agent-cloud' ? 'Free Tier' : 'Developer Tier';
                console.log(`Model: ${chalk_1.default.green(`GOC Agent (${tierName})`)}`);
                // Show authentication status for GOC Agent
                const isAuth = this.apiClient.isAuthenticated();
                console.log(`Authentication: ${isAuth ? chalk_1.default.green('✅ Authenticated') : chalk_1.default.red('❌ Not authenticated')}`);
                if (isAuth) {
                    console.log(`Tier: ${chalk_1.default.blue(tierName)}`);
                    if (model === 'goc-agent-cloud') {
                        console.log(`Limits: ${chalk_1.default.dim('50 requests/month, 10 requests/day')}`);
                    }
                    else {
                        console.log(`Limits: ${chalk_1.default.dim('500 requests/month, 50 requests/day')}`);
                    }
                }
            }
        }
        else {
            console.log(`Model: ${chalk_1.default.red('Not selected')}`);
            console.log(chalk_1.default.yellow('💡 Run "goc models select" to choose a model'));
        }
        // Show quick setup tips
        if (provider === 'ollama') {
            const isRunning = await this.checkOllamaAvailability();
            if (!isRunning) {
                console.log(chalk_1.default.yellow('\n💡 Tips:'));
                console.log(chalk_1.default.dim('   • Install Ollama from https://ollama.ai'));
                console.log(chalk_1.default.dim('   • Start Ollama service'));
                console.log(chalk_1.default.dim('   • Pull models: ollama pull llama3.2:3b'));
            }
        }
        else if (provider === 'goc' && !this.apiClient.isAuthenticated()) {
            console.log(chalk_1.default.yellow('\n💡 Tips:'));
            console.log(chalk_1.default.dim('   • Register at https://goc-agent.com/register'));
            console.log(chalk_1.default.dim('   • Use "goc auth login" to authenticate'));
        }
    }
    async testModel() {
        console.log(chalk_1.default.blue('🧪 Testing model connection...\n'));
        const config = await this.configManager.getConfig();
        const provider = config.defaultProvider;
        if (provider === 'ollama') {
            const available = await this.checkOllamaAvailability();
            if (available) {
                console.log(chalk_1.default.green('✅ Ollama is running and accessible'));
            }
            else {
                console.log(chalk_1.default.red('❌ Ollama is not running or not accessible'));
            }
        }
        else if (provider === 'goc') {
            if (this.apiClient.isAuthenticated()) {
                console.log(chalk_1.default.green('✅ GOC Agent authentication valid'));
            }
            else {
                console.log(chalk_1.default.red('❌ GOC Agent authentication required'));
            }
        }
    }
}
exports.ModelsCommand = ModelsCommand;
//# sourceMappingURL=models.js.map